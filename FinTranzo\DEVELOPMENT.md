# FinTranzo 開發指南

## 🎯 下一步開發計劃

### 第一階段：Supabase 數據庫設計 (優先級：高)

1. **登錄 Supabase Console**
   - 訪問 https://supabase.com/
   - 創建新專案或使用現有專案
   - 獲取專案 URL 和 Anon Key

2. **創建數據庫表結構**
   ```sql
   -- 用戶資料表
   CREATE TABLE profiles (
     id UUID REFERENCES auth.users(id) PRIMARY KEY,
     display_name TEXT,
     default_currency TEXT DEFAULT 'TWD',
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   -- 帳戶表
   CREATE TABLE accounts (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
     name TEXT NOT NULL,
     type TEXT NOT NULL CHECK (type IN ('cash', 'bank', 'credit_card', 'investment', 'loan', 'other')),
     balance DECIMAL(15,2) DEFAULT 0,
     currency TEXT DEFAULT 'TWD',
     is_active BOOLEAN DEFAULT true,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   -- 交易記錄表
   CREATE TABLE transactions (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
     account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
     category_id UUID REFERENCES categories(id),
     amount DECIMAL(15,2) NOT NULL,
     type TEXT NOT NULL CHECK (type IN ('income', 'expense', 'transfer')),
     description TEXT,
     date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     tags TEXT[],
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   -- 類別表
   CREATE TABLE categories (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
     name TEXT NOT NULL,
     type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
     icon TEXT DEFAULT 'help-outline',
     color TEXT DEFAULT '#007AFF',
     is_default BOOLEAN DEFAULT false,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   -- 資產表
   CREATE TABLE assets (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
     name TEXT NOT NULL,
     type TEXT NOT NULL CHECK (type IN ('tw_stock', 'us_stock', 'mutual_fund', 'cryptocurrency', 'real_estate', 'vehicle', 'insurance', 'precious_metal', 'other')),
     quantity DECIMAL(15,6) DEFAULT 0,
     cost_basis DECIMAL(15,2) DEFAULT 0,
     current_value DECIMAL(15,2) DEFAULT 0,
     symbol TEXT, -- 股票代號
     last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   -- 負債表
   CREATE TABLE liabilities (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
     name TEXT NOT NULL,
     type TEXT NOT NULL CHECK (type IN ('credit_card', 'personal_loan', 'mortgage', 'car_loan', 'other_loan')),
     balance DECIMAL(15,2) DEFAULT 0,
     interest_rate DECIMAL(5,2),
     monthly_payment DECIMAL(15,2),
     due_date DATE,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );
   ```

3. **設定 RLS 規則**
   ```sql
   -- 啟用 RLS
   ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
   ALTER TABLE accounts ENABLE ROW LEVEL SECURITY;
   ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
   ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
   ALTER TABLE assets ENABLE ROW LEVEL SECURITY;
   ALTER TABLE liabilities ENABLE ROW LEVEL SECURITY;

   -- 創建 RLS 策略
   CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
   CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
   CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (auth.uid() = id);

   CREATE POLICY "Users can manage own accounts" ON accounts FOR ALL USING (auth.uid() = user_id);
   CREATE POLICY "Users can manage own transactions" ON transactions FOR ALL USING (auth.uid() = user_id);
   CREATE POLICY "Users can manage own categories" ON categories FOR ALL USING (auth.uid() = user_id);
   CREATE POLICY "Users can manage own assets" ON assets FOR ALL USING (auth.uid() = user_id);
   CREATE POLICY "Users can manage own liabilities" ON liabilities FOR ALL USING (auth.uid() = user_id);
   ```

### 第二階段：API 整合實現

1. **創建 Supabase Edge Functions**
   ```bash
   # 安裝 Supabase CLI
   npm install -g supabase

   # 初始化 Supabase 項目
   supabase init

   # 創建 Edge Function
   supabase functions new stock-prices
   ```

2. **實現股價更新 Edge Function**
   - Alpha Vantage API 整合
   - TWSE API 整合
   - 錯誤處理和重試機制
   - 數據緩存策略

### 第三階段：核心功能開發

1. **完善認證流程**
   - 用戶註冊後自動創建 profile
   - 默認類別初始化
   - 歡迎引導流程

2. **實現記帳功能**
   - 交易表單組件
   - 類別選擇器
   - 日期時間選擇器
   - 帳戶選擇器

3. **資產負債管理**
   - 資產添加/編輯表單
   - 股票代號驗證
   - 市價更新機制

## 🛠️ 開發工具與命令

### 常用開發命令
```bash
# 啟動開發服務器
npm start

# TypeScript 類型檢查
npx tsc --noEmit

# 代碼格式化
npx prettier --write .

# ESLint 檢查
npx eslint . --ext .ts,.tsx

# 清理緩存
npx expo start --clear
```

### 調試技巧
1. **React Native Debugger**
   - 安裝：https://github.com/jhen0409/react-native-debugger
   - 用於調試 Redux/Zustand 狀態

2. **Flipper**
   - 網絡請求監控
   - 數據庫查詢調試

3. **Expo Dev Tools**
   - 設備日誌查看
   - 性能監控

## 📱 測試策略

### 單元測試
```bash
# 安裝測試依賴
npm install --save-dev jest @testing-library/react-native

# 運行測試
npm test
```

### 組件測試
- 使用 React Native Testing Library
- 測試用戶交互
- 快照測試

### 端對端測試
- 考慮使用 Detox
- 測試關鍵用戶流程

## 🚀 部署準備

### 環境配置
1. **生產環境變量**
   - Supabase 生產環境配置
   - API 金鑰管理
   - 錯誤追蹤 (Sentry)

2. **應用商店準備**
   - iOS App Store Connect
   - Google Play Console
   - 應用圖標和截圖

### 性能優化
1. **代碼分割**
   - 懶加載組件
   - 路由級別分割

2. **圖片優化**
   - WebP 格式
   - 響應式圖片

3. **數據庫優化**
   - 索引優化
   - 查詢優化

## 🔍 故障排除

### 常見問題

1. **Expo 啟動失敗**
   ```bash
   # 清理緩存
   npx expo start --clear
   
   # 重新安裝依賴
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **TypeScript 錯誤**
   - 檢查 tsconfig.json 配置
   - 確認類型定義文件

3. **Supabase 連接問題**
   - 驗證環境變量
   - 檢查 RLS 規則
   - 確認 API 金鑰權限

### 調試步驟
1. 檢查控制台錯誤
2. 驗證網絡請求
3. 確認數據庫連接
4. 測試 API 端點

## 📚 學習資源

- [Expo 官方文檔](https://docs.expo.dev/)
- [React Navigation 指南](https://reactnavigation.org/)
- [Supabase 文檔](https://supabase.com/docs)
- [Zustand 使用指南](https://github.com/pmndrs/zustand)

---

**記住：小步快跑，持續迭代！** 🚀
