#!/usr/bin/env node

/**
 * 美國ETF數據導入腳本
 * 從 CSV 文件讀取美國ETF數據並導入到 Supabase
 */

const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Supabase 配置
const SUPABASE_URL = 'https://your-project.supabase.co'; // 請替換為實際的 URL
const SUPABASE_ANON_KEY = 'your-anon-key'; // 請替換為實際的 key

// 初始化 Supabase 客戶端
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

/**
 * 解析 CSV 文件
 */
function parseCSV(filePath) {
  try {
    const csvContent = fs.readFileSync(filePath, 'utf-8');
    const lines = csvContent.split('\n');
    
    const etfData = [];
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (!trimmedLine) continue;
      
      // 分割 CSV 行，只取前兩個字段
      const parts = trimmedLine.split(',');
      if (parts.length >= 2) {
        const symbol = parts[0].trim();
        const name = parts[1].trim();
        
        // 檢查是否為有效的 ETF 代碼（只包含字母）
        if (symbol && name && /^[A-Z]+$/.test(symbol)) {
          etfData.push({
            symbol: symbol,
            name: name,
            sector: 'ETF' // 預設分類為 ETF
          });
        }
      }
    }
    
    console.log(`✅ 成功解析 ${etfData.length} 個 ETF`);
    return etfData;
    
  } catch (error) {
    console.error('❌ 解析 CSV 文件失敗:', error.message);
    return [];
  }
}

/**
 * 批量插入 ETF 數據到 Supabase
 */
async function insertETFData(etfData) {
  try {
    console.log('🚀 開始插入 ETF 數據到 Supabase...');
    
    // 分批插入，每批 100 個
    const batchSize = 100;
    let totalInserted = 0;
    
    for (let i = 0; i < etfData.length; i += batchSize) {
      const batch = etfData.slice(i, i + batchSize);
      
      console.log(`📦 處理第 ${Math.floor(i/batchSize) + 1} 批，共 ${batch.length} 個 ETF...`);
      
      // 準備批量數據
      const batchData = batch.map(etf => ({
        symbol: etf.symbol,
        name: etf.name,
        is_etf: true,
        asset_type: 'ETF',
        sector: etf.sector,
        is_sp500: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));
      
      // 使用 upsert 插入數據
      const { data, error } = await supabase
        .from('us_stocks')
        .upsert(batchData, {
          onConflict: 'symbol',
          ignoreDuplicates: false
        });
      
      if (error) {
        console.error(`❌ 第 ${Math.floor(i/batchSize) + 1} 批插入失敗:`, error.message);
        continue;
      }
      
      totalInserted += batch.length;
      console.log(`✅ 第 ${Math.floor(i/batchSize) + 1} 批插入成功`);
      
      // 避免請求過於頻繁
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log(`🎉 總共成功插入 ${totalInserted} 個 ETF`);
    return totalInserted;
    
  } catch (error) {
    console.error('❌ 插入數據失敗:', error.message);
    return 0;
  }
}

/**
 * 驗證插入結果
 */
async function verifyInsert() {
  try {
    console.log('🔍 驗證插入結果...');
    
    const { data, error } = await supabase
      .from('us_stocks')
      .select('symbol, name, is_etf')
      .eq('is_etf', true)
      .limit(10);
    
    if (error) {
      console.error('❌ 驗證失敗:', error.message);
      return;
    }
    
    console.log('✅ 驗證成功，前 10 個 ETF:');
    data.forEach((etf, index) => {
      console.log(`${index + 1}. ${etf.symbol} - ${etf.name}`);
    });
    
    // 獲取總數
    const { count, error: countError } = await supabase
      .from('us_stocks')
      .select('*', { count: 'exact', head: true })
      .eq('is_etf', true);
    
    if (!countError) {
      console.log(`📊 數據庫中總共有 ${count} 個 ETF`);
    }
    
  } catch (error) {
    console.error('❌ 驗證過程出錯:', error.message);
  }
}

/**
 * 主函數
 */
async function main() {
  console.log('🎯 美國ETF數據導入工具');
  console.log('================================');
  
  // CSV 文件路徑
  const csvPath = path.join(__dirname, '../database/美國ETF.csv');
  
  // 檢查文件是否存在
  if (!fs.existsSync(csvPath)) {
    console.error(`❌ CSV 文件不存在: ${csvPath}`);
    process.exit(1);
  }
  
  // 解析 CSV 文件
  const etfData = parseCSV(csvPath);
  
  if (etfData.length === 0) {
    console.error('❌ 沒有找到有效的 ETF 數據');
    process.exit(1);
  }
  
  // 顯示前幾個 ETF 作為預覽
  console.log('\n📋 ETF 數據預覽:');
  etfData.slice(0, 5).forEach((etf, index) => {
    console.log(`${index + 1}. ${etf.symbol} - ${etf.name}`);
  });
  console.log(`... 還有 ${etfData.length - 5} 個 ETF\n`);
  
  // 插入數據
  const insertedCount = await insertETFData(etfData);
  
  if (insertedCount > 0) {
    // 驗證插入結果
    await verifyInsert();
    console.log('\n🎉 ETF 數據導入完成！');
  } else {
    console.log('\n❌ ETF 數據導入失敗！');
    process.exit(1);
  }
}

// 執行主函數
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 程序執行失敗:', error.message);
    process.exit(1);
  });
}

module.exports = {
  parseCSV,
  insertETFData,
  verifyInsert
};
