/**
 * 調試收支分析中缺少還款記錄的問題
 * 系統性檢查整個數據流程
 */

console.log('🔍 ===== 調試收支分析缺少還款記錄問題 =====\n');

// 模擬完整的數據流程
function simulateCompleteDataFlow() {
  console.log('📊 模擬完整的數據流程...\n');
  
  // 1. 模擬初始交易數據
  console.log('1️⃣ 初始交易數據:');
  const initialTransactions = [
    {
      id: '1',
      amount: 500,
      type: 'expense',
      description: '午餐',
      category: '餐飲',
      account: '現金',
      date: new Date(2025, 4, 20, 12, 0, 0, 0).toISOString()
    },
    {
      id: '2',
      amount: 80000,
      type: 'income',
      description: '薪水',
      category: '薪水',
      account: '銀行',
      date: new Date(2025, 4, 1, 12, 0, 0, 0).toISOString()
    }
  ];
  
  console.log('初始交易:');
  initialTransactions.forEach(t => {
    console.log(`  ${t.description}: ${t.type === 'income' ? '+' : '-'}${t.amount} (${t.category})`);
  });
  
  // 2. 模擬負債創建
  console.log('\n2️⃣ 負債創建:');
  const liability = {
    id: 'test_liability',
    name: '測試信用卡',
    type: 'credit_card',
    balance: 50000,
    monthly_payment: 10000,
    payment_account: '銀行',
    payment_day: 31,
    payment_periods: 12,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
  
  console.log('負債信息:', {
    name: liability.name,
    monthlyPayment: liability.monthly_payment,
    paymentDay: liability.payment_day,
    paymentAccount: liability.payment_account
  });
  
  // 3. 模擬交易記錄創建（使用修復後的邏輯）
  console.log('\n3️⃣ 交易記錄創建:');
  
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();
  const paymentDay = liability.payment_day;
  
  // 使用修復後的日期邏輯
  const lastDayOfCurrentMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
  let actualPaymentDay;
  if (paymentDay > lastDayOfCurrentMonth) {
    actualPaymentDay = lastDayOfCurrentMonth;
    console.log(`📅 需要調整: 原定${paymentDay}號 → ${actualPaymentDay}號`);
  } else {
    actualPaymentDay = paymentDay;
    console.log(`📅 無需調整: ${paymentDay}號正常`);
  }
  
  // 使用修復後的時區邏輯
  const paymentDate = new Date(currentYear, currentMonth, actualPaymentDay, 12, 0, 0, 0);
  
  const debtTransaction = {
    id: `debt_payment_${Date.now()}`,
    amount: liability.monthly_payment,
    type: 'expense',
    description: liability.name,
    category: '還款',
    account: liability.payment_account,
    date: paymentDate.toISOString(),
    is_recurring: true,
    recurring_frequency: 'monthly',
    max_occurrences: liability.payment_periods,
  };
  
  console.log('創建的還款交易:');
  console.log(`  ID: ${debtTransaction.id}`);
  console.log(`  描述: ${debtTransaction.description}`);
  console.log(`  金額: ${debtTransaction.amount}`);
  console.log(`  類別: ${debtTransaction.category}`);
  console.log(`  帳戶: ${debtTransaction.account}`);
  console.log(`  日期 (ISO): ${debtTransaction.date}`);
  console.log(`  日期 (本地): ${new Date(debtTransaction.date).toLocaleDateString('zh-TW')}`);
  console.log(`  日期鍵: ${debtTransaction.date.split('T')[0]}`);
  
  // 4. 模擬完整交易列表
  console.log('\n4️⃣ 完整交易列表:');
  const allTransactions = [...initialTransactions, debtTransaction];
  
  console.log('所有交易:');
  allTransactions.forEach((t, index) => {
    console.log(`  ${index + 1}. ${t.description}: ${t.type === 'income' ? '+' : '-'}${t.amount} (${t.category}) - ${new Date(t.date).toLocaleDateString('zh-TW')}`);
  });
  
  return { allTransactions, debtTransaction };
}

const { allTransactions, debtTransaction } = simulateCompleteDataFlow();

// 模擬收支分析的過濾邏輯
function simulateCashFlowFiltering() {
  console.log('\n📈 ===== 模擬收支分析過濾邏輯 =====');
  
  // 模擬 CashFlowScreen 的過濾邏輯
  const now = new Date();
  const startDate = new Date(now.getFullYear(), now.getMonth(), 1); // 本月1號
  
  console.log('過濾條件:');
  console.log(`  時間範圍: ${startDate.toLocaleDateString('zh-TW')} - ${now.toLocaleDateString('zh-TW')}`);
  console.log(`  過濾類型: 全部`);
  
  // 檢查還款交易
  const debtPayments = allTransactions.filter(t => t.category === '還款');
  console.log(`\n🔍 還款交易檢查:`);
  console.log(`  總還款交易數: ${debtPayments.length}`);
  
  if (debtPayments.length > 0) {
    debtPayments.forEach(t => {
      console.log(`    - ${t.description}: ${t.amount} (${new Date(t.date).toLocaleDateString('zh-TW')})`);
    });
  } else {
    console.log(`    ❌ 沒有找到還款交易`);
  }
  
  // 過濾本月交易
  const filteredTransactions = allTransactions.filter(t => {
    const transactionDate = new Date(t.date);
    const isInRange = transactionDate >= startDate && transactionDate <= now;
    return isInRange;
  });
  
  console.log(`\n📊 過濾結果:`);
  console.log(`  過濾前交易數: ${allTransactions.length}`);
  console.log(`  過濾後交易數: ${filteredTransactions.length}`);
  
  console.log(`\n過濾後的交易:`);
  filteredTransactions.forEach(t => {
    const transactionDate = new Date(t.date);
    console.log(`    ${t.description}: ${t.type === 'income' ? '+' : '-'}${t.amount} (${t.category}) - ${transactionDate.toLocaleDateString('zh-TW')}`);
  });
  
  // 檢查過濾後的還款交易
  const filteredDebtPayments = filteredTransactions.filter(t => t.category === '還款');
  console.log(`\n🔍 過濾後還款交易:`);
  console.log(`  數量: ${filteredDebtPayments.length}`);
  
  return { filteredTransactions, filteredDebtPayments };
}

const { filteredTransactions, filteredDebtPayments } = simulateCashFlowFiltering();

// 模擬 FinancialCalculator 的計算邏輯
function simulateFinancialCalculator() {
  console.log('\n🧮 ===== 模擬 FinancialCalculator 計算邏輯 =====');
  
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();
  
  // 獲取當月交易
  const currentMonthTransactions = allTransactions.filter(transaction => {
    const transactionDate = new Date(transaction.date);
    return transactionDate.getFullYear() === currentYear && 
           transactionDate.getMonth() === currentMonth;
  });
  
  console.log('當月交易過濾:');
  console.log(`  總交易數: ${allTransactions.length}`);
  console.log(`  當月交易數: ${currentMonthTransactions.length}`);
  
  // 計算各項金額
  let monthlyIncome = 0;
  let monthlyExpenses = 0;
  let monthlyDebtPayments = 0;
  let regularExpenses = 0;
  
  currentMonthTransactions.forEach(transaction => {
    console.log(`  處理交易: ${transaction.description} - ${transaction.type} - ${transaction.category} - ${transaction.amount}`);
    
    if (transaction.type === 'income') {
      monthlyIncome += transaction.amount;
    } else if (transaction.type === 'expense') {
      monthlyExpenses += transaction.amount;
      if (transaction.category === '還款') {
        monthlyDebtPayments += transaction.amount;
        console.log(`    ✅ 計入還款: ${transaction.amount}`);
      } else {
        regularExpenses += transaction.amount;
      }
    }
  });
  
  console.log('\n計算結果:');
  console.log(`  月收入: ${monthlyIncome}`);
  console.log(`  月支出: ${monthlyExpenses}`);
  console.log(`  還款金額: ${monthlyDebtPayments}`);
  console.log(`  一般支出: ${regularExpenses}`);
  console.log(`  淨收入: ${monthlyIncome - monthlyExpenses}`);
  
  return {
    monthlyIncome,
    monthlyExpenses,
    monthlyDebtPayments,
    regularExpenses
  };
}

const calculatorResult = simulateFinancialCalculator();

// 總結分析
console.log('\n🎯 ===== 問題分析總結 =====');

const hasDebtTransaction = debtTransaction !== undefined;
const hasFilteredDebtPayments = filteredDebtPayments.length > 0;
const hasCalculatedDebtPayments = calculatorResult.monthlyDebtPayments > 0;

console.log('1. 交易創建檢查:');
console.log(`   還款交易已創建: ${hasDebtTransaction ? '✅' : '❌'}`);
if (hasDebtTransaction) {
  console.log(`   交易ID: ${debtTransaction.id}`);
  console.log(`   交易金額: ${debtTransaction.amount}`);
  console.log(`   交易類別: ${debtTransaction.category}`);
}

console.log('\n2. 過濾邏輯檢查:');
console.log(`   過濾後包含還款: ${hasFilteredDebtPayments ? '✅' : '❌'}`);
if (hasFilteredDebtPayments) {
  console.log(`   過濾後還款數量: ${filteredDebtPayments.length}`);
}

console.log('\n3. 計算邏輯檢查:');
console.log(`   計算器包含還款: ${hasCalculatedDebtPayments ? '✅' : '❌'}`);
if (hasCalculatedDebtPayments) {
  console.log(`   計算的還款金額: ${calculatorResult.monthlyDebtPayments}`);
}

console.log('\n🔍 可能的問題點:');
if (!hasDebtTransaction) {
  console.log('❌ 問題1: 還款交易沒有被創建');
  console.log('   檢查: 負債同步服務是否正確執行');
} else if (!hasFilteredDebtPayments) {
  console.log('❌ 問題2: 還款交易被過濾掉了');
  console.log('   檢查: 日期範圍過濾邏輯');
} else if (!hasCalculatedDebtPayments) {
  console.log('❌ 問題3: 計算器沒有正確計算還款');
  console.log('   檢查: FinancialCalculator 的邏輯');
} else {
  console.log('✅ 邏輯上應該沒有問題');
  console.log('   可能是: 實際應用中的數據同步問題');
}

console.log('\n🛠️ 建議的調試步驟:');
console.log('1. 檢查瀏覽器控制台是否有錯誤');
console.log('2. 確認是否看到 "🔥🔥🔥 修復" 相關日誌');
console.log('3. 檢查 transactionDataService.getTransactions() 的實際內容');
console.log('4. 驗證事件監聽器是否正確觸發');
console.log('5. 確認應用程序已完全重新啟動');

console.log('\n✨ 調試完成！');
