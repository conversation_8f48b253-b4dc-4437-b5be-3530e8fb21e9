#!/usr/bin/env python3
"""
測試上架前的修改是否完成
"""

import os
import re

def check_dashboard_buttons_removed():
    """檢查儀錶板的5個符號按鈕是否已移除"""
    print("🔍 檢查儀錶板按鈕移除...")
    
    file_path = 'src/screens/main/DashboardScreen.tsx'
    
    if not os.path.exists(file_path):
        print("❌ 儀錶板文件不存在")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查是否還有測試按鈕
        test_button_patterns = [
            r'數據綁定驗證按鈕',
            r'同步驗證測試按鈕',
            r'更新類別按鈕',
            r'測試按鈕',
            r'analytics-outline',
            r'checkmark-circle-outline',
            r'list-outline.*24.*color.*#fff',
            r'refresh-outline.*24.*color.*#fff'
        ]
        
        found_buttons = []
        for pattern in test_button_patterns:
            if re.search(pattern, content):
                found_buttons.append(pattern)
        
        if found_buttons:
            print(f"❌ 仍有 {len(found_buttons)} 個測試按鈕未移除:")
            for button in found_buttons:
                print(f"   - {button}")
            return False
        else:
            print("✅ 所有測試按鈕已成功移除")
            return True
            
    except Exception as e:
        print(f"❌ 檢查失敗: {str(e)}")
        return False

def check_edit_name_feature():
    """檢查編輯名稱功能是否已添加"""
    print("\n🔍 檢查編輯名稱功能...")
    
    file_path = 'src/screens/main/DashboardScreen.tsx'
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查必要的功能
        required_features = [
            r'userProfileService',
            r'showEditNameModal',
            r'handleEditName',
            r'handleSaveName',
            r'create-outline',
            r'編輯名稱',
            r'visible=\{showEditNameModal\}',
            r'value=\{editingName\}'
        ]
        
        missing_features = []
        for feature in required_features:
            if not re.search(feature, content):
                missing_features.append(feature)
        
        if missing_features:
            print(f"❌ 缺少 {len(missing_features)} 個編輯功能:")
            for feature in missing_features:
                print(f"   - {feature}")
            return False
        else:
            print("✅ 編輯名稱功能已完整添加")
            return True
            
    except Exception as e:
        print(f"❌ 檢查失敗: {str(e)}")
        return False

def check_user_profile_service():
    """檢查用戶資料服務是否存在"""
    print("\n🔍 檢查用戶資料服務...")
    
    file_path = 'src/services/userProfileService.ts'
    
    if not os.path.exists(file_path):
        print("❌ 用戶資料服務文件不存在")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查必要的功能
        required_functions = [
            r'class UserProfileService',
            r'initialize\(\)',
            r'updateDisplayName',
            r'getDisplayName',
            r'小富翁',
            r'AsyncStorage'
        ]
        
        missing_functions = []
        for func in required_functions:
            if not re.search(func, content):
                missing_functions.append(func)
        
        if missing_functions:
            print(f"❌ 缺少 {len(missing_functions)} 個必要功能:")
            for func in missing_functions:
                print(f"   - {func}")
            return False
        else:
            print("✅ 用戶資料服務功能完整")
            return True
            
    except Exception as e:
        print(f"❌ 檢查失敗: {str(e)}")
        return False

def check_demo_name_change():
    """檢查demo名稱是否已改為小富翁"""
    print("\n🔍 檢查demo名稱修改...")
    
    files_to_check = [
        ('src/screens/main/DashboardScreen.tsx', r'小富翁'),
        ('demo.html', r'小富翁'),
    ]
    
    all_correct = True
    
    for file_path, pattern in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if re.search(pattern, content):
                    print(f"✅ {file_path} 已使用'小富翁'")
                else:
                    print(f"❌ {file_path} 未找到'小富翁'")
                    all_correct = False
                    
            except Exception as e:
                print(f"❌ 檢查 {file_path} 失敗: {str(e)}")
                all_correct = False
        else:
            print(f"⚠️ {file_path} 不存在")
    
    return all_correct

def check_etf_labels_removed():
    """檢查ETF標籤是否已移除"""
    print("\n🔍 檢查ETF標籤移除...")
    
    file_path = 'src/components/USStockSearchInput.tsx'
    
    if not os.path.exists(file_path):
        print("❌ 美股搜索組件不存在")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查是否還有ETF標籤相關代碼
        etf_patterns = [
            r'etfBadge',
            r'item\.is_etf.*&&',
            r'ETF.*Badge',
            r'symbolContainer'
        ]
        
        found_etf_code = []
        for pattern in etf_patterns:
            if re.search(pattern, content):
                found_etf_code.append(pattern)
        
        if found_etf_code:
            print(f"❌ 仍有 {len(found_etf_code)} 個ETF標籤相關代碼:")
            for code in found_etf_code:
                print(f"   - {code}")
            return False
        else:
            print("✅ ETF標籤已成功移除")
            return True
            
    except Exception as e:
        print(f"❌ 檢查失敗: {str(e)}")
        return False

def main():
    """主函數"""
    print("🚀 上架前修改檢查工具")
    print("=" * 60)
    
    checks = [
        ("移除儀錶板5個符號", check_dashboard_buttons_removed),
        ("編輯名稱功能", check_edit_name_feature),
        ("用戶資料服務", check_user_profile_service),
        ("demo改為小富翁", check_demo_name_change),
        ("ETF標籤移除", check_etf_labels_removed),
    ]
    
    results = []
    
    for check_name, check_func in checks:
        print(f"\n📋 檢查: {check_name}")
        print("-" * 40)
        result = check_func()
        results.append((check_name, result))
    
    # 總結
    print(f"\n{'='*60}")
    print("📊 檢查結果總結:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{status} {check_name}")
        if result:
            passed += 1
    
    print(f"\n📈 總體結果: {passed}/{total} 項檢查通過")
    
    if passed == total:
        print("🎉 所有修改已完成，可以準備上架測試版！")
        print("\n📱 上架準備清單:")
        print("✅ 移除了開發測試按鈕")
        print("✅ 添加了用戶名稱編輯功能")
        print("✅ 默認用戶名改為'小富翁'")
        print("✅ 移除了ETF標籤顯示")
        print("✅ 界面更加簡潔統一")
    else:
        print("⚠️ 仍有部分修改未完成，請檢查上述失敗項目")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
