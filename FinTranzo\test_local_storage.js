/**
 * 本地存儲功能測試腳本
 * 測試 AsyncStorage 的數據持久化功能
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

// 模擬測試數據
const testTransactions = [
  {
    id: 'test_1',
    amount: 1000,
    type: 'expense',
    description: '測試支出',
    category: '餐飲',
    account: '現金',
    date: new Date().toISOString()
  },
  {
    id: 'test_2',
    amount: 50000,
    type: 'income',
    description: '測試收入',
    category: '薪水',
    account: '銀行',
    date: new Date().toISOString()
  }
];

const testCategories = [
  { id: '1', name: '餐飲', icon: 'restaurant-outline', color: '#FF6384', type: 'expense' },
  { id: '2', name: '薪水', icon: 'card-outline', color: '#2ECC71', type: 'income' }
];

const testAccounts = [
  { id: '1', name: '現金', type: 'cash' },
  { id: '2', name: '銀行', type: 'bank' }
];

// 存儲鍵名
const STORAGE_KEYS = {
  TRANSACTIONS: '@FinTranzo:transactions',
  CATEGORIES: '@FinTranzo:categories',
  ACCOUNTS: '@FinTranzo:accounts',
  INITIALIZED: '@FinTranzo:initialized'
};

/**
 * 測試數據保存功能
 */
async function testSaveData() {
  console.log('🧪 測試數據保存功能...');
  
  try {
    // 保存測試數據
    await AsyncStorage.setItem(STORAGE_KEYS.TRANSACTIONS, JSON.stringify(testTransactions));
    await AsyncStorage.setItem(STORAGE_KEYS.CATEGORIES, JSON.stringify(testCategories));
    await AsyncStorage.setItem(STORAGE_KEYS.ACCOUNTS, JSON.stringify(testAccounts));
    await AsyncStorage.setItem(STORAGE_KEYS.INITIALIZED, 'true');
    
    console.log('✅ 數據保存成功');
    return true;
  } catch (error) {
    console.error('❌ 數據保存失敗:', error);
    return false;
  }
}

/**
 * 測試數據加載功能
 */
async function testLoadData() {
  console.log('🧪 測試數據加載功能...');
  
  try {
    // 加載數據
    const transactionsData = await AsyncStorage.getItem(STORAGE_KEYS.TRANSACTIONS);
    const categoriesData = await AsyncStorage.getItem(STORAGE_KEYS.CATEGORIES);
    const accountsData = await AsyncStorage.getItem(STORAGE_KEYS.ACCOUNTS);
    const initializedData = await AsyncStorage.getItem(STORAGE_KEYS.INITIALIZED);
    
    // 解析數據
    const transactions = transactionsData ? JSON.parse(transactionsData) : [];
    const categories = categoriesData ? JSON.parse(categoriesData) : [];
    const accounts = accountsData ? JSON.parse(accountsData) : [];
    const initialized = initializedData === 'true';
    
    console.log('📊 加載的數據:');
    console.log('  - 交易記錄數量:', transactions.length);
    console.log('  - 類別數量:', categories.length);
    console.log('  - 帳戶數量:', accounts.length);
    console.log('  - 已初始化:', initialized);
    
    // 驗證數據完整性
    const isValid = transactions.length === testTransactions.length &&
                   categories.length === testCategories.length &&
                   accounts.length === testAccounts.length &&
                   initialized === true;
    
    if (isValid) {
      console.log('✅ 數據加載成功，數據完整');
      return true;
    } else {
      console.log('❌ 數據加載失敗，數據不完整');
      return false;
    }
  } catch (error) {
    console.error('❌ 數據加載失敗:', error);
    return false;
  }
}

/**
 * 測試數據更新功能
 */
async function testUpdateData() {
  console.log('🧪 測試數據更新功能...');
  
  try {
    // 加載現有數據
    const transactionsData = await AsyncStorage.getItem(STORAGE_KEYS.TRANSACTIONS);
    const transactions = transactionsData ? JSON.parse(transactionsData) : [];
    
    // 添加新交易
    const newTransaction = {
      id: 'test_3',
      amount: 200,
      type: 'expense',
      description: '新增測試交易',
      category: '交通',
      account: '現金',
      date: new Date().toISOString()
    };
    
    transactions.push(newTransaction);
    
    // 保存更新後的數據
    await AsyncStorage.setItem(STORAGE_KEYS.TRANSACTIONS, JSON.stringify(transactions));
    
    // 驗證更新
    const updatedData = await AsyncStorage.getItem(STORAGE_KEYS.TRANSACTIONS);
    const updatedTransactions = JSON.parse(updatedData);
    
    if (updatedTransactions.length === transactions.length) {
      console.log('✅ 數據更新成功');
      return true;
    } else {
      console.log('❌ 數據更新失敗');
      return false;
    }
  } catch (error) {
    console.error('❌ 數據更新失敗:', error);
    return false;
  }
}

/**
 * 測試數據清除功能
 */
async function testClearData() {
  console.log('🧪 測試數據清除功能...');
  
  try {
    // 清除所有數據
    await AsyncStorage.multiRemove([
      STORAGE_KEYS.TRANSACTIONS,
      STORAGE_KEYS.CATEGORIES,
      STORAGE_KEYS.ACCOUNTS,
      STORAGE_KEYS.INITIALIZED
    ]);
    
    // 驗證清除
    const transactionsData = await AsyncStorage.getItem(STORAGE_KEYS.TRANSACTIONS);
    const initializedData = await AsyncStorage.getItem(STORAGE_KEYS.INITIALIZED);
    
    if (transactionsData === null && initializedData === null) {
      console.log('✅ 數據清除成功');
      return true;
    } else {
      console.log('❌ 數據清除失敗');
      return false;
    }
  } catch (error) {
    console.error('❌ 數據清除失敗:', error);
    return false;
  }
}

/**
 * 運行完整測試套件
 */
async function runLocalStorageTests() {
  console.log('🚀 開始本地存儲功能測試...');
  console.log('=====================================');
  
  const results = {
    save: false,
    load: false,
    update: false,
    clear: false
  };
  
  // 1. 測試保存
  results.save = await testSaveData();
  
  // 2. 測試加載
  if (results.save) {
    results.load = await testLoadData();
  }
  
  // 3. 測試更新
  if (results.load) {
    results.update = await testUpdateData();
  }
  
  // 4. 測試清除
  results.clear = await testClearData();
  
  // 總結
  console.log('=====================================');
  console.log('📋 測試結果總結:');
  console.log('  - 數據保存:', results.save ? '✅ 通過' : '❌ 失敗');
  console.log('  - 數據加載:', results.load ? '✅ 通過' : '❌ 失敗');
  console.log('  - 數據更新:', results.update ? '✅ 通過' : '❌ 失敗');
  console.log('  - 數據清除:', results.clear ? '✅ 通過' : '❌ 失敗');
  
  const allPassed = Object.values(results).every(result => result);
  
  if (allPassed) {
    console.log('🎉 所有測試通過！本地存儲功能正常');
  } else {
    console.log('⚠️ 部分測試失敗，請檢查本地存儲配置');
  }
  
  return results;
}

// 導出測試函數
export {
  runLocalStorageTests,
  testSaveData,
  testLoadData,
  testUpdateData,
  testClearData
};

// 如果直接運行此文件，執行測試
if (require.main === module) {
  runLocalStorageTests().catch(console.error);
}
