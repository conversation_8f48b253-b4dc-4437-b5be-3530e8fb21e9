// 最簡單的環境變數測試
console.log('🚀 開始環境變數測試...');

// 載入環境變數
require('dotenv').config();

console.log('📋 環境變數狀態:');
console.log('EXPO_PUBLIC_SUPABASE_URL:', process.env.EXPO_PUBLIC_SUPABASE_URL ? '✅ 已設置' : '❌ 未設置');
console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? '✅ 已設置' : '❌ 未設置');

if (process.env.EXPO_PUBLIC_SUPABASE_URL && process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.log('🎉 環境變數設置正確！');
} else {
  console.log('❌ 環境變數設置有問題');
}

console.log('✅ 測試完成');
