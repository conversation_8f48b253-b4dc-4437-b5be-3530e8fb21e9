#!/usr/bin/env python3
"""
檢查數據庫中ETF的總數和更新狀態
"""

import sys
from supabase import create_client, Client

# Supabase配置
SUPABASE_URL = "https://yrryyapzkgrsahranzvo.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlycnl5YXB6a2dyc2FocmFuenZvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODE3MzYzNSwiZXhwIjoyMDYzNzQ5NjM1fQ.xjiXVagzKgBBYXqtRjJiXwCgbRdEW0EpFzCIlJ26SSQ"

def check_etf_status():
    """檢查ETF狀態"""
    try:
        # 創建Supabase客戶端
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        print("✅ Supabase連接成功")
        
        # 1. 檢查總ETF數量
        total_result = supabase.table('us_stocks').select('*', count='exact').eq('is_etf', True).execute()
        total_etfs = total_result.count
        
        # 2. 檢查有價格的ETF數量
        priced_result = supabase.table('us_stocks').select('*', count='exact').eq('is_etf', True).not_.is_('price', 'null').execute()
        priced_etfs = priced_result.count
        
        # 3. 檢查沒有價格的ETF數量
        unpriced_result = supabase.table('us_stocks').select('*', count='exact').eq('is_etf', True).is_('price', 'null').execute()
        unpriced_etfs = unpriced_result.count
        
        print("\n📊 ETF狀態統計:")
        print("=" * 50)
        print(f"📈 總ETF數量: {total_etfs}")
        print(f"✅ 已有價格: {priced_etfs}")
        print(f"❌ 未有價格: {unpriced_etfs}")
        print(f"📊 完成度: {(priced_etfs/total_etfs*100):.1f}%")
        
        # 4. 顯示沒有價格的ETF列表
        if unpriced_etfs > 0:
            print(f"\n📋 未更新價格的ETF (前20個):")
            unpriced_list = supabase.table('us_stocks').select('symbol, name').eq('is_etf', True).is_('price', 'null').order('symbol').limit(20).execute()
            
            if unpriced_list.data:
                for i, etf in enumerate(unpriced_list.data, 1):
                    print(f"   {i:2d}. {etf['symbol']}: {etf['name']}")
        
        # 5. 顯示最近更新的ETF
        print(f"\n🕒 最近更新的ETF (前10個):")
        recent_result = supabase.table('us_stocks').select('symbol, name, price, change_percent, updated_at').eq('is_etf', True).not_.is_('price', 'null').order('updated_at', desc=True).limit(10).execute()
        
        if recent_result.data:
            for i, etf in enumerate(recent_result.data, 1):
                updated_time = etf['updated_at'][:19] if etf['updated_at'] else 'N/A'
                print(f"   {i:2d}. {etf['symbol']}: ${etf['price']} ({etf['change_percent']:+.2f}%) - {updated_time}")
        
        # 6. 檢查ETF視圖
        print(f"\n🔍 測試ETF視圖:")
        view_result = supabase.table('us_etf_view').select('*', count='exact').execute()
        view_count = view_result.count
        print(f"   us_etf_view 記錄數: {view_count}")
        
        # 7. 檢查ETF分類視圖
        sector_result = supabase.table('us_etf_by_sector').select('*', count='exact').execute()
        sector_count = sector_result.count
        print(f"   us_etf_by_sector 記錄數: {sector_count}")
        
        return {
            'total': total_etfs,
            'priced': priced_etfs,
            'unpriced': unpriced_etfs,
            'completion_rate': priced_etfs/total_etfs*100 if total_etfs > 0 else 0
        }
        
    except Exception as e:
        print(f"❌ 檢查ETF狀態失敗: {str(e)}")
        return None

def main():
    """主函數"""
    print("📊 ETF狀態檢查工具")
    print("=" * 50)
    
    result = check_etf_status()
    
    if result:
        print(f"\n🎯 建議:")
        if result['unpriced'] > 0:
            print(f"   還有 {result['unpriced']} 個ETF需要更新價格")
            print(f"   可以運行: python update_etf_prices_simple.py {min(result['unpriced'], 200)}")
        else:
            print("   ✅ 所有ETF都已更新價格！")
        
        return True
    else:
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ 程序執行失敗: {str(e)}")
        sys.exit(1)
