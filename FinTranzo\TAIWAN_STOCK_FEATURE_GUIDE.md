# 台股現價功能使用指南

## 🎯 功能概述

這個功能讓您可以：
- 🔍 **搜尋台股**：輸入股票代號或名稱快速搜尋
- 💰 **即時價格**：獲取台股前日收盤價
- 📊 **自動計算**：資產漲跌自動計算
- 🏪 **股票名稱**：自動帶入完整股票名稱

## 📋 設定步驟

### 步驟 1：設定 Supabase 資料庫

1. **登入 Supabase Dashboard**
   - 前往：https://yrryyapzkgrsahranzvo.supabase.co
   - 使用您的帳號登入

2. **執行 SQL 設定**
   - 點擊左側選單的「SQL Editor」
   - 建立新查詢
   - 複製並執行 `supabase_setup.sql` 中的內容

3. **驗證資料表建立**
   - 檢查是否成功建立 `taiwan_stocks` 資料表
   - 確認相關函數和視圖已建立

### 步驟 2：初始化股票資料

1. **開啟台股管理頁面**
   - 在應用程式底部導航點擊「台股管理」
   - 查看當前資料狀態

2. **更新股票資料**
   - 點擊「更新股票資料」按鈕
   - 等待從台灣證交所 API 獲取資料（約 2-3 分鐘）
   - 確認更新完成

3. **測試搜尋功能**
   - 點擊「測試搜尋」按鈕
   - 或在搜尋框輸入「2330」測試台積電

## 🚀 使用方式

### 基本搜尋

1. **開啟股票搜尋**
   - 在台股管理頁面找到搜尋框
   - 或在資產管理中使用股票搜尋組件

2. **輸入搜尋條件**
   ```
   支援格式：
   - 股票代號：2330、2317、2454
   - 股票名稱：台積電、鴻海、聯發科
   - 部分名稱：台積、鴻海
   ```

3. **選擇股票**
   - 從搜尋結果中點擊想要的股票
   - 系統會自動帶入股票資訊

### 資產管理整合

1. **新增股票資產**
   - 在資產管理頁面選擇「股票」類型
   - 使用股票搜尋組件輸入代號
   - 系統自動帶入：
     - 股票名稱
     - 現在市價
     - 計算現在價值

2. **漲跌計算**
   - 成本基礎 = 買入成本價 × 持有數量
   - 現在價值 = 現在市價 × 持有數量
   - 漲跌金額 = 現在價值 - 成本基礎
   - 漲跌百分比 = (漲跌金額 ÷ 成本基礎) × 100%

## 📊 資料來源

### API 資訊
- **來源**：台灣證券交易所 (TWSE)
- **API 端點**：`/exchangeReport/STOCK_DAY_AVG_ALL`
- **更新頻率**：每日（建議每日更新一次）
- **資料內容**：
  - 股票代號
  - 股票名稱
  - 收盤價
  - 月平均價
  - 資料日期

### 資料格式
```json
{
  "Date": "20241230",
  "Code": "2330",
  "Name": "台積電",
  "ClosingPrice": "1000.00",
  "MonthlyAveragePrice": "980.50"
}
```

## 🔧 技術架構

### 服務層
- **TaiwanStockService**：核心股票資料服務
- **StockSearchInput**：股票搜尋組件
- **StockManagementScreen**：管理介面

### 資料庫設計
```sql
-- 主要資料表
taiwan_stocks (
  id, date, code, name, 
  closing_price, monthly_average_price
)

-- 視圖
latest_taiwan_stocks (最新股價視圖)

-- 函數
get_latest_stock_price(股票代號)
search_stocks(搜尋關鍵字)
```

### API 整合
- 自動從 TWSE API 獲取資料
- 批次處理避免 API 限制
- 錯誤處理和重試機制

## 📱 使用介面

### 台股管理頁面
- **資料狀態卡片**：顯示股票數量和最後更新時間
- **操作按鈕**：更新資料、檢查更新、測試搜尋
- **搜尋測試區**：測試股票搜尋功能
- **使用說明**：詳細的操作指引

### 股票搜尋組件
- **即時搜尋**：輸入時即時顯示結果
- **結果列表**：顯示代號、名稱、價格
- **選擇確認**：選中後顯示詳細資訊
- **清除功能**：快速清除搜尋內容

## 🔍 故障排除

### 常見問題

1. **搜尋不到股票**
   - 檢查是否已更新股票資料
   - 確認輸入的代號或名稱正確
   - 嘗試重新更新資料

2. **更新資料失敗**
   - 檢查網路連線
   - 確認 Supabase 連線正常
   - 查看控制台錯誤訊息

3. **價格不是最新**
   - 台股資料為前日收盤價
   - 週末和假日不會有新資料
   - 建議每個交易日更新一次

### 除錯步驟

1. **檢查 Supabase 連線**
   ```javascript
   // 在控制台執行
   console.log('Supabase URL:', process.env.EXPO_PUBLIC_SUPABASE_URL);
   ```

2. **測試 API 連線**
   - 使用「測試搜尋」功能
   - 查看控制台日誌
   - 確認 API 回應正常

3. **檢查資料表**
   - 在 Supabase Dashboard 查看 `taiwan_stocks` 表
   - 確認有資料且格式正確

## 📈 進階功能

### 自動更新機制
- 檢查資料是否需要更新
- 智能判斷更新時機
- 避免重複更新

### 批次處理
- 分批插入避免超時
- 錯誤處理和重試
- 進度追蹤和日誌

### 效能優化
- 資料庫索引優化
- 搜尋結果快取
- API 呼叫限制

## 🔮 未來擴展

### 計劃功能
- **即時股價**：整合即時報價 API
- **技術指標**：移動平均、RSI 等
- **價格提醒**：設定價格到達提醒
- **投資組合**：完整的投資組合管理

### 資料增強
- **歷史價格**：儲存歷史價格資料
- **財務指標**：本益比、股息率等
- **新聞整合**：相關新聞和公告
- **分析工具**：技術分析圖表

## 📞 技術支援

如果遇到問題：
1. 查看控制台日誌
2. 檢查 Supabase 連線狀態
3. 確認 API 金鑰設定
4. 聯繫開發團隊

---

**功能版本：** v1.0.0  
**最後更新：** 2024年12月  
**狀態：** ✅ 已完成並測試
