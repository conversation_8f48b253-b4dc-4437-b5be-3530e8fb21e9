-- =====================================================
-- FinTranzo 函數修正腳本
-- 解決函數返回類型衝突問題
-- 執行日期: 2025-06-01
-- =====================================================

-- 先刪除所有可能衝突的函數
DROP FUNCTION IF EXISTS search_stocks(text, market_type, integer);
DROP FUNCTION IF EXISTS search_stocks(text, varchar, integer);
DROP FUNCTION IF EXISTS search_stocks(text);
DROP FUNCTION IF EXISTS get_stock_stats();
DROP FUNCTION IF EXISTS get_latest_exchange_rate(varchar);
DROP FUNCTION IF EXISTS get_latest_exchange_rate(text);
DROP FUNCTION IF EXISTS get_exchange_rate_by_date(date, varchar);
DROP FUNCTION IF EXISTS get_exchange_rate_by_date(date, text);
DROP FUNCTION IF EXISTS convert_usd_to_twd(decimal, decimal);
DROP FUNCTION IF EXISTS convert_usd_to_twd(numeric, numeric);
DROP FUNCTION IF EXISTS convert_twd_to_usd(decimal, decimal);
DROP FUNCTION IF EXISTS convert_twd_to_usd(numeric, numeric);
DROP FUNCTION IF EXISTS upsert_exchange_rate(date, varchar, decimal, decimal, decimal, decimal);
DROP FUNCTION IF EXISTS upsert_exchange_rate(date, text, numeric, numeric, numeric, numeric);

-- 確保市場類型枚舉存在
DO $$ BEGIN
    CREATE TYPE market_type AS ENUM ('TSE', 'OTC', 'ETF');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- =====================================================
-- 重新創建所有函數
-- =====================================================

-- 1. 搜尋股票函數
CREATE FUNCTION search_stocks(
    search_term TEXT,
    market_filter market_type DEFAULT NULL,
    limit_count INTEGER DEFAULT 50
)
RETURNS TABLE (
    code VARCHAR(10),
    name VARCHAR(100),
    market_type market_type,
    closing_price DECIMAL(10,2),
    change_percent DECIMAL(5,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.code, s.name, s.market_type, s.closing_price, s.change_percent
    FROM taiwan_stocks s
    WHERE 
        (market_filter IS NULL OR s.market_type = market_filter)
        AND (s.code ILIKE '%' || search_term || '%' OR s.name ILIKE '%' || search_term || '%')
    ORDER BY s.volume DESC NULLS LAST
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 2. 獲取股票統計函數
CREATE FUNCTION get_stock_stats()
RETURNS TABLE (
    total_stocks BIGINT,
    tse_count BIGINT,
    otc_count BIGINT,
    etf_count BIGINT,
    last_updated DATE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_stocks,
        COUNT(*) FILTER (WHERE market_type = 'TSE') as tse_count,
        COUNT(*) FILTER (WHERE market_type = 'OTC') as otc_count,
        COUNT(*) FILTER (WHERE market_type = 'ETF') as etf_count,
        MAX(price_date) as last_updated
    FROM taiwan_stocks;
END;
$$ LANGUAGE plpgsql;

-- 3. 獲取最新匯率函數
CREATE FUNCTION get_latest_exchange_rate(
    target_currency VARCHAR(3) DEFAULT 'USD'
)
RETURNS TABLE (
    date DATE,
    currency VARCHAR(3),
    cash_buy DECIMAL(8,3),
    cash_sell DECIMAL(8,3),
    spot_buy DECIMAL(8,3),
    spot_sell DECIMAL(8,3)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        er.date,
        er.currency,
        er.cash_buy,
        er.cash_sell,
        er.spot_buy,
        er.spot_sell
    FROM exchange_rates er
    WHERE er.currency = target_currency
    ORDER BY er.date DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- 4. 獲取指定日期匯率函數
CREATE FUNCTION get_exchange_rate_by_date(
    target_date DATE,
    target_currency VARCHAR(3) DEFAULT 'USD'
)
RETURNS TABLE (
    date DATE,
    currency VARCHAR(3),
    cash_buy DECIMAL(8,3),
    cash_sell DECIMAL(8,3),
    spot_buy DECIMAL(8,3),
    spot_sell DECIMAL(8,3)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        er.date,
        er.currency,
        er.cash_buy,
        er.cash_sell,
        er.spot_buy,
        er.spot_sell
    FROM exchange_rates er
    WHERE er.currency = target_currency
        AND er.date <= target_date
    ORDER BY er.date DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- 5. 美元轉台幣函數
CREATE FUNCTION convert_usd_to_twd(
    usd_amount DECIMAL(15,2),
    exchange_rate DECIMAL(8,3) DEFAULT NULL
)
RETURNS DECIMAL(15,2) AS $$
DECLARE
    rate DECIMAL(8,3);
BEGIN
    IF exchange_rate IS NULL THEN
        SELECT spot_sell INTO rate
        FROM get_latest_exchange_rate('USD')
        LIMIT 1;
        
        IF rate IS NULL THEN
            rate := 29.95; -- 2025-06-01 實際匯率
        END IF;
    ELSE
        rate := exchange_rate;
    END IF;
    
    RETURN usd_amount * rate;
END;
$$ LANGUAGE plpgsql;

-- 6. 台幣轉美元函數
CREATE FUNCTION convert_twd_to_usd(
    twd_amount DECIMAL(15,2),
    exchange_rate DECIMAL(8,3) DEFAULT NULL
)
RETURNS DECIMAL(15,2) AS $$
DECLARE
    rate DECIMAL(8,3);
BEGIN
    IF exchange_rate IS NULL THEN
        SELECT spot_buy INTO rate
        FROM get_latest_exchange_rate('USD')
        LIMIT 1;
        
        IF rate IS NULL THEN
            rate := 29.9; -- 2025-06-01 實際匯率
        END IF;
    ELSE
        rate := exchange_rate;
    END IF;
    
    RETURN twd_amount / rate;
END;
$$ LANGUAGE plpgsql;

-- 7. 插入或更新匯率資料函數
CREATE FUNCTION upsert_exchange_rate(
    target_date DATE,
    target_currency VARCHAR(3),
    cash_buy_rate DECIMAL(8,3),
    cash_sell_rate DECIMAL(8,3),
    spot_buy_rate DECIMAL(8,3),
    spot_sell_rate DECIMAL(8,3)
)
RETURNS UUID AS $$
DECLARE
    rate_id UUID;
BEGIN
    INSERT INTO exchange_rates (
        date, currency, cash_buy, cash_sell, spot_buy, spot_sell
    ) VALUES (
        target_date, target_currency, cash_buy_rate, cash_sell_rate, spot_buy_rate, spot_sell_rate
    )
    ON CONFLICT (date, currency) 
    DO UPDATE SET
        cash_buy = EXCLUDED.cash_buy,
        cash_sell = EXCLUDED.cash_sell,
        spot_buy = EXCLUDED.spot_buy,
        spot_sell = EXCLUDED.spot_sell,
        updated_at = NOW()
    RETURNING id INTO rate_id;
    
    RETURN rate_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 測試函數是否正常工作
-- =====================================================

-- 測試搜尋功能
SELECT '✅ 搜尋測試' as test_type, * FROM search_stocks('2330', NULL, 5);

-- 測試匯率功能
SELECT '✅ 匯率測試' as test_type, * FROM get_latest_exchange_rate('USD');

-- 測試轉換功能
SELECT 
    '✅ 轉換測試' as test_type,
    convert_usd_to_twd(100.00) as usd_100_to_twd,
    convert_twd_to_usd(3120.00) as twd_3120_to_usd;

-- 測試統計功能
SELECT '✅ 統計測試' as test_type, * FROM get_stock_stats();

-- =====================================================
-- 函數修正完成
-- =====================================================

SELECT 
    '🎉 函數修正完成！' as status,
    NOW() as fix_time;
