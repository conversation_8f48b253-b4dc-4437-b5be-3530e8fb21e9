# 每日台股資料自動更新工作流程
name: Daily Taiwan Stock Update

on:
  # 每日台北時間下午 6:00 執行（UTC 10:00）
  schedule:
    - cron: '0 10 * * 1-5'  # 週一到週五執行

  # 允許手動觸發
  workflow_dispatch:
    inputs:
      force_update:
        description: '強制更新（忽略週末限制）'
        required: false
        default: 'false'
        type: boolean

jobs:
  update-stock-data:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: |
        npm install @supabase/supabase-js

    - name: Check if trading day
      id: check_trading_day
      run: |
        # 檢查是否為交易日（週一到週五）
        day_of_week=$(date +%u)
        if [ "$day_of_week" -ge 1 ] && [ "$day_of_week" -le 5 ]; then
          echo "is_trading_day=true" >> $GITHUB_OUTPUT
        else
          echo "is_trading_day=false" >> $GITHUB_OUTPUT
        fi

        # 如果是手動觸發且設置強制更新，則忽略交易日檢查
        if [ "${{ github.event.inputs.force_update }}" = "true" ]; then
          echo "is_trading_day=true" >> $GITHUB_OUTPUT
          echo "強制更新模式，忽略交易日限制"
        fi

    - name: Update Taiwan stock data
      if: steps.check_trading_day.outputs.is_trading_day == 'true'
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
      run: |
        node database/fetch_json_stock_data.js

    - name: Skip non-trading day
      if: steps.check_trading_day.outputs.is_trading_day == 'false'
      run: |
        echo "今天不是交易日，跳過股票資料更新"

    - name: Notify on failure
      if: failure()
      run: |
        echo "股票資料更新失敗，請檢查日誌"
        # 這裡可以添加通知邏輯，例如發送 Slack 或 Discord 訊息
