/**
 * 調試當前狀態腳本
 * 檢查負債添加流程和交易記錄創建的實際情況
 */

console.log('🔍 開始調試當前狀態...');

// 模擬當前的問題場景
function debugLiabilityDateLogic() {
  console.log('\n📅 問題1：負債還款日期邏輯調試');
  console.log('='.repeat(50));
  
  // 模擬用戶設置31號還款日
  const userSetPaymentDay = 31;
  const currentDate = new Date(); // 2025年5月
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth(); // 4 (5月，因為從0開始)
  
  console.log('當前時間:', {
    year: currentYear,
    month: currentMonth + 1, // 顯示為5月
    monthName: currentDate.toLocaleDateString('zh-TW', { month: 'long' }),
    date: currentDate.getDate()
  });
  
  // 檢查5月有多少天
  const lastDayOfMay = new Date(currentYear, currentMonth + 1, 0).getDate();
  console.log('5月最後一天:', lastDayOfMay);
  
  // 模擬原始邏輯（有問題的）
  console.log('\n❌ 原始邏輯（有問題）:');
  const originalDate = new Date(currentYear, currentMonth, userSetPaymentDay);
  console.log('直接創建日期:', {
    input: `new Date(${currentYear}, ${currentMonth}, ${userSetPaymentDay})`,
    result: originalDate.toLocaleDateString('zh-TW'),
    actualDay: originalDate.getDate()
  });
  
  // 模擬修復後的邏輯
  console.log('\n✅ 修復後的邏輯:');
  const lastDayOfCurrentMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
  let actualPaymentDay;
  if (userSetPaymentDay > lastDayOfCurrentMonth) {
    actualPaymentDay = lastDayOfCurrentMonth;
    console.log(`月末調整: 原定${userSetPaymentDay}號 -> ${actualPaymentDay}號`);
  } else {
    actualPaymentDay = userSetPaymentDay;
    console.log(`無需調整: ${userSetPaymentDay}號`);
  }
  
  const fixedDate = new Date(currentYear, currentMonth, actualPaymentDay);
  console.log('修復後日期:', {
    input: `new Date(${currentYear}, ${currentMonth}, ${actualPaymentDay})`,
    result: fixedDate.toLocaleDateString('zh-TW'),
    actualDay: fixedDate.getDate()
  });
}

function debugTransactionFlow() {
  console.log('\n💰 問題2：交易記錄流程調試');
  console.log('='.repeat(50));
  
  // 模擬初始交易數據
  const initialTransactions = [
    {
      id: '1',
      amount: 500,
      type: 'expense',
      description: '午餐',
      category: '餐飲',
      account: '現金',
      date: new Date().toISOString()
    },
    {
      id: '2',
      amount: 80000,
      type: 'income',
      description: '薪水',
      category: '薪水',
      account: '銀行',
      date: new Date().toISOString()
    }
  ];
  
  console.log('初始交易記錄:');
  initialTransactions.forEach((t, i) => {
    console.log(`  ${i + 1}. ${t.description}: ${t.type === 'income' ? '+' : '-'}${t.amount} (${t.category})`);
  });
  
  // 模擬添加負債還款交易
  const newDebtPayment = {
    id: '3',
    amount: 10000,
    type: 'expense',
    description: '房貸',
    category: '還款',
    account: '銀行',
    date: new Date().toISOString(),
    is_recurring: true,
    recurring_frequency: 'monthly'
  };
  
  console.log('\n添加負債還款後:');
  const allTransactions = [...initialTransactions, newDebtPayment];
  allTransactions.forEach((t, i) => {
    console.log(`  ${i + 1}. ${t.description}: ${t.type === 'income' ? '+' : '-'}${t.amount} (${t.category})`);
  });
  
  // 檢查收支分析篩選邏輯
  console.log('\n🔍 收支分析篩選邏輯:');
  
  // 全部交易
  const allFiltered = allTransactions;
  console.log('全部交易:', allFiltered.length, '筆');
  allFiltered.forEach(t => {
    console.log(`  - ${t.description}: ${t.type === 'income' ? '+' : '-'}${t.amount}`);
  });
  
  // 收入交易
  const incomeFiltered = allTransactions.filter(t => t.type === 'income');
  console.log('\n收入交易:', incomeFiltered.length, '筆');
  incomeFiltered.forEach(t => {
    console.log(`  - ${t.description}: +${t.amount}`);
  });
  
  // 支出交易
  const expenseFiltered = allTransactions.filter(t => t.type === 'expense');
  console.log('\n支出交易:', expenseFiltered.length, '筆');
  expenseFiltered.forEach(t => {
    console.log(`  - ${t.description}: -${t.amount}`);
  });
  
  // 檢查還款類別
  const debtPayments = allTransactions.filter(t => t.category === '還款');
  console.log('\n還款交易:', debtPayments.length, '筆');
  debtPayments.forEach(t => {
    console.log(`  - ${t.description}: -${t.amount} (${t.category})`);
  });
}

function debugPossibleIssues() {
  console.log('\n🐛 可能的問題分析');
  console.log('='.repeat(50));
  
  console.log('問題1分析：');
  console.log('- JavaScript的Date構造函數會自動調整無效日期');
  console.log('- new Date(2025, 4, 31) 可能會變成 new Date(2025, 5, 0) = 2025年5月30日');
  console.log('- 需要在創建Date之前先檢查月份的最大天數');
  
  console.log('\n問題2分析：');
  console.log('- 交易記錄可能沒有正確添加到transactionDataService');
  console.log('- 事件監聽器可能沒有正確觸發');
  console.log('- 收支分析頁面的數據可能沒有及時刷新');
  console.log('- 需要檢查整個數據流：負債添加 -> 循環交易創建 -> 實際交易記錄 -> 頁面顯示');
}

// 執行調試
debugLiabilityDateLogic();
debugTransactionFlow();
debugPossibleIssues();

console.log('\n✅ 調試完成');
