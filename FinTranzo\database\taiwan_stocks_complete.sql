-- =====================================================
-- 台股資料庫完整架構 (Supabase 相容版)
-- 支援：上市 (TSE) + 上櫃 (OTC) + ETF
-- 適用於：Supabase PostgreSQL
-- 更新日期：2025-05-29
-- =====================================================

-- 清理舊資料 (如果存在)
DROP TABLE IF EXISTS taiwan_stocks CASCADE;
DROP TYPE IF EXISTS market_type CASCADE;
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS upsert_stock_data(VARCHAR, VARCHAR, market_type, DECIMAL, DECIMAL, DECIMAL, DECIMAL, BIGINT, INTEGER, DECIMAL, DECIMAL, DECIMAL, DATE) CASCADE;
DROP FUNCTION IF EXISTS search_stocks(TEXT, market_type, INTEGER) CASCADE;
DROP VIEW IF EXISTS v_stock_summary CASCADE;
DROP VIEW IF EXISTS v_popular_stocks CASCADE;
DROP VIEW IF EXISTS v_price_movers CASCADE;

-- =====================================================
-- 1. 建立資料類型
-- =====================================================

-- 建立股票市場類型枚舉
CREATE TYPE market_type AS ENUM ('TSE', 'OTC', 'ETF');

-- =====================================================
-- 2. 建立主要資料表
-- =====================================================

CREATE TABLE taiwan_stocks (
    -- 基本資訊
    code VARCHAR(10) PRIMARY KEY,              -- 股票代號 (如: 2330, 00878)
    name VARCHAR(100) NOT NULL,                -- 股票名稱
    market_type market_type NOT NULL,          -- 市場類型 (TSE/OTC/ETF)
    
    -- 價格資訊
    closing_price DECIMAL(10,2),               -- 收盤價
    opening_price DECIMAL(10,2),               -- 開盤價
    highest_price DECIMAL(10,2),               -- 最高價
    lowest_price DECIMAL(10,2),                -- 最低價
    
    -- 交易資訊
    volume BIGINT DEFAULT 0,                   -- 成交量
    transaction_count INTEGER DEFAULT 0,       -- 成交筆數
    turnover DECIMAL(15,2) DEFAULT 0,          -- 成交金額
    
    -- 漲跌資訊
    price_change DECIMAL(10,2) DEFAULT 0,      -- 漲跌價差
    change_percent DECIMAL(5,2) DEFAULT 0,     -- 漲跌幅 (%)
    
    -- 時間戳記
    price_date DATE NOT NULL,                  -- 價格日期
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 約束條件
    CONSTRAINT valid_price CHECK (closing_price >= 0),
    CONSTRAINT valid_volume CHECK (volume >= 0),
    CONSTRAINT valid_change_percent CHECK (change_percent >= -100 AND change_percent <= 100)
);

-- =====================================================
-- 3. 建立索引 (效能優化)
-- =====================================================

-- 基本查詢索引
CREATE INDEX idx_taiwan_stocks_market_type ON taiwan_stocks(market_type);
CREATE INDEX idx_taiwan_stocks_price_date ON taiwan_stocks(price_date);
CREATE INDEX idx_taiwan_stocks_code_date ON taiwan_stocks(code, price_date);

-- 搜尋功能索引 (LIKE 查詢優化)
CREATE INDEX idx_taiwan_stocks_code_search ON taiwan_stocks(code varchar_pattern_ops);
CREATE INDEX idx_taiwan_stocks_name_search ON taiwan_stocks(name varchar_pattern_ops);

-- 排序功能索引
CREATE INDEX idx_taiwan_stocks_price ON taiwan_stocks(closing_price);
CREATE INDEX idx_taiwan_stocks_volume ON taiwan_stocks(volume);
CREATE INDEX idx_taiwan_stocks_change ON taiwan_stocks(change_percent);

-- 複合索引 (常用查詢組合)
CREATE INDEX idx_taiwan_stocks_market_price ON taiwan_stocks(market_type, closing_price);
CREATE INDEX idx_taiwan_stocks_date_volume ON taiwan_stocks(price_date, volume);

-- =====================================================
-- 4. 建立觸發器 (自動更新時間)
-- =====================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_taiwan_stocks_updated_at 
    BEFORE UPDATE ON taiwan_stocks 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 5. 設定 RLS (Row Level Security)
-- =====================================================

-- 啟用 RLS
ALTER TABLE taiwan_stocks ENABLE ROW LEVEL SECURITY;

-- 允許所有人讀取股票資料
CREATE POLICY "Allow public read access" ON taiwan_stocks
    FOR SELECT USING (true);

-- 只允許認證用戶更新資料（用於自動更新腳本）
CREATE POLICY "Allow authenticated updates" ON taiwan_stocks
    FOR ALL USING (auth.role() = 'authenticated');

-- =====================================================
-- 6. 建立實用函數
-- =====================================================

-- 批量更新函數
CREATE OR REPLACE FUNCTION upsert_stock_data(
    p_code VARCHAR(10),
    p_name VARCHAR(100),
    p_market_type market_type,
    p_closing_price DECIMAL(10,2),
    p_opening_price DECIMAL(10,2) DEFAULT NULL,
    p_highest_price DECIMAL(10,2) DEFAULT NULL,
    p_lowest_price DECIMAL(10,2) DEFAULT NULL,
    p_volume BIGINT DEFAULT 0,
    p_transaction_count INTEGER DEFAULT 0,
    p_turnover DECIMAL(15,2) DEFAULT 0,
    p_price_change DECIMAL(10,2) DEFAULT 0,
    p_change_percent DECIMAL(5,2) DEFAULT 0,
    p_price_date DATE DEFAULT CURRENT_DATE
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO taiwan_stocks (
        code, name, market_type, closing_price, opening_price, 
        highest_price, lowest_price, volume, transaction_count, 
        turnover, price_change, change_percent, price_date
    ) VALUES (
        p_code, p_name, p_market_type, p_closing_price, p_opening_price,
        p_highest_price, p_lowest_price, p_volume, p_transaction_count,
        p_turnover, p_price_change, p_change_percent, p_price_date
    )
    ON CONFLICT (code) DO UPDATE SET
        name = EXCLUDED.name,
        market_type = EXCLUDED.market_type,
        closing_price = EXCLUDED.closing_price,
        opening_price = EXCLUDED.opening_price,
        highest_price = EXCLUDED.highest_price,
        lowest_price = EXCLUDED.lowest_price,
        volume = EXCLUDED.volume,
        transaction_count = EXCLUDED.transaction_count,
        turnover = EXCLUDED.turnover,
        price_change = EXCLUDED.price_change,
        change_percent = EXCLUDED.change_percent,
        price_date = EXCLUDED.price_date,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- 智能搜尋函數
CREATE OR REPLACE FUNCTION search_stocks(
    search_term TEXT,
    market_filter market_type DEFAULT NULL,
    limit_count INTEGER DEFAULT 50
)
RETURNS TABLE (
    code VARCHAR(10),
    name VARCHAR(100),
    market_type market_type,
    closing_price DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    volume BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.code,
        s.name,
        s.market_type,
        s.closing_price,
        s.change_percent,
        s.volume
    FROM taiwan_stocks s
    WHERE 
        (market_filter IS NULL OR s.market_type = market_filter)
        AND (
            s.code ILIKE '%' || search_term || '%' 
            OR s.name ILIKE '%' || search_term || '%'
        )
        AND s.price_date = (SELECT MAX(price_date) FROM taiwan_stocks)
    ORDER BY 
        CASE 
            WHEN s.code = search_term THEN 1
            WHEN s.code ILIKE search_term || '%' THEN 2
            WHEN s.name ILIKE search_term || '%' THEN 3
            ELSE 4
        END,
        s.volume DESC NULLS LAST
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 7. 建立查詢視圖
-- =====================================================

-- 市場統計視圖
CREATE OR REPLACE VIEW v_stock_summary AS
SELECT 
    market_type,
    COUNT(*) as stock_count,
    ROUND(AVG(closing_price), 2) as avg_price,
    SUM(volume) as total_volume,
    MAX(price_date) as latest_date,
    MIN(closing_price) as min_price,
    MAX(closing_price) as max_price
FROM taiwan_stocks 
WHERE closing_price IS NOT NULL
GROUP BY market_type;

-- 熱門股票視圖 (依成交量排序)
CREATE OR REPLACE VIEW v_popular_stocks AS
SELECT 
    code,
    name,
    market_type,
    closing_price,
    volume,
    change_percent,
    price_date
FROM taiwan_stocks 
WHERE price_date = (SELECT MAX(price_date) FROM taiwan_stocks)
  AND volume > 0
ORDER BY volume DESC
LIMIT 50;

-- 漲跌幅排行視圖
CREATE OR REPLACE VIEW v_price_movers AS
SELECT 
    code,
    name,
    market_type,
    closing_price,
    price_change,
    change_percent,
    volume,
    price_date
FROM taiwan_stocks 
WHERE price_date = (SELECT MAX(price_date) FROM taiwan_stocks)
  AND change_percent IS NOT NULL
ORDER BY change_percent DESC;

-- 高價股視圖 (收盤價 > 100)
CREATE OR REPLACE VIEW v_high_price_stocks AS
SELECT 
    code,
    name,
    market_type,
    closing_price,
    volume,
    change_percent
FROM taiwan_stocks 
WHERE price_date = (SELECT MAX(price_date) FROM taiwan_stocks)
  AND closing_price > 100
ORDER BY closing_price DESC;

-- ETF 專用視圖
CREATE OR REPLACE VIEW v_etf_stocks AS
SELECT 
    code,
    name,
    closing_price,
    volume,
    change_percent,
    price_date
FROM taiwan_stocks 
WHERE market_type = 'ETF'
  AND price_date = (SELECT MAX(price_date) FROM taiwan_stocks)
ORDER BY volume DESC;

-- =====================================================
-- 8. 插入範例資料
-- =====================================================

INSERT INTO taiwan_stocks (code, name, market_type, closing_price, volume, change_percent, price_date) VALUES
-- 上市股票 (TSE)
('2330', '台積電', 'TSE', 967.00, 50000000, 1.25, CURRENT_DATE),
('2317', '鴻海', 'TSE', 156.00, 30000000, -0.85, CURRENT_DATE),
('2454', '聯發科', 'TSE', 1200.00, 15000000, 2.15, CURRENT_DATE),
('2308', '台達電', 'TSE', 374.00, 8000000, 0.75, CURRENT_DATE),
('2382', '廣達', 'TSE', 285.00, 12000000, -1.25, CURRENT_DATE),
('2395', '研華', 'TSE', 485.00, 3000000, 1.85, CURRENT_DATE),
('3008', '大立光', 'TSE', 2800.00, 500000, -2.15, CURRENT_DATE),
('4938', '和碩', 'TSE', 95.20, 8000000, 0.45, CURRENT_DATE),
('6505', '台塑化', 'TSE', 98.70, 5000000, -0.35, CURRENT_DATE),
('2327', '國巨', 'TSE', 490.00, 4000000, 3.25, CURRENT_DATE),

-- ETF
('0050', '元大台灣50', 'ETF', 179.75, 8000000, 0.95, CURRENT_DATE),
('0056', '元大高股息', 'ETF', 34.08, 25000000, 1.15, CURRENT_DATE),
('00878', '國泰永續高股息', 'ETF', 20.43, 20000000, 0.85, CURRENT_DATE),
('00881', '國泰台灣科技龍頭', 'ETF', 22.26, 15000000, 1.45, CURRENT_DATE),
('00892', '富邦台灣半導體', 'ETF', 15.92, 18000000, 2.25, CURRENT_DATE),
('00757', '統一FANG+', 'ETF', 100.40, 5000000, -1.15, CURRENT_DATE),
('00830', '國泰費城半導體', 'ETF', 38.41, 8000000, 1.85, CURRENT_DATE),
('00646', '元大S&P500', 'ETF', 56.10, 6000000, 0.75, CURRENT_DATE),

-- 上櫃股票 (OTC)
('6488', '環球晶', 'OTC', 168.50, 2000000, 1.25, CURRENT_DATE),
('3711', '日月光投控', 'OTC', 145.00, 8000000, 0.85, CURRENT_DATE),
('4966', '譜瑞-KY', 'OTC', 89.70, 1500000, -1.45, CURRENT_DATE),
('5274', '信驊', 'OTC', 168.50, 2500000, 2.85, CURRENT_DATE),
('6415', '矽力-KY', 'OTC', 285.00, 1800000, 1.65, CURRENT_DATE),
('3034', '聯詠', 'OTC', 580.00, 3000000, -0.95, CURRENT_DATE),
('6239', '力成', 'OTC', 168.50, 4000000, 0.55, CURRENT_DATE),
('3443', '創意', 'OTC', 168.50, 2200000, 1.35, CURRENT_DATE)

ON CONFLICT (code) DO NOTHING;

-- =====================================================
-- 9. 建立註解說明
-- =====================================================

COMMENT ON TABLE taiwan_stocks IS '台灣股票資料表 - 包含上市(TSE)、上櫃(OTC)、ETF 完整資料';
COMMENT ON COLUMN taiwan_stocks.code IS '股票代號 (如: 2330, 00878)';
COMMENT ON COLUMN taiwan_stocks.name IS '股票名稱';
COMMENT ON COLUMN taiwan_stocks.market_type IS '市場類型: TSE(上市), OTC(上櫃), ETF(指數股票型基金)';
COMMENT ON COLUMN taiwan_stocks.closing_price IS '收盤價 (新台幣)';
COMMENT ON COLUMN taiwan_stocks.volume IS '成交量 (股數)';
COMMENT ON COLUMN taiwan_stocks.change_percent IS '漲跌幅百分比';
COMMENT ON COLUMN taiwan_stocks.price_date IS '價格資料日期';

-- =====================================================
-- 10. 驗證與測試查詢
-- =====================================================

-- 基本統計
SELECT 
    '✅ 資料表建立完成' as status,
    COUNT(*) as total_stocks,
    COUNT(DISTINCT market_type) as market_types,
    STRING_AGG(DISTINCT market_type::text, ', ') as available_markets
FROM taiwan_stocks;

-- 市場分布
SELECT 
    '📊 市場分布統計' as info,
    market_type,
    COUNT(*) as count,
    ROUND(AVG(closing_price), 2) as avg_price,
    SUM(volume) as total_volume
FROM taiwan_stocks 
GROUP BY market_type 
ORDER BY count DESC;

-- 索引檢查
SELECT 
    '🔍 索引建立狀況' as info,
    COUNT(*) as index_count
FROM pg_indexes 
WHERE tablename = 'taiwan_stocks';

-- 搜尋功能測試
SELECT '🔍 搜尋功能測試 - 台積' as test_name;
SELECT * FROM search_stocks('台積', NULL, 3);

SELECT '🔍 搜尋功能測試 - ETF' as test_name;
SELECT * FROM search_stocks('元大', 'ETF', 3);

-- 視圖測試
SELECT '📈 市場統計視圖' as view_name;
SELECT * FROM v_stock_summary;

SELECT '🔥 熱門股票視圖 (前5名)' as view_name;
SELECT * FROM v_popular_stocks LIMIT 5;

-- =====================================================
-- 安裝完成提示
-- =====================================================

SELECT 
    '🎉 台股資料庫安裝完成！' as message,
    '包含 ' || COUNT(*) || ' 檔範例股票' as sample_data,
    '支援 TSE + OTC + ETF 完整資料' as features
FROM taiwan_stocks;
