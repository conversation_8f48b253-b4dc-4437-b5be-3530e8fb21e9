import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { bankAccountService } from './src/services/bankAccountService';
import { BankAccountType } from './src/types';
import BankAccountManager from './src/components/BankAccountManager';

export default function BankAccountDemo() {
  const [showBankManager, setShowBankManager] = useState(false);
  const [bankAccounts, setBankAccounts] = useState(bankAccountService.getAllBankAccounts());

  const refreshBankAccounts = () => {
    setBankAccounts(bankAccountService.getAllBankAccounts());
  };

  const handleAddSampleBanks = () => {
    const sampleBanks = [
      { name: '玉山銀行' },
      { name: '國泰世華' },
      { name: '富邦銀行' },
    ];

    sampleBanks.forEach(bank => {
      if (!bankAccountService.isBankNameExists(bank.name)) {
        bankAccountService.createBankAccount({
          name: bank.name,
        });
      }
    });

    refreshBankAccounts();
    Alert.alert('成功', '已添加示例銀行帳戶');
  };

  const handleResetBanks = () => {
    Alert.alert(
      '確認重置',
      '這將重置為預設的銀行帳戶，確定要繼續嗎？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '重置',
          style: 'destructive',
          onPress: () => {
            bankAccountService.resetToDefault();
            refreshBankAccounts();
            Alert.alert('成功', '已重置為預設銀行帳戶');
          },
        },
      ]
    );
  };



  const statistics = bankAccountService.getStatistics();

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>🏦 銀行帳戶管理演示</Text>
        <Text style={styles.subtitle}>測試和演示銀行帳戶的各種功能</Text>
      </View>

      {/* 控制面板 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>控制面板</Text>

        <TouchableOpacity
          style={styles.button}
          onPress={() => {
            setShowBankManager(true);
          }}
        >
          <Ionicons name="settings" size={20} color="#fff" />
          <Text style={styles.buttonText}>打開銀行管理界面</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={handleAddSampleBanks}
        >
          <Ionicons name="add-circle" size={20} color="#007AFF" />
          <Text style={[styles.buttonText, styles.secondaryButtonText]}>添加示例銀行</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.dangerButton]}
          onPress={handleResetBanks}
        >
          <Ionicons name="refresh-circle" size={20} color="#FF3B30" />
          <Text style={[styles.buttonText, styles.dangerButtonText]}>重置為預設</Text>
        </TouchableOpacity>
      </View>

      {/* 統計信息 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>統計信息</Text>

        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{statistics.totalBanks}</Text>
            <Text style={styles.statLabel}>總銀行數</Text>
          </View>

          <View style={styles.statItem}>
            <Text style={styles.statNumber}>
              {statistics.shouldShowSelector ? '是' : '否'}
            </Text>
            <Text style={styles.statLabel}>需要選擇器</Text>
          </View>
        </View>


      </View>

      {/* 銀行帳戶列表 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>當前銀行帳戶 ({bankAccounts.length})</Text>

        {bankAccounts.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="card-outline" size={48} color="#999" />
            <Text style={styles.emptyText}>暫無銀行帳戶</Text>
          </View>
        ) : (
          bankAccounts.map((bank) => (
            <View key={bank.id} style={styles.bankItem}>
              <View style={styles.bankLeft}>
                <View style={styles.bankIcon}>
                  <Text style={styles.bankIconText}>🏦</Text>
                </View>
                <View style={styles.bankInfo}>
                  <Text style={styles.bankName}>{bank.name}</Text>
                  <Text style={styles.bankId}>ID: {bank.id}</Text>
                </View>
              </View>
              <View style={styles.bankRight}>
                <View style={styles.statusBadge}>
                  <Text style={styles.statusText}>啟用</Text>
                </View>
              </View>
            </View>
          ))
        )}
      </View>

      {/* 功能說明 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>功能說明</Text>

        <View style={styles.featureList}>
          <View style={styles.featureItem}>
            <Ionicons name="checkmark-circle" size={20} color="#34C759" />
            <Text style={styles.featureText}>支援多種銀行帳戶類型</Text>
          </View>

          <View style={styles.featureItem}>
            <Ionicons name="checkmark-circle" size={20} color="#34C759" />
            <Text style={styles.featureText}>智能銀行選擇器（超過1個銀行時顯示）</Text>
          </View>

          <View style={styles.featureItem}>
            <Ionicons name="checkmark-circle" size={20} color="#34C759" />
            <Text style={styles.featureText}>與記帳功能完全聯動</Text>
          </View>

          <View style={styles.featureItem}>
            <Ionicons name="checkmark-circle" size={20} color="#34C759" />
            <Text style={styles.featureText}>支援銀行帳戶的增刪改查</Text>
          </View>

          <View style={styles.featureItem}>
            <Ionicons name="checkmark-circle" size={20} color="#34C759" />
            <Text style={styles.featureText}>自動處理單一銀行的情況</Text>
          </View>
        </View>
      </View>

      {/* 銀行管理界面 */}
      <BankAccountManager
        visible={showBankManager}
        onClose={() => {
          setShowBankManager(false);
          refreshBankAccounts();
        }}
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  section: {
    margin: 20,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginBottom: 12,
    gap: 8,
  },
  secondaryButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  dangerButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#FF3B30',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  secondaryButtonText: {
    color: '#007AFF',
  },
  dangerButtonText: {
    color: '#FF3B30',
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
  },
  typeDistribution: {
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    padding: 12,
  },
  distributionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  distributionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  distributionIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  distributionType: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  distributionCount: {
    fontSize: 14,
    fontWeight: '600',
    color: '#007AFF',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    marginTop: 12,
  },
  bankItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  bankLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  bankIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0F8FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  bankIconText: {
    fontSize: 20,
  },
  bankInfo: {
    flex: 1,
  },
  bankName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  bankType: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  bankId: {
    fontSize: 12,
    color: '#999',
  },
  bankRight: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    backgroundColor: '#E8F5E8',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    color: '#34C759',
    fontWeight: '600',
  },
  featureList: {
    gap: 12,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  featureText: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
});
