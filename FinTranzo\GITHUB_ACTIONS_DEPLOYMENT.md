# 🚀 GitHub Actions 自動更新部署指南

## 🎯 為什麼選擇 GitHub Actions？

### 📊 GitHub Actions vs Vercel 比較

| 功能 | GitHub Actions | Vercel 免費版 | 優勢 |
|------|----------------|---------------|------|
| **執行時間** | 6 小時/job | 60 秒 | 🏆 GitHub |
| **每月額度** | 2000 分鐘 | 100 次/天 | 🏆 GitHub |
| **並發執行** | 20 個 jobs | 1 個 | 🏆 GitHub |
| **股票數量** | 無限制 | 受時間限制 | 🏆 GitHub |
| **設置複雜度** | 中等 | 簡單 | Vercel |
| **維護成本** | 低 | 低 | 平手 |

**結論：GitHub Actions 更適合股票數據更新！** 🎉

---

## 📋 已準備的文件

### ✅ 工作流程文件
- `.github/workflows/update-stocks.yml` - 主要工作流程

### ✅ 更新腳本
- `scripts/update-taiwan-stocks.js` - 台股更新
- `scripts/update-us-stocks.js` - 美股更新  
- `scripts/update-exchange-rates.js` - 匯率更新

---

## 🚀 部署步驟

### 步驟1：設置 GitHub Secrets

在您的 GitHub 倉庫中設置環境變數：

1. **前往倉庫設置**：
   - GitHub 倉庫 → Settings → Secrets and variables → Actions

2. **添加必需的 Secrets**：
   ```
   EXPO_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   ```

3. **添加可選的 Secrets**（提升數據質量）：
   ```
   FINMIND_TOKEN=your-finmind-token
   ALPHA_VANTAGE_API_KEY=your-alpha-vantage-key
   ```

### 步驟2：推送代碼到 GitHub

```bash
git add .
git commit -m "Add GitHub Actions for daily stock updates"
git push origin master
```

### 步驟3：啟用 GitHub Actions

1. **檢查 Actions 標籤**：
   - 前往您的 GitHub 倉庫
   - 點擊 "Actions" 標籤
   - 確認工作流程已啟用

2. **手動測試**：
   - 點擊 "📈 每日股票和匯率更新"
   - 點擊 "Run workflow"
   - 選擇更新類型進行測試

---

## ⏰ 自動排程

### 📅 執行時間表

| 功能 | 執行時間 | Cron 表達式 | 說明 |
|------|----------|-------------|------|
| 💱 **匯率更新** | 早上 9:00 | `0 1 * * 1-5` | UTC+8 轉 UTC |
| 🇹🇼 **台股更新** | 下午 3:00 | `0 7 * * 1-5` | 台股收盤後 |
| 🇺🇸 **美股更新** | 晚上 10:00 | `0 14 * * 1-5` | 美股收盤後 |

### 🔄 執行邏輯
- **分別執行**：每個更新類型獨立運行
- **條件觸發**：只在對應時間執行對應任務
- **手動觸發**：支援手動選擇更新類型

---

## 📊 處理能力

### 🇹🇼 台股更新
- **數量**：100 支股票/次
- **批次**：每批 10 支
- **時間**：約 2-3 分鐘
- **覆蓋**：主要上市櫃股票

### 🇺🇸 美股更新  
- **數量**：50 支股票/次
- **處理**：逐一更新
- **時間**：約 1-2 分鐘
- **覆蓋**：S&P 500 主要股票

### 💱 匯率更新
- **數量**：USD/TWD
- **備援**：3 個數據源
- **時間**：約 10 秒
- **可靠性**：99.9%

---

## 🔐 環境變數說明

### 必需變數

#### `EXPO_PUBLIC_SUPABASE_URL`
```
https://your-project.supabase.co
```
- 您的 Supabase 項目 URL
- 在 Supabase 控制台 → Settings → API 中找到

#### `SUPABASE_SERVICE_ROLE_KEY`
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```
- Supabase 服務角色金鑰（有寫入權限）
- 在 Supabase 控制台 → Settings → API 中找到

### 可選變數

#### `FINMIND_TOKEN`
```
your-finmind-token-here
```
- FinMind API Token（台股數據）
- 申請地址：[finmindtrade.com](https://finmindtrade.com/)
- 免費版本有使用限制

#### `ALPHA_VANTAGE_API_KEY`
```
your-alpha-vantage-key-here
```
- Alpha Vantage API 金鑰（美股數據）
- 申請地址：[alphavantage.co](https://www.alphavantage.co/support/#api-key)
- 免費版本：500 次/天

---

## 📈 監控和日誌

### 查看執行狀態

1. **GitHub Actions 頁面**：
   - 倉庫 → Actions → 選擇工作流程
   - 查看執行歷史和狀態

2. **執行日誌**：
   - 點擊具體的執行記錄
   - 查看詳細的執行日誌

3. **錯誤排查**：
   - 紅色 ❌ 表示失敗
   - 點擊查看錯誤詳情

### 成功指標

```
✅ 台股更新完成: 成功 95, 失敗 5
✅ 美股更新完成: 成功 48, 失敗 2  
✅ 匯率更新完成: 成功 1, 失敗 0
```

---

## 🔧 故障排除

### 常見問題

#### 1. 環境變數錯誤
```
❌ 缺少 Supabase 環境變數
```
**解決**：檢查 GitHub Secrets 設置

#### 2. API 限制
```
❌ Alpha Vantage 獲取失敗: API call frequency limit
```
**解決**：等待或升級 API 方案

#### 3. 網路超時
```
❌ 獲取股票失敗: timeout
```
**解決**：GitHub Actions 會自動重試

#### 4. 資料庫連接失敗
```
❌ Supabase 連接失敗
```
**解決**：檢查 Supabase 服務狀態

---

## 💰 成本分析

### GitHub Actions 免費額度
- ✅ **2000 分鐘/月**：足夠每日更新
- ✅ **20 並發 jobs**：可同時執行多個更新
- ✅ **無限制倉庫**：公開倉庫完全免費

### 實際使用量
- 🇹🇼 **台股**：3 分鐘/天 × 22 天 = 66 分鐘/月
- 🇺🇸 **美股**：2 分鐘/天 × 22 天 = 44 分鐘/月
- 💱 **匯率**：0.2 分鐘/天 × 22 天 = 4.4 分鐘/月
- **總計**：約 115 分鐘/月（遠低於 2000 分鐘限制）

---

## 🎯 優化建議

### 1. 增加股票數量
```javascript
// 可以增加到更多股票
.limit(200); // 台股
.limit(100); // 美股
```

### 2. 添加更多數據源
```javascript
// 添加更多備用 API
await fetchFromAPI1() || await fetchFromAPI2() || await fetchFromAPI3();
```

### 3. 智能更新策略
```javascript
// 只更新有變化的股票
const lastUpdate = await getLastUpdateTime();
if (shouldUpdate(lastUpdate)) {
  await updateStock();
}
```

---

## 🎉 部署完成檢查清單

- [ ] ✅ GitHub Secrets 已設置
- [ ] ✅ 代碼已推送到 GitHub
- [ ] ✅ GitHub Actions 已啟用
- [ ] ✅ 手動測試成功
- [ ] ✅ 自動排程正常運行
- [ ] ✅ 監控日誌正常

---

## 📞 技術支援

如果遇到問題：

1. **檢查 GitHub Actions 日誌**
2. **驗證環境變數設置**
3. **測試 Supabase 連接**
4. **檢查 API 金鑰有效性**

**GitHub Actions 部署完成後，您將擁有比 Vercel 更強大的自動更新系統！** 🚀
