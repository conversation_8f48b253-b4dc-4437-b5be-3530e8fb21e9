/**
 * 部署前檢查腳本
 * 驗證所有必需的文件和配置是否正確
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 檢查部署準備狀況...\n');

// 檢查必需的文件
const requiredFiles = [
  'vercel.json',
  'api/update-taiwan-stocks.js',
  'api/update-us-stocks.js', 
  'api/update-exchange-rates.js',
  'package.json'
];

let allFilesExist = true;

console.log('📁 檢查必需文件:');
requiredFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  const exists = fs.existsSync(filePath);
  console.log(`  ${exists ? '✅' : '❌'} ${file}`);
  if (!exists) allFilesExist = false;
});

// 檢查 package.json 依賴
console.log('\n📦 檢查依賴:');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const requiredDeps = ['@supabase/supabase-js'];
  
  requiredDeps.forEach(dep => {
    const exists = packageJson.dependencies && packageJson.dependencies[dep];
    console.log(`  ${exists ? '✅' : '❌'} ${dep}`);
    if (!exists) allFilesExist = false;
  });
} catch (error) {
  console.log('  ❌ 無法讀取 package.json');
  allFilesExist = false;
}

// 檢查 vercel.json 配置
console.log('\n⚙️ 檢查 Vercel 配置:');
try {
  const vercelConfig = JSON.parse(fs.readFileSync('vercel.json', 'utf8'));
  
  const hasFunctions = vercelConfig.functions && Object.keys(vercelConfig.functions).length > 0;
  const hasCrons = vercelConfig.crons && vercelConfig.crons.length > 0;
  
  console.log(`  ${hasFunctions ? '✅' : '❌'} Functions 配置`);
  console.log(`  ${hasCrons ? '✅' : '❌'} Cron Jobs 配置`);
  
  if (hasCrons) {
    console.log('  📅 Cron 排程:');
    vercelConfig.crons.forEach(cron => {
      console.log(`    - ${cron.path}: ${cron.schedule}`);
    });
  }
} catch (error) {
  console.log('  ❌ 無法讀取 vercel.json');
  allFilesExist = false;
}

// 環境變數提醒
console.log('\n🔐 需要設定的環境變數:');
const envVars = [
  { name: 'SUPABASE_URL', required: true, description: 'Supabase 項目 URL' },
  { name: 'SUPABASE_ANON_KEY', required: true, description: 'Supabase 匿名金鑰' },
  { name: 'ALPHA_VANTAGE_API_KEY', required: false, description: '美股數據 API 金鑰' },
  { name: 'FINMIND_TOKEN', required: false, description: '台股數據 Token' },
  { name: 'CRON_SECRET', required: false, description: 'Cron Job 安全金鑰' }
];

envVars.forEach(env => {
  const status = env.required ? '🔴 必需' : '🟡 可選';
  console.log(`  ${status} ${env.name}: ${env.description}`);
});

// 總結
console.log('\n📋 部署準備總結:');
if (allFilesExist) {
  console.log('✅ 所有必需文件都已準備完成');
  console.log('🚀 可以開始部署到 Vercel');
  console.log('\n📖 請參考 DEPLOYMENT_GUIDE.md 進行部署');
} else {
  console.log('❌ 部分文件缺失，請檢查上述問題');
  console.log('🔧 修復問題後再次運行此腳本');
}

console.log('\n🌐 部署步驟:');
console.log('1. 前往 https://vercel.com 註冊帳號');
console.log('2. 連接您的 GitHub 倉庫');
console.log('3. 設定環境變數');
console.log('4. 部署項目');
console.log('5. 測試 API 端點');

process.exit(allFilesExist ? 0 : 1);
