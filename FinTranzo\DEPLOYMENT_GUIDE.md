# 🚀 FinTranzo 每日自動更新部署指南

## 📋 概述

本指南將幫助您在 Vercel 上部署台股、美股和匯率的每日自動更新功能。

## 🛠️ 準備工作

### 1. 確認文件結構
確保您的項目包含以下文件：
```
FinTranzo/
├── vercel.json                    # Vercel 配置
├── api/
│   ├── update-taiwan-stocks.js    # 台股更新 API
│   ├── update-us-stocks.js        # 美股更新 API
│   └── update-exchange-rates.js   # 匯率更新 API
└── package.json                   # 依賴配置
```

### 2. 環境變數準備
您需要準備以下環境變數：

#### 必需的環境變數：
- `SUPABASE_URL` - 您的 Supabase 項目 URL
- `SUPABASE_ANON_KEY` - Supabase 匿名金鑰

#### 可選的環境變數：
- `ALPHA_VANTAGE_API_KEY` - Alpha Vantage API 金鑰（美股數據）
- `FINMIND_TOKEN` - FinMind API Token（台股數據）
- `CRON_SECRET` - <PERSON>ron Job 安全金鑰（自定義）

## 🚀 部署步驟

### 步驟1：註冊 Vercel 帳號
1. 前往 [vercel.com](https://vercel.com)
2. 點擊 "Sign Up" 註冊帳號
3. 建議使用 GitHub 帳號登入

### 步驟2：連接 GitHub 倉庫
1. 將您的 FinTranzo 項目推送到 GitHub
2. 在 Vercel 控制台點擊 "New Project"
3. 選擇您的 FinTranzo GitHub 倉庫
4. 點擊 "Import"

### 步驟3：配置環境變數
1. 在項目設置中找到 "Environment Variables"
2. 添加以下環境變數：

```
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
CRON_SECRET=your-secret-key-123
```

### 步驟4：部署項目
1. 點擊 "Deploy" 開始部署
2. 等待部署完成（通常需要1-3分鐘）
3. 部署成功後會獲得一個 URL

## ⏰ Cron Jobs 設定

### 自動排程（已配置）：
- **台股更新**：每個工作日下午3點（台股收盤後）
- **美股更新**：每個工作日晚上10點（美股收盤後）  
- **匯率更新**：每個工作日早上9點

### 手動測試：
部署完成後，您可以手動測試 API：

```bash
# 測試台股更新
curl -X POST https://your-app.vercel.app/api/update-taiwan-stocks

# 測試美股更新  
curl -X POST https://your-app.vercel.app/api/update-us-stocks

# 測試匯率更新
curl -X POST https://your-app.vercel.app/api/update-exchange-rates
```

## 🔧 API 金鑰申請

### Alpha Vantage（美股數據）：
1. 前往 [alphavantage.co](https://www.alphavantage.co/support/#api-key)
2. 免費註冊獲得 API 金鑰
3. 每日限制：500 次請求

### FinMind（台股數據）：
1. 前往 [finmindtrade.com](https://finmindtrade.com/)
2. 註冊並獲得 Token
3. 免費版本有使用限制

## 📊 監控和日誌

### 查看執行日誌：
1. 在 Vercel 控制台進入您的項目
2. 點擊 "Functions" 標籤
3. 查看各個 API 的執行日誌

### 監控執行狀態：
- 成功執行會返回 200 狀態碼
- 失敗會返回 500 狀態碼並包含錯誤信息

## 🔍 故障排除

### 常見問題：

#### 1. 環境變數錯誤
**症狀**：API 返回 "Missing Supabase environment variables"
**解決**：檢查 Vercel 環境變數設定

#### 2. API 限制
**症狀**：部分股票更新失敗
**解決**：檢查 API 金鑰額度，考慮升級方案

#### 3. 資料庫連接失敗
**症狀**：Supabase 連接錯誤
**解決**：檢查 Supabase URL 和金鑰是否正確

### 調試步驟：
1. 檢查 Vercel 函數日誌
2. 驗證環境變數設定
3. 測試 Supabase 連接
4. 檢查 API 金鑰有效性

## 💰 成本估算

### Vercel 免費方案：
- ✅ 100GB 頻寬/月
- ✅ 100 次函數執行/天
- ✅ 10 秒函數執行時間
- ✅ 足夠支援每日更新需求

### API 成本：
- **Alpha Vantage**：免費 500 次/天
- **FinMind**：免費版本有限制
- **匯率 API**：完全免費

## 🎯 下一步

部署完成後：
1. 監控首次執行結果
2. 檢查資料庫更新狀況
3. 根據需要調整更新頻率
4. 考慮添加更多股票到更新列表

## 📞 技術支援

如果遇到問題：
1. 檢查 Vercel 函數日誌
2. 查看 Supabase 資料庫狀態
3. 驗證 API 金鑰有效性
4. 聯繫技術支援團隊
