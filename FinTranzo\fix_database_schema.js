// 修正數據庫表結構的腳本
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://yrryyapzkgrsahranzvo.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlycnl5YXB6a2dyc2FocmFuenZvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxNzM2MzUsImV4cCI6MjA2Mzc0OTYzNX0.TccJJ9KGG6R4KiaDb-548kRkhTaPMODYa6vlQsj8dmM';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkTableStructure() {
  console.log('🔍 檢查 taiwan_stocks 表結構...');
  
  try {
    // 使用 information_schema 查詢表結構
    const { data, error } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_name', 'taiwan_stocks')
      .eq('table_schema', 'public');
    
    if (error) {
      console.error('❌ 無法查詢表結構:', error.message);
      return null;
    }
    
    if (!data || data.length === 0) {
      console.log('❌ taiwan_stocks 表不存在');
      return null;
    }
    
    console.log('✅ taiwan_stocks 表結構:');
    data.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });
    
    return data;
  } catch (error) {
    console.error('❌ 檢查表結構失敗:', error.message);
    return null;
  }
}

async function createCorrectTable() {
  console.log('🔄 創建正確的 taiwan_stocks 表...');
  
  const createTableSQL = `
    -- 刪除現有表（如果存在）
    DROP TABLE IF EXISTS taiwan_stocks CASCADE;
    
    -- 創建新表
    CREATE TABLE taiwan_stocks (
      id SERIAL PRIMARY KEY,
      date DATE NOT NULL,
      code VARCHAR(10) NOT NULL,
      name VARCHAR(100) NOT NULL,
      closing_price DECIMAL(10,2),
      monthly_average_price DECIMAL(10,2),
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      
      -- 建立複合唯一索引
      UNIQUE(date, code)
    );
    
    -- 建立索引
    CREATE INDEX idx_taiwan_stocks_code ON taiwan_stocks(code);
    CREATE INDEX idx_taiwan_stocks_date ON taiwan_stocks(date);
    CREATE INDEX idx_taiwan_stocks_code_date ON taiwan_stocks(code, date);
    
    -- 建立更新時間觸發器
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
    END;
    $$ language 'plpgsql';
    
    CREATE TRIGGER update_taiwan_stocks_updated_at 
        BEFORE UPDATE ON taiwan_stocks 
        FOR EACH ROW 
        EXECUTE FUNCTION update_updated_at_column();
    
    -- 建立 RLS 政策
    ALTER TABLE taiwan_stocks ENABLE ROW LEVEL SECURITY;
    
    -- 允許所有人讀取
    CREATE POLICY "Allow public read access" ON taiwan_stocks
      FOR SELECT USING (true);
    
    -- 建立搜尋函數
    CREATE OR REPLACE FUNCTION search_stocks(search_term TEXT)
    RETURNS TABLE(
      code VARCHAR(10),
      name VARCHAR(100),
      closing_price DECIMAL(10,2),
      monthly_average_price DECIMAL(10,2),
      price_date DATE
    ) AS $$
    BEGIN
      RETURN QUERY
      SELECT DISTINCT ON (ts.code)
        ts.code,
        ts.name,
        ts.closing_price,
        ts.monthly_average_price,
        ts.date as price_date
      FROM taiwan_stocks ts
      WHERE ts.code ILIKE '%' || search_term || '%' 
         OR ts.name ILIKE '%' || search_term || '%'
      ORDER BY ts.code, ts.date DESC;
    END;
    $$ LANGUAGE plpgsql;
    
    -- 建立獲取最新股價函數
    CREATE OR REPLACE FUNCTION get_latest_stock_price(stock_code TEXT)
    RETURNS TABLE(
      code VARCHAR(10),
      name VARCHAR(100),
      closing_price DECIMAL(10,2),
      monthly_average_price DECIMAL(10,2),
      price_date DATE
    ) AS $$
    BEGIN
      RETURN QUERY
      SELECT 
        ts.code,
        ts.name,
        ts.closing_price,
        ts.monthly_average_price,
        ts.date as price_date
      FROM taiwan_stocks ts
      WHERE ts.code = stock_code
      ORDER BY ts.date DESC
      LIMIT 1;
    END;
    $$ LANGUAGE plpgsql;
  `;
  
  try {
    const { error } = await supabase.rpc('exec_sql', { sql: createTableSQL });
    
    if (error) {
      console.error('❌ 創建表失敗:', error.message);
      return false;
    }
    
    console.log('✅ 成功創建 taiwan_stocks 表');
    return true;
  } catch (error) {
    console.error('❌ 創建表失敗:', error.message);
    return false;
  }
}

async function insertSampleData() {
  console.log('🔄 插入範例資料...');
  
  const sampleData = [
    {
      date: '2024-12-30',
      code: '2330',
      name: '台積電',
      closing_price: 1000.00,
      monthly_average_price: 995.50
    },
    {
      date: '2024-12-30',
      code: '2317',
      name: '鴻海',
      closing_price: 180.50,
      monthly_average_price: 178.20
    },
    {
      date: '2024-12-30',
      code: '2454',
      name: '聯發科',
      closing_price: 1200.00,
      monthly_average_price: 1180.30
    },
    {
      date: '2024-12-30',
      code: '2881',
      name: '富邦金',
      closing_price: 85.20,
      monthly_average_price: 83.50
    },
    {
      date: '2024-12-30',
      code: '2412',
      name: '中華電',
      closing_price: 125.50,
      monthly_average_price: 124.80
    }
  ];
  
  try {
    const { data, error } = await supabase
      .from('taiwan_stocks')
      .insert(sampleData);
    
    if (error) {
      console.error('❌ 插入範例資料失敗:', error.message);
      return false;
    }
    
    console.log('✅ 成功插入範例資料');
    return true;
  } catch (error) {
    console.error('❌ 插入範例資料失敗:', error.message);
    return false;
  }
}

async function testSearch() {
  console.log('🔍 測試搜尋功能...');
  
  try {
    const { data, error } = await supabase
      .rpc('search_stocks', { search_term: '2330' });
    
    if (error) {
      console.error('❌ 搜尋測試失敗:', error.message);
      return false;
    }
    
    console.log('✅ 搜尋功能正常');
    if (data && data.length > 0) {
      console.log('📊 搜尋結果:');
      data.forEach((stock, index) => {
        console.log(`  ${index + 1}. ${stock.code} ${stock.name} - NT$${stock.closing_price}`);
      });
    }
    
    return true;
  } catch (error) {
    console.error('❌ 搜尋測試失敗:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 開始修正數據庫表結構\n');
  
  // 1. 檢查現有表結構
  await checkTableStructure();
  
  console.log('\n⚠️ 注意：以下操作將重新創建 taiwan_stocks 表');
  console.log('如果表中有重要資料，請先備份！\n');
  
  // 2. 創建正確的表結構（這需要在 Supabase SQL Editor 中手動執行）
  console.log('📋 請在 Supabase SQL Editor 中執行以下 SQL：');
  console.log('---');
  console.log(`
-- 刪除現有表（如果存在）
DROP TABLE IF EXISTS taiwan_stocks CASCADE;

-- 創建新表
CREATE TABLE taiwan_stocks (
  id SERIAL PRIMARY KEY,
  date DATE NOT NULL,
  code VARCHAR(10) NOT NULL,
  name VARCHAR(100) NOT NULL,
  closing_price DECIMAL(10,2),
  monthly_average_price DECIMAL(10,2),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 建立複合唯一索引
  UNIQUE(date, code)
);

-- 建立索引
CREATE INDEX idx_taiwan_stocks_code ON taiwan_stocks(code);
CREATE INDEX idx_taiwan_stocks_date ON taiwan_stocks(date);

-- 建立 RLS 政策
ALTER TABLE taiwan_stocks ENABLE ROW LEVEL SECURITY;

-- 允許所有人讀取
CREATE POLICY "Allow public read access" ON taiwan_stocks
  FOR SELECT USING (true);

-- 建立搜尋函數
CREATE OR REPLACE FUNCTION search_stocks(search_term TEXT)
RETURNS TABLE(
  code VARCHAR(10),
  name VARCHAR(100),
  closing_price DECIMAL(10,2),
  monthly_average_price DECIMAL(10,2),
  price_date DATE
) AS $$
BEGIN
  RETURN QUERY
  SELECT DISTINCT ON (ts.code)
    ts.code,
    ts.name,
    ts.closing_price,
    ts.monthly_average_price,
    ts.date as price_date
  FROM taiwan_stocks ts
  WHERE ts.code ILIKE '%' || search_term || '%' 
     OR ts.name ILIKE '%' || search_term || '%'
  ORDER BY ts.code, ts.date DESC;
END;
$$ LANGUAGE plpgsql;
  `);
  console.log('---\n');
  
  console.log('執行完 SQL 後，再次運行此腳本來插入測試資料');
}

main().catch(console.error);
