/**
 * 簡單測試：檢查還款交易是否被正確創建
 */

console.log('🔍 簡單測試：檢查還款交易創建');

// 模擬負債數據
const testLiability = {
  id: 'test_001',
  name: '測試信用卡',
  type: 'credit_card',
  balance: 50000,
  monthly_payment: 10000,
  payment_account: '銀行',
  payment_day: 31,
  payment_periods: 12
};

console.log('測試負債:', testLiability.name);
console.log('月付金:', testLiability.monthly_payment);
console.log('還款日:', testLiability.payment_day);

// 模擬交易創建邏輯
const currentDate = new Date();
const currentYear = currentDate.getFullYear();
const currentMonth = currentDate.getMonth();
const currentDay = currentDate.getDate();

console.log('當前日期:', `${currentYear}年${currentMonth + 1}月${currentDay}日`);

// 檢查是否需要調整日期
const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
const paymentDay = testLiability.payment_day;

let actualPaymentDay;
if (paymentDay > lastDayOfMonth) {
  actualPaymentDay = lastDayOfMonth;
  console.log(`日期調整: ${paymentDay}號 → ${actualPaymentDay}號`);
} else {
  actualPaymentDay = paymentDay;
  console.log(`日期正常: ${paymentDay}號`);
}

// 創建交易日期（使用修復後的邏輯）
const transactionDate = new Date(currentYear, currentMonth, actualPaymentDay, 12, 0, 0, 0);

console.log('交易日期:', transactionDate.toLocaleDateString('zh-TW'));
console.log('ISO日期:', transactionDate.toISOString());
console.log('日期鍵:', transactionDate.toISOString().split('T')[0]);

// 模擬交易記錄
const transaction = {
  id: `debt_${Date.now()}`,
  amount: testLiability.monthly_payment,
  type: 'expense',
  description: testLiability.name,
  category: '還款',
  account: testLiability.payment_account,
  date: transactionDate.toISOString()
};

console.log('\n創建的交易記錄:');
console.log('ID:', transaction.id);
console.log('金額:', transaction.amount);
console.log('類別:', transaction.category);
console.log('描述:', transaction.description);
console.log('日期:', new Date(transaction.date).toLocaleDateString('zh-TW'));

// 檢查關鍵問題
console.log('\n關鍵檢查:');
console.log('1. 交易類型是否為 expense:', transaction.type === 'expense');
console.log('2. 交易類別是否為 還款:', transaction.category === '還款');
console.log('3. 交易金額是否正確:', transaction.amount === 10000);
console.log('4. 日期是否為31號:', new Date(transaction.date).getDate() === 31);

console.log('\n✅ 簡單測試完成');
console.log('如果以上都正確，問題可能在於:');
console.log('1. 交易沒有被添加到 transactionDataService');
console.log('2. 收支分析沒有正確獲取交易數據');
console.log('3. 事件監聽沒有正確觸發');
