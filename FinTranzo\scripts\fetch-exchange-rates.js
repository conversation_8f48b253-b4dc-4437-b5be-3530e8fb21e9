/**
 * 匯率獲取腳本
 * 從 FinMind API 獲取美元兌台幣匯率並儲存到 Supabase
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Supabase 配置
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ 請設置 EXPO_PUBLIC_SUPABASE_URL 和 SUPABASE_SERVICE_ROLE_KEY 環境變數');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

class ExchangeRateFetcher {
  constructor() {
    this.API_URL = 'https://api.finmindtrade.com/api/v3/data';
  }

  /**
   * 獲取指定日期的美元匯率
   */
  async fetchUSDRate(date) {
    try {
      console.log(`🔄 獲取 ${date} 的美元匯率...`);

      const params = {
        dataset: 'TaiwanExchangeRate',
        data_id: 'USD',
        date: date, // 格式: 2025-06-01
      };

      const queryString = new URLSearchParams(params).toString();
      const response = await fetch(`${this.API_URL}?${queryString}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.status !== 200 || !data.data || data.data.length === 0) {
        console.warn(`⚠️ ${date} 無匯率資料`);
        return null;
      }

      // 取得當日最新的匯率資料
      const rateData = data.data[data.data.length - 1];
      
      console.log(`✅ 成功獲取 ${date} 匯率:`, {
        cash_buy: rateData.cash_buy,
        cash_sell: rateData.cash_sell,
        spot_buy: rateData.spot_buy,
        spot_sell: rateData.spot_sell
      });

      return {
        date: rateData.date,
        currency: rateData.currency,
        cash_buy: parseFloat(rateData.cash_buy),
        cash_sell: parseFloat(rateData.cash_sell),
        spot_buy: parseFloat(rateData.spot_buy),
        spot_sell: parseFloat(rateData.spot_sell)
      };
    } catch (error) {
      console.error(`❌ 獲取 ${date} 匯率失敗:`, error.message);
      return null;
    }
  }

  /**
   * 獲取最近N天的匯率資料
   */
  async fetchRecentRates(days = 7) {
    const rates = [];
    const today = new Date();

    console.log(`🚀 開始獲取最近 ${days} 天的匯率資料...`);

    for (let i = 0; i < days; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateString = date.toISOString().split('T')[0];

      const rate = await this.fetchUSDRate(dateString);
      if (rate) {
        rates.push(rate);
      }

      // 避免 API 限制，每次請求間隔 500ms
      if (i < days - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    console.log(`✅ 成功獲取 ${rates.length} 天的匯率資料`);
    return rates;
  }

  /**
   * 儲存匯率資料到 Supabase
   */
  async saveRatesToDatabase(rates) {
    try {
      console.log(`💾 開始儲存 ${rates.length} 筆匯率資料到資料庫...`);

      let successCount = 0;
      let errorCount = 0;

      for (const rate of rates) {
        try {
          // 使用 upsert 避免重複資料
          const { data, error } = await supabase
            .from('exchange_rates')
            .upsert({
              date: rate.date,
              currency: rate.currency,
              cash_buy: rate.cash_buy,
              cash_sell: rate.cash_sell,
              spot_buy: rate.spot_buy,
              spot_sell: rate.spot_sell
            }, {
              onConflict: 'date,currency'
            });

          if (error) {
            console.error(`❌ 儲存 ${rate.date} 匯率失敗:`, error.message);
            errorCount++;
          } else {
            console.log(`✅ 成功儲存 ${rate.date} 匯率`);
            successCount++;
          }
        } catch (error) {
          console.error(`❌ 儲存 ${rate.date} 匯率時發生錯誤:`, error.message);
          errorCount++;
        }
      }

      console.log(`\n📊 儲存結果:`);
      console.log(`   成功: ${successCount} 筆`);
      console.log(`   失敗: ${errorCount} 筆`);
      console.log(`   總計: ${rates.length} 筆`);

      return { successCount, errorCount, total: rates.length };
    } catch (error) {
      console.error('❌ 儲存匯率資料失敗:', error);
      throw error;
    }
  }

  /**
   * 獲取當日匯率
   */
  async fetchTodayRate() {
    const today = new Date().toISOString().split('T')[0];
    return await this.fetchUSDRate(today);
  }

  /**
   * 檢查資料庫中的匯率資料狀態
   */
  async checkDatabaseStatus() {
    try {
      console.log('🔍 檢查資料庫匯率資料狀態...');

      // 獲取總記錄數
      const { count, error: countError } = await supabase
        .from('exchange_rates')
        .select('*', { count: 'exact', head: true });

      if (countError) {
        throw countError;
      }

      // 獲取最新記錄
      const { data: latestData, error: latestError } = await supabase
        .from('exchange_rates')
        .select('*')
        .order('date', { ascending: false })
        .limit(1);

      if (latestError) {
        throw latestError;
      }

      console.log(`📊 資料庫狀態:`);
      console.log(`   總記錄數: ${count} 筆`);
      
      if (latestData && latestData.length > 0) {
        const latest = latestData[0];
        console.log(`   最新日期: ${latest.date}`);
        console.log(`   最新匯率: 買入 ${latest.spot_buy}, 賣出 ${latest.spot_sell}`);
      } else {
        console.log(`   無匯率資料`);
      }

      return { count, latest: latestData?.[0] || null };
    } catch (error) {
      console.error('❌ 檢查資料庫狀態失敗:', error);
      throw error;
    }
  }

  /**
   * 執行完整的匯率更新流程
   */
  async execute(days = 7) {
    try {
      console.log('🚀 開始執行匯率更新流程...');
      console.log(`⏰ 開始時間: ${new Date().toLocaleString()}`);

      // 檢查資料庫狀態
      await this.checkDatabaseStatus();

      // 獲取匯率資料
      const rates = await this.fetchRecentRates(days);

      if (rates.length === 0) {
        console.warn('⚠️ 沒有獲取到任何匯率資料');
        return;
      }

      // 儲存到資料庫
      const result = await this.saveRatesToDatabase(rates);

      console.log('\n🎉 匯率更新完成！');
      console.log(`📊 處理結果: 成功 ${result.successCount} 筆，失敗 ${result.errorCount} 筆`);
      console.log(`⏰ 完成時間: ${new Date().toLocaleString()}`);

      // 再次檢查資料庫狀態
      console.log('\n📋 更新後狀態:');
      await this.checkDatabaseStatus();

    } catch (error) {
      console.error('❌ 執行匯率更新失敗:', error);
      throw error;
    }
  }
}

// 主執行函數
async function main() {
  const fetcher = new ExchangeRateFetcher();
  
  // 從命令列參數獲取天數，預設為 7 天
  const days = parseInt(process.argv[2]) || 7;
  
  try {
    await fetcher.execute(days);
  } catch (error) {
    console.error('❌ 程式執行失敗:', error);
    process.exit(1);
  }
}

// 如果直接執行此腳本
if (require.main === module) {
  main();
}

module.exports = ExchangeRateFetcher;
