# 保單資產功能實現記錄 (2024-12-19)

## 📝 功能概述

本次更新在新增資產頁面添加了"保單"資產類型，並實現了完整的保單管理功能，包括壽險額度字段和在資產負債頁面的特殊顯示邏輯。

## 🔧 具體實現

### 1. 新增資產類型 - 保單

#### 修改位置
- 文件：`src/components/AddAssetModal.tsx`
- 位置：資產類型選擇器

#### 實現內容

**資產類型列表更新**：
```typescript
const assetTypes = [
  { key: 'cash', label: '現金', icon: '💵' },
  { key: 'bank', label: '銀行', icon: '🏦' },
  { key: 'tw_stock', label: '台股', icon: '📈' },
  { key: 'us_stock', label: '美股', icon: '🇺🇸' },
  { key: 'mutual_fund', label: '基金', icon: '📊' },
  { key: 'insurance', label: '保單', icon: '🛡️' }, // 新增
  { key: 'cryptocurrency', label: '加密貨幣', icon: '₿' },
  { key: 'real_estate', label: '不動產', icon: '🏠' },
  { key: 'vehicle', label: '汽車', icon: '🚗' },
  { key: 'other', label: '其他', icon: '💼' },
];
```

**位置調整**：保單放置在加密貨幣的左邊，符合用戶要求。

### 2. 壽險額度字段

#### 新增狀態變量
```typescript
const [insuranceAmount, setInsuranceAmount] = useState(''); // 壽險額度
```

#### 表單字段
- **位置**：在"現在價值"下方
- **標題**：壽險額度
- **輸入類型**：數字鍵盤
- **顯示條件**：僅當資產類型為'insurance'時顯示

#### 表單驗證
- 保單類型包含在需要現在價值的資產類型列表中
- 支持完整的表單驗證和重置邏輯

### 3. 數據結構更新

#### AssetData接口擴展
```typescript
export interface AssetData {
  // ... 現有字段
  // 保單專用字段
  insurance_amount?: number;
}
```

#### 資產對象構建
```typescript
const asset = {
  // ... 現有字段
  // 保單專用字段
  insurance_amount: type === 'insurance' ? parseFloat(insuranceAmount) : undefined,
};
```

### 4. 資產負債頁面顯示

#### 修改位置
- 文件：`src/screens/main/BalanceSheetScreen.tsx`

#### 顯示邏輯
保單在資產負債頁面顯示以下信息：
1. **成本基礎** - 保單的投入成本
2. **當前價值** - 保單的現在價值
3. **損益** - 計算收益率和絕對損益
4. **壽險額度** - 保險保障金額（僅當有值時顯示）

#### 實現代碼
```typescript
asset.type === 'insurance' ? (
  // 保單顯示成本基礎、當前價值、損益和壽險額度
  <>
    <View style={styles.detailRow}>
      <Text style={styles.detailLabel}>成本基礎</Text>
      <Text style={styles.detailValue}>{formatCurrency(asset.cost_basis)}</Text>
    </View>
    <View style={styles.detailRow}>
      <Text style={styles.detailLabel}>當前價值</Text>
      <Text style={styles.detailValue}>{formatCurrency(asset.current_value)}</Text>
    </View>
    <View style={styles.detailRow}>
      <Text style={styles.detailLabel}>損益</Text>
      <Text style={[
        styles.detailValue,
        (asset.current_value - asset.cost_basis) >= 0 ? styles.gainText : styles.lossText
      ]}>
        {(asset.current_value - asset.cost_basis) >= 0 ? '+' : ''}
        {formatCurrency(asset.current_value - asset.cost_basis)}
        ({((asset.current_value - asset.cost_basis) / asset.cost_basis * 100).toFixed(1)}%)
      </Text>
    </View>
    {(asset as any).insurance_amount && (
      <View style={styles.detailRow}>
        <Text style={styles.detailLabel}>壽險額度</Text>
        <Text style={styles.detailValue}>{formatCurrency((asset as any).insurance_amount)}</Text>
      </View>
    )}
  </>
) : // ... 其他資產類型
```

### 5. 類型標籤更新

#### 修改文件
- `src/screens/main/BalanceSheetScreen.tsx`
- `src/components/AddTransactionModal.tsx`

#### 標籤映射
```typescript
const labels: Record<string, string> = {
  // ... 其他類型
  'insurance': '保單', // 更新為'保單'而非'保險'
  // ... 其他類型
};
```

## 🎯 用戶體驗設計

### 1. **直觀的位置安排**
- 保單放置在加密貨幣左邊，符合用戶習慣
- 使用🛡️圖標，直觀表示保護/保險概念

### 2. **完整的信息展示**
- 成本基礎：用戶投入的保費
- 當前價值：保單的現金價值
- 損益：投資收益情況
- 壽險額度：保險保障金額

### 3. **靈活的字段顯示**
- 壽險額度僅在有值時顯示，避免空白行
- 與其他資產類型保持一致的顯示風格

### 4. **完整的表單支持**
- 支持新增、編輯、刪除操作
- 完整的表單驗證和重置邏輯
- 與現有資產管理流程完全整合

## ✅ 測試要點

### 1. **新增保單資產**
- 選擇保單類型
- 填寫保單名稱（可選）
- 填寫持有數量
- 填寫成本基礎
- 填寫現在價值
- 填寫壽險額度

### 2. **資產負債頁面顯示**
- 確認顯示"保單"標籤
- 確認顯示四個字段：成本基礎、當前價值、損益、壽險額度
- 確認損益計算正確（包括百分比）
- 確認壽險額度格式化正確

### 3. **編輯和刪除功能**
- 確認可以編輯保單資產
- 確認可以刪除保單資產
- 確認編輯時壽險額度字段正確載入

## 🔍 技術細節

### 修改的文件列表
1. `src/components/AddAssetModal.tsx` - 主要實現文件
2. `src/services/assetTransactionSyncService.ts` - 數據接口擴展
3. `src/screens/main/BalanceSheetScreen.tsx` - 顯示邏輯
4. `src/components/AddTransactionModal.tsx` - 類型標籤更新

### 新增的字段
- `insuranceAmount` - 狀態變量
- `insurance_amount` - 數據字段

### 保持的兼容性
- 所有現有功能保持不變
- 向後兼容現有資產數據
- 遵循現有的代碼風格和架構

---

**實現日期**：2024年12月19日  
**實現人員**：Augment Agent  
**功能狀態**：已完成並測試
