/**
 * SP500 股票同步腳本
 * 1. 從 CSV 讀取 SP500 清單
 * 2. 使用 Alpha Vantage API 獲取股價資料
 * 3. 儲存到 Supabase 資料庫
 * 
 * 執行方式: node scripts/syncSP500WithAlphaVantage.js
 */

const fs = require('fs');
const path = require('path');

// Alpha Vantage API 設定
const ALPHA_VANTAGE_API_KEY = 'QJTK95T7SA1661WM';
const ALPHA_VANTAGE_BASE_URL = 'https://www.alphavantage.co/query';

// Supabase 設定 (需要從環境變數或配置文件讀取)
const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL || 'YOUR_SUPABASE_URL';
const SUPABASE_ANON_KEY = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'YOUR_SUPABASE_ANON_KEY';

// API 限制設定 (Alpha Vantage 免費版限制)
const API_CALLS_PER_MINUTE = 5;
const API_CALLS_PER_DAY = 500;
const DELAY_BETWEEN_CALLS = 12000; // 12秒間隔 (5 calls/min)

class SP500Synchronizer {
  constructor() {
    this.processedCount = 0;
    this.successCount = 0;
    this.errorCount = 0;
    this.apiCallsToday = 0;
    this.startTime = Date.now();
  }

  /**
   * 讀取 SP500 CSV 檔案
   */
  readSP500CSV() {
    try {
      const csvPath = path.join(__dirname, '../database/20250601135735.csv');
      const csvContent = fs.readFileSync(csvPath, 'utf-8');
      
      const lines = csvContent.split('\n').filter(line => line.trim());
      const stocks = [];
      
      for (const line of lines) {
        const [symbol, name] = line.split(',').map(item => item.trim());
        if (symbol && name && symbol !== '﻿MSFT') { // 跳過 BOM 字符
          stocks.push({
            symbol: symbol.replace(/﻿/g, ''), // 清除 BOM
            name: name
          });
        }
      }
      
      console.log(`📋 成功讀取 ${stocks.length} 個 SP500 股票`);
      return stocks;
    } catch (error) {
      console.error('❌ 讀取 CSV 檔案失敗:', error);
      return [];
    }
  }

  /**
   * 從 Alpha Vantage 獲取股票報價
   */
  async fetchStockQuote(symbol) {
    try {
      const params = new URLSearchParams({
        function: 'GLOBAL_QUOTE',
        symbol: symbol,
        apikey: ALPHA_VANTAGE_API_KEY,
      });

      const response = await fetch(`${ALPHA_VANTAGE_BASE_URL}?${params}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      // 檢查 API 限制錯誤
      if (data.Note && data.Note.includes('API call frequency')) {
        throw new Error('API_LIMIT_EXCEEDED');
      }

      if (data.Information && data.Information.includes('API call frequency')) {
        throw new Error('API_LIMIT_EXCEEDED');
      }

      if (!data['Global Quote'] || !data['Global Quote']['01. symbol']) {
        throw new Error('No data returned');
      }

      const quote = data['Global Quote'];
      
      return {
        symbol: quote['01. symbol'],
        price: parseFloat(quote['05. price']) || 0,
        open_price: parseFloat(quote['02. open']) || 0,
        high_price: parseFloat(quote['03. high']) || 0,
        low_price: parseFloat(quote['04. low']) || 0,
        volume: parseInt(quote['06. volume']) || 0,
        change_amount: parseFloat(quote['09. change']) || 0,
        change_percent: parseFloat(quote['10. change percent'].replace('%', '')) || 0,
        previous_close: parseFloat(quote['08. previous close']) || 0,
        price_date: quote['07. latest trading day'],
      };
    } catch (error) {
      if (error.message === 'API_LIMIT_EXCEEDED') {
        throw error;
      }
      console.error(`❌ 獲取 ${symbol} 報價失敗:`, error.message);
      return null;
    }
  }

  /**
   * 儲存股票資料到 Supabase
   */
  async saveStockToSupabase(stockData, stockName) {
    try {
      const payload = {
        stock_symbol: stockData.symbol,
        stock_name: stockName,
        stock_price: stockData.price,
        stock_open: stockData.open_price,
        stock_high: stockData.high_price,
        stock_low: stockData.low_price,
        stock_volume: stockData.volume,
        stock_change: stockData.change_amount,
        stock_change_percent: stockData.change_percent,
        stock_previous_close: stockData.previous_close,
        is_sp500_stock: true
      };

      const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/upsert_us_stock`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Supabase error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      console.log(`✅ ${stockData.symbol} 儲存成功 - $${stockData.price}`);
      return true;
    } catch (error) {
      console.error(`❌ 儲存 ${stockData.symbol} 失敗:`, error.message);
      return false;
    }
  }

  /**
   * 延遲函數
   */
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 顯示進度
   */
  showProgress() {
    const elapsed = (Date.now() - this.startTime) / 1000;
    const rate = this.processedCount / elapsed * 60; // 每分鐘處理數量
    
    console.log(`\n📊 進度報告:`);
    console.log(`   已處理: ${this.processedCount}`);
    console.log(`   成功: ${this.successCount}`);
    console.log(`   失敗: ${this.errorCount}`);
    console.log(`   今日 API 調用: ${this.apiCallsToday}`);
    console.log(`   處理速度: ${rate.toFixed(1)} 股票/分鐘`);
    console.log(`   已用時間: ${Math.floor(elapsed / 60)}分${Math.floor(elapsed % 60)}秒\n`);
  }

  /**
   * 主要同步函數
   */
  async syncSP500() {
    console.log('🚀 開始 SP500 股票同步...\n');

    // 讀取 SP500 清單
    const sp500Stocks = this.readSP500CSV();
    if (sp500Stocks.length === 0) {
      console.error('❌ 無法讀取 SP500 清單');
      return;
    }

    console.log(`📈 準備同步 ${sp500Stocks.length} 個股票`);
    console.log(`⏱️  預估時間: ${Math.ceil(sp500Stocks.length * DELAY_BETWEEN_CALLS / 1000 / 60)} 分鐘\n`);

    // 逐個處理股票
    for (let i = 0; i < sp500Stocks.length; i++) {
      const stock = sp500Stocks[i];
      
      try {
        // 檢查 API 限制
        if (this.apiCallsToday >= API_CALLS_PER_DAY) {
          console.log('⚠️ 已達到每日 API 調用限制，停止同步');
          break;
        }

        console.log(`🔄 [${i + 1}/${sp500Stocks.length}] 處理 ${stock.symbol} (${stock.name})`);

        // 獲取股票報價
        const stockData = await this.fetchStockQuote(stock.symbol);
        this.apiCallsToday++;

        if (stockData) {
          // 儲存到資料庫
          const saved = await this.saveStockToSupabase(stockData, stock.name);
          if (saved) {
            this.successCount++;
          } else {
            this.errorCount++;
          }
        } else {
          this.errorCount++;
        }

        this.processedCount++;

        // 每 10 個股票顯示一次進度
        if (this.processedCount % 10 === 0) {
          this.showProgress();
        }

        // API 限制延遲
        if (i < sp500Stocks.length - 1) {
          console.log(`⏳ 等待 ${DELAY_BETWEEN_CALLS / 1000} 秒...`);
          await this.delay(DELAY_BETWEEN_CALLS);
        }

      } catch (error) {
        if (error.message === 'API_LIMIT_EXCEEDED') {
          console.log('⚠️ API 調用頻率限制，等待 60 秒...');
          await this.delay(60000);
          i--; // 重試當前股票
          continue;
        }
        
        console.error(`❌ 處理 ${stock.symbol} 時發生錯誤:`, error.message);
        this.errorCount++;
        this.processedCount++;
      }
    }

    // 最終報告
    console.log('\n🎉 SP500 同步完成！');
    this.showProgress();
    
    const successRate = (this.successCount / this.processedCount * 100).toFixed(1);
    console.log(`📈 成功率: ${successRate}%`);
  }

  /**
   * 測試單個股票
   */
  async testSingleStock(symbol = 'AAPL') {
    console.log(`🧪 測試獲取 ${symbol} 股票資料...\n`);

    try {
      const stockData = await this.fetchStockQuote(symbol);
      if (stockData) {
        console.log('✅ API 測試成功:');
        console.log(`   股票: ${stockData.symbol}`);
        console.log(`   價格: $${stockData.price}`);
        console.log(`   漲跌: ${stockData.change_percent}%`);
        console.log(`   成交量: ${stockData.volume.toLocaleString()}`);
        
        // 測試儲存到資料庫
        const saved = await this.saveStockToSupabase(stockData, 'Apple Inc.');
        if (saved) {
          console.log('✅ 資料庫儲存測試成功');
        }
      } else {
        console.log('❌ API 測試失敗');
      }
    } catch (error) {
      console.error('❌ 測試失敗:', error);
    }
  }
}

// 主程序
async function main() {
  const synchronizer = new SP500Synchronizer();
  
  // 檢查命令行參數
  const args = process.argv.slice(2);
  
  if (args.includes('--test')) {
    // 測試模式
    const symbol = args[args.indexOf('--test') + 1] || 'AAPL';
    await synchronizer.testSingleStock(symbol);
  } else if (args.includes('--help')) {
    // 顯示幫助
    console.log(`
SP500 股票同步工具

使用方式:
  node scripts/syncSP500WithAlphaVantage.js           # 完整同步
  node scripts/syncSP500WithAlphaVantage.js --test    # 測試 AAPL
  node scripts/syncSP500WithAlphaVantage.js --test MSFT # 測試指定股票
  node scripts/syncSP500WithAlphaVantage.js --help    # 顯示幫助

注意事項:
- Alpha Vantage 免費版限制: 5 calls/min, 500 calls/day
- 完整同步約需 ${Math.ceil(500 * 12 / 60)} 分鐘
- 建議分批執行，避免超過每日限制
    `);
  } else {
    // 完整同步
    await synchronizer.syncSP500();
  }
}

// 錯誤處理
process.on('unhandledRejection', (reason, promise) => {
  console.error('未處理的 Promise 拒絕:', reason);
  process.exit(1);
});

process.on('SIGINT', () => {
  console.log('\n⚠️ 收到中斷信號，正在安全退出...');
  process.exit(0);
});

// 執行主程序
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 程序執行失敗:', error);
    process.exit(1);
  });
}

module.exports = { SP500Synchronizer };