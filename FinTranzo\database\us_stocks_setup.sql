-- =====================================================
-- 美股資料表設定 (S&P 500)
-- 使用 Alpha Vantage API 獲取資料並儲存到 Supabase
-- 執行日期: 2025-06-01
-- =====================================================

-- =====================================================
-- 1. 美股資料表
-- =====================================================

-- 建立美股資料表
CREATE TABLE IF NOT EXISTS us_stocks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL UNIQUE,
    name VARCHAR(200) NOT NULL,
    sector VARCHAR(100),
    industry VARCHAR(100),
    market_cap BIGINT,
    price DECIMAL(10,2),
    open_price DECIMAL(10,2),
    high_price DECIMAL(10,2),
    low_price DECIMAL(10,2),
    volume BIGINT,
    change_amount DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    previous_close DECIMAL(10,2),
    price_date DATE DEFAULT CURRENT_DATE,
    is_sp500 BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 美股索引
CREATE INDEX IF NOT EXISTS idx_us_stocks_symbol ON us_stocks(symbol);
CREATE INDEX IF NOT EXISTS idx_us_stocks_name ON us_stocks(name);
CREATE INDEX IF NOT EXISTS idx_us_stocks_sector ON us_stocks(sector);
CREATE INDEX IF NOT EXISTS idx_us_stocks_price_date ON us_stocks(price_date DESC);
CREATE INDEX IF NOT EXISTS idx_us_stocks_symbol_search ON us_stocks(symbol varchar_pattern_ops);
CREATE INDEX IF NOT EXISTS idx_us_stocks_name_search ON us_stocks(name varchar_pattern_ops);
CREATE INDEX IF NOT EXISTS idx_us_stocks_sp500 ON us_stocks(is_sp500);
CREATE INDEX IF NOT EXISTS idx_us_stocks_market_cap ON us_stocks(market_cap DESC);

-- =====================================================
-- 2. 更新時間觸發器
-- =====================================================

-- 美股更新時間觸發器
CREATE OR REPLACE FUNCTION update_us_stocks_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_us_stocks_updated_at ON us_stocks;
CREATE TRIGGER trigger_update_us_stocks_updated_at
    BEFORE UPDATE ON us_stocks
    FOR EACH ROW
    EXECUTE FUNCTION update_us_stocks_updated_at();

-- =====================================================
-- 3. 美股查詢函數
-- =====================================================

-- 搜尋美股函數
CREATE OR REPLACE FUNCTION search_us_stocks(
    search_term TEXT,
    sp500_only BOOLEAN DEFAULT true,
    limit_count INTEGER DEFAULT 50
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    sector VARCHAR(100),
    price DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.sector, s.price, s.change_percent, s.market_cap
    FROM us_stocks s
    WHERE 
        (sp500_only = false OR s.is_sp500 = true)
        AND (s.symbol ILIKE '%' || search_term || '%' OR s.name ILIKE '%' || search_term || '%')
    ORDER BY s.market_cap DESC NULLS LAST
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 獲取美股統計函數
CREATE OR REPLACE FUNCTION get_us_stock_stats()
RETURNS TABLE (
    total_stocks BIGINT,
    sp500_count BIGINT,
    sectors_count BIGINT,
    last_updated DATE,
    avg_price DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_stocks,
        COUNT(*) FILTER (WHERE is_sp500 = true) as sp500_count,
        COUNT(DISTINCT sector) as sectors_count,
        MAX(price_date) as last_updated,
        AVG(price) as avg_price
    FROM us_stocks;
END;
$$ LANGUAGE plpgsql;

-- 獲取指定股票資訊
CREATE OR REPLACE FUNCTION get_us_stock_by_symbol(
    stock_symbol VARCHAR(10)
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    sector VARCHAR(100),
    industry VARCHAR(100),
    price DECIMAL(10,2),
    open_price DECIMAL(10,2),
    high_price DECIMAL(10,2),
    low_price DECIMAL(10,2),
    volume BIGINT,
    change_amount DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT,
    price_date DATE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.sector, s.industry, s.price, s.open_price,
        s.high_price, s.low_price, s.volume, s.change_amount, 
        s.change_percent, s.market_cap, s.price_date
    FROM us_stocks s
    WHERE s.symbol = stock_symbol;
END;
$$ LANGUAGE plpgsql;

-- 獲取熱門股票 (按市值排序)
CREATE OR REPLACE FUNCTION get_popular_us_stocks(
    limit_count INTEGER DEFAULT 20
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    sector VARCHAR(100),
    price DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.sector, s.price, s.change_percent, s.market_cap
    FROM us_stocks s
    WHERE s.is_sp500 = true
    ORDER BY s.market_cap DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 按行業分類獲取股票
CREATE OR REPLACE FUNCTION get_us_stocks_by_sector(
    target_sector VARCHAR(100),
    limit_count INTEGER DEFAULT 20
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    price DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.price, s.change_percent, s.market_cap
    FROM us_stocks s
    WHERE s.sector = target_sector
    ORDER BY s.market_cap DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. 資料插入/更新函數
-- =====================================================

-- 插入或更新美股資料函數
CREATE OR REPLACE FUNCTION upsert_us_stock(
    stock_symbol VARCHAR(10),
    stock_name VARCHAR(200),
    stock_sector VARCHAR(100) DEFAULT NULL,
    stock_industry VARCHAR(100) DEFAULT NULL,
    stock_price DECIMAL(10,2) DEFAULT NULL,
    stock_open DECIMAL(10,2) DEFAULT NULL,
    stock_high DECIMAL(10,2) DEFAULT NULL,
    stock_low DECIMAL(10,2) DEFAULT NULL,
    stock_volume BIGINT DEFAULT NULL,
    stock_change DECIMAL(10,2) DEFAULT NULL,
    stock_change_percent DECIMAL(5,2) DEFAULT NULL,
    stock_previous_close DECIMAL(10,2) DEFAULT NULL,
    stock_market_cap BIGINT DEFAULT NULL,
    is_sp500_stock BOOLEAN DEFAULT true
)
RETURNS UUID AS $$
DECLARE
    stock_id UUID;
BEGIN
    INSERT INTO us_stocks (
        symbol, name, sector, industry, price, open_price, high_price, low_price,
        volume, change_amount, change_percent, previous_close, market_cap, is_sp500
    ) VALUES (
        stock_symbol, stock_name, stock_sector, stock_industry, stock_price, 
        stock_open, stock_high, stock_low, stock_volume, stock_change, 
        stock_change_percent, stock_previous_close, stock_market_cap, is_sp500_stock
    )
    ON CONFLICT (symbol) 
    DO UPDATE SET
        name = EXCLUDED.name,
        sector = EXCLUDED.sector,
        industry = EXCLUDED.industry,
        price = EXCLUDED.price,
        open_price = EXCLUDED.open_price,
        high_price = EXCLUDED.high_price,
        low_price = EXCLUDED.low_price,
        volume = EXCLUDED.volume,
        change_amount = EXCLUDED.change_amount,
        change_percent = EXCLUDED.change_percent,
        previous_close = EXCLUDED.previous_close,
        market_cap = EXCLUDED.market_cap,
        price_date = CURRENT_DATE,
        updated_at = NOW()
    RETURNING id INTO stock_id;
    
    RETURN stock_id;
END;
$$ LANGUAGE plpgsql;

-- 批量更新股票價格
CREATE OR REPLACE FUNCTION batch_update_us_stock_prices(
    stock_data JSONB
)
RETURNS INTEGER AS $$
DECLARE
    stock_record JSONB;
    update_count INTEGER := 0;
BEGIN
    FOR stock_record IN SELECT * FROM jsonb_array_elements(stock_data)
    LOOP
        UPDATE us_stocks 
        SET 
            price = (stock_record->>'price')::DECIMAL(10,2),
            open_price = (stock_record->>'open')::DECIMAL(10,2),
            high_price = (stock_record->>'high')::DECIMAL(10,2),
            low_price = (stock_record->>'low')::DECIMAL(10,2),
            volume = (stock_record->>'volume')::BIGINT,
            change_amount = (stock_record->>'change')::DECIMAL(10,2),
            change_percent = (stock_record->>'change_percent')::DECIMAL(5,2),
            previous_close = (stock_record->>'previous_close')::DECIMAL(10,2),
            price_date = CURRENT_DATE,
            updated_at = NOW()
        WHERE symbol = stock_record->>'symbol';
        
        IF FOUND THEN
            update_count := update_count + 1;
        END IF;
    END LOOP;
    
    RETURN update_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. 視圖建立
-- =====================================================

-- 最新美股價格視圖
CREATE OR REPLACE VIEW latest_us_stocks AS
SELECT 
    symbol,
    name,
    sector,
    industry,
    price,
    change_amount,
    change_percent,
    volume,
    market_cap,
    price_date,
    updated_at
FROM us_stocks
WHERE price_date = (SELECT MAX(price_date) FROM us_stocks);

-- S&P 500 股票視圖
CREATE OR REPLACE VIEW sp500_stocks AS
SELECT 
    symbol,
    name,
    sector,
    price,
    change_percent,
    market_cap,
    price_date
FROM us_stocks
WHERE is_sp500 = true
ORDER BY market_cap DESC;

-- 行業統計視圖
CREATE OR REPLACE VIEW us_stock_sector_stats AS
SELECT 
    sector,
    COUNT(*) as stock_count,
    AVG(price) as avg_price,
    SUM(market_cap) as total_market_cap,
    AVG(change_percent) as avg_change_percent
FROM us_stocks
WHERE sector IS NOT NULL
GROUP BY sector
ORDER BY total_market_cap DESC;

-- =====================================================
-- 6. RLS (Row Level Security) 政策
-- =====================================================

-- 美股表 RLS
ALTER TABLE us_stocks ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Allow public read access" ON us_stocks;
CREATE POLICY "Allow public read access" ON us_stocks
    FOR SELECT USING (true);

DROP POLICY IF EXISTS "Allow service role write access" ON us_stocks;
CREATE POLICY "Allow service role write access" ON us_stocks
    FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- 7. 註解說明
-- =====================================================

COMMENT ON TABLE us_stocks IS '美股資料表，儲存 S&P 500 等美股資訊';
COMMENT ON COLUMN us_stocks.symbol IS '股票代號 (如: AAPL)';
COMMENT ON COLUMN us_stocks.name IS '公司名稱';
COMMENT ON COLUMN us_stocks.sector IS '行業分類';
COMMENT ON COLUMN us_stocks.industry IS '細分行業';
COMMENT ON COLUMN us_stocks.price IS '當前股價 (美元)';
COMMENT ON COLUMN us_stocks.market_cap IS '市值';
COMMENT ON COLUMN us_stocks.is_sp500 IS '是否為 S&P 500 成分股';

COMMENT ON FUNCTION search_us_stocks IS '搜尋美股函數，支援代號和名稱模糊搜尋';
COMMENT ON FUNCTION get_us_stock_by_symbol IS '根據股票代號獲取詳細資訊';
COMMENT ON FUNCTION upsert_us_stock IS '插入或更新美股資料';

-- =====================================================
-- 設定完成
-- =====================================================

SELECT 
    '🎉 美股資料庫設定完成！' as status,
    '準備導入 S&P 500 股票清單' as next_step,
    NOW() as setup_time;
