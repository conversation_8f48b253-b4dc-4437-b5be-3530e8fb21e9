# 美國ETF整合功能

## 📈 概述

本項目已成功將美國ETF整合到美股查詢系統中，用戶現在可以搜索和查詢ETF的即時股價。

## 🚀 主要功能

### 1. ETF數據存儲
- **數據庫表**: `us_stocks` 表已擴展支持ETF
- **ETF標識**: 使用 `is_etf=true` 和 `asset_type='ETF'` 標識
- **ETF視圖**: `us_etf_view` 和 `us_etf_by_sector` 提供專門的ETF查詢
- **ETF數量**: 支持438個美國ETF

### 2. ETF搜索功能
- **混合搜索**: 同時搜索股票和ETF
- **專門ETF搜索**: 僅搜索ETF的專用接口
- **熱門ETF**: 按市值排序的熱門ETF列表
- **ETF徽章**: 前端顯示ETF標識徽章

### 3. ETF價格更新
- **即時報價**: 使用Alpha Vantage API獲取ETF即時價格
- **批量同步**: 支持批量更新ETF價格
- **每日更新**: 整合到每日自動更新流程
- **API限制**: 遵守Alpha Vantage API速率限制

## 🔧 技術實現

### 數據處理器 (`stock_data_processor.py`)
```python
# 處理美國ETF CSV數據並插入數據庫
python stock_data_processor.py
```

### 前端組件更新
- **USStockSearchInput**: 支持ETF搜索和顯示
- **ETF徽章**: 視覺區分ETF和股票
- **搜索參數**: `includeETF` 和 `sp500Only` 選項

### 後端服務更新
- **usStockQueryService**: 新增ETF搜索方法
- **usStockSyncService**: 新增ETF價格同步功能
- **dailyUpdateScheduler**: 整合ETF到每日更新

## 📊 支持的ETF

### 熱門ETF列表
- **SPY**: 標普500指數ETF-SPDR
- **QQQ**: 納指100ETF-Invesco QQQ Trust
- **IWM**: 羅素2000ETF-iShares
- **VOO**: 標普500ETF-Vanguard
- **VTI**: 整體股市指數ETF-Vanguard
- 以及更多...

### ETF分類
- 股票指數ETF
- 債券ETF
- 商品ETF
- 行業ETF
- 國際ETF

## 🛠️ 使用方法

### 1. 初始化ETF數據
```bash
# 運行數據處理器插入ETF基本信息
python stock_data_processor.py
```

### 2. 前端搜索ETF
```typescript
// 搜索ETF
const etfResults = await usStockQueryService.searchETFs('標普500', 10);

// 混合搜索 (股票+ETF)
const mixedResults = await usStockQueryService.searchStocks('Apple', false, 10, true);

// 獲取熱門ETF
const popularETFs = await usStockQueryService.getPopularETFs(10);
```

### 3. 更新ETF價格
```typescript
// 更新單個ETF價格
await usStockSyncService.updateStockPrice('SPY', true);

// 批量更新ETF價格
const etfList = ['SPY', 'QQQ', 'IWM'];
await usStockSyncService.syncETFPrices(etfList, 5);
```

## 🧪 測試功能

### 運行ETF整合測試
```typescript
import { runAllETFTests } from './src/utils/testETFIntegration';

// 運行所有ETF測試
const result = await runAllETFTests();
```

### 測試項目
1. ETF搜索功能
2. 混合搜索功能
3. 熱門ETF查詢
4. ETF詳細信息
5. ETF價格更新
6. 統計信息
7. 快取功能

## 📋 數據庫結構

### us_stocks 表新增字段
```sql
is_etf BOOLEAN DEFAULT false        -- 是否為ETF
asset_type VARCHAR(20) DEFAULT 'STOCK'  -- 資產類型 (STOCK/ETF)
```

### ETF專用視圖
```sql
-- ETF視圖
SELECT * FROM us_etf_view;

-- ETF分類統計
SELECT * FROM us_etf_by_sector;
```

### ETF搜索函數
```sql
-- 搜索ETF
SELECT * FROM search_us_etf('標普500', 10);
```

## 🔄 每日更新流程

每日更新現在包含4個步驟：
1. **美股更新** - S&P 500股票
2. **美國ETF更新** - 熱門ETF價格
3. **台股更新** - 台灣股票
4. **匯率更新** - USD/TWD匯率

## ⚠️ 注意事項

### API限制
- Alpha Vantage免費版：每分鐘5次請求
- 每日500次請求限制
- 建議分批更新避免超限

### 錯誤處理
- 網絡錯誤自動重試
- API限制自動等待
- 失敗記錄詳細日誌

### 快取機制
- ETF搜索結果快取2分鐘
- ETF詳細信息快取5分鐘
- 減少API請求頻率

## 🎯 未來擴展

1. **更多ETF數據源**: 整合其他API提供商
2. **ETF分析功能**: 持股分析、費用比較
3. **ETF推薦系統**: 基於用戶偏好推薦
4. **實時價格推送**: WebSocket實時更新
5. **ETF新聞整合**: 相關新聞和公告

## 📞 支持

如有問題或建議，請聯繫開發團隊。

---

**版本**: 1.0.0  
**更新日期**: 2024年12月  
**狀態**: ✅ 已完成並測試
