/**
 * Vercel Serverless Function - 匯率每日更新
 * 每個工作日早上9點執行
 */

import { createClient } from '@supabase/supabase-js';

// Supabase 配置
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Missing Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * 使用 ExchangeRate-API 獲取匯率
 */
async function fetchExchangeRates() {
  try {
    // 使用免費的 ExchangeRate-API
    const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (data.rates && data.rates.TWD) {
      return {
        usd_to_twd: data.rates.TWD,
        updated_at: new Date().toISOString(),
        source: 'exchangerate-api'
      };
    }
    
    return null;
  } catch (error) {
    console.error('Error fetching from ExchangeRate-API:', error);
    return null;
  }
}

/**
 * 使用台灣銀行 API 獲取匯率（備用方案）
 */
async function fetchTaiwanBankRates() {
  try {
    const response = await fetch('https://rate.bot.com.tw/xrt/flcsv/0/day');
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const csvText = await response.text();
    const lines = csvText.split('\n');
    
    // 尋找美元匯率
    for (const line of lines) {
      if (line.includes('USD') || line.includes('美金')) {
        const columns = line.split(',');
        if (columns.length >= 3) {
          const buyRate = parseFloat(columns[2]); // 現金買入
          const sellRate = parseFloat(columns[3]); // 現金賣出
          
          if (!isNaN(buyRate) && !isNaN(sellRate)) {
            const midRate = (buyRate + sellRate) / 2;
            
            return {
              usd_to_twd: midRate,
              buy_rate: buyRate,
              sell_rate: sellRate,
              updated_at: new Date().toISOString(),
              source: 'taiwan-bank'
            };
          }
        }
      }
    }
    
    return null;
  } catch (error) {
    console.error('Error fetching from Taiwan Bank:', error);
    return null;
  }
}

/**
 * 手動設定匯率（最後備用方案）
 */
function getDefaultExchangeRate() {
  return {
    usd_to_twd: 31.5, // 預設匯率
    updated_at: new Date().toISOString(),
    source: 'default'
  };
}

/**
 * 更新匯率到資料庫
 */
async function updateExchangeRates() {
  try {
    console.log('🚀 開始更新匯率...');
    
    let rateData = null;
    
    // 嘗試多個數據源
    rateData = await fetchExchangeRates();
    
    if (!rateData) {
      console.log('⚠️ 主要 API 失敗，嘗試台灣銀行 API');
      rateData = await fetchTaiwanBankRates();
    }
    
    if (!rateData) {
      console.log('⚠️ 所有 API 失敗，使用預設匯率');
      rateData = getDefaultExchangeRate();
    }
    
    console.log(`📊 獲取匯率: USD/TWD = ${rateData.usd_to_twd} (來源: ${rateData.source})`);
    
    // 更新到資料庫
    const { error: updateError } = await supabase
      .from('exchange_rates')
      .upsert({
        from_currency: 'USD',
        to_currency: 'TWD',
        rate: rateData.usd_to_twd,
        buy_rate: rateData.buy_rate || null,
        sell_rate: rateData.sell_rate || null,
        source: rateData.source,
        updated_at: rateData.updated_at
      }, {
        onConflict: 'from_currency,to_currency'
      });
    
    if (updateError) {
      throw updateError;
    }
    
    console.log('✅ 匯率更新成功');
    
    return {
      success: true,
      rate: rateData.usd_to_twd,
      source: rateData.source,
      updated_at: rateData.updated_at
    };
    
  } catch (error) {
    console.error('❌ 匯率更新失敗:', error);
    throw error;
  }
}

/**
 * Vercel Serverless Function 入口
 */
export default async function handler(req, res) {
  // 只允許 POST 請求和 Cron Jobs
  if (req.method !== 'POST' && req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  // 驗證 Cron Job 請求（可選）
  const authHeader = req.headers.authorization;
  if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
    console.log('⚠️ 未授權的請求，但繼續執行更新');
  }
  
  try {
    const result = await updateExchangeRates();
    
    res.status(200).json({
      message: '匯率更新成功',
      timestamp: new Date().toISOString(),
      result
    });
    
  } catch (error) {
    console.error('❌ 處理請求失敗:', error);
    
    res.status(500).json({
      error: '匯率更新失敗',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
}
