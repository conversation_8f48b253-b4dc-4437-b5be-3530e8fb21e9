/**
 * Expo + Supabase 官方推薦的 Polyfills 配置
 * 基於 Expo 官方文檔：https://docs.expo.dev/guides/using-supabase/
 */

// 必須在最前面載入
import 'react-native-get-random-values';

// URL polyfill for Supabase
import 'react-native-url-polyfill/auto';

// Buffer polyfill
import { Buffer } from '@craftzdog/react-native-buffer';
global.Buffer = Buffer;

// Base64 polyfill using Buffer
if (typeof global.btoa === 'undefined') {
  global.btoa = function(str) {
    return Buffer.from(str, 'binary').toString('base64');
  };
}

if (typeof global.atob === 'undefined') {
  global.atob = function(str) {
    return Buffer.from(str, 'base64').toString('binary');
  };
}

// Process polyfill
if (typeof global.process === 'undefined') {
  global.process = {
    env: {},
    version: '16.0.0',
    platform: 'react-native',
    nextTick: (fn) => setTimeout(fn, 0),
  };
}

// Location polyfill
if (typeof global.location === 'undefined') {
  global.location = {
    href: 'https://localhost',
    protocol: 'https:',
    host: 'localhost',
    hostname: 'localhost',
    port: '',
    pathname: '/',
    search: '',
    hash: '',
    origin: 'https://localhost'
  };
}

console.log('✅ Expo + Supabase Polyfills 載入完成');
