# 本地存儲功能說明

## 🎯 功能概述

FinTranzo 現在支援本地存儲功能，用戶輸入的所有數據會自動保存到手機本地存儲中，關閉 APP 後數據不會丟失。

## 📱 支援的數據類型

### 自動保存的數據：
- ✅ **交易記錄** - 所有收入和支出記錄
- ✅ **類別設定** - 自定義的收入/支出類別
- ✅ **帳戶資訊** - 現金、銀行等帳戶
- ✅ **資產數據** - 資產負債表中的資產項目
- ✅ **負債數據** - 負債項目和還款記錄
- ✅ **循環交易** - 定期重複的交易設定

## 🔧 技術實現

### 存儲方式
- 使用 **AsyncStorage** 進行本地持久化存儲
- 數據以 JSON 格式保存在手機本地
- 支援異步讀寫，不會阻塞 UI

### 存儲鍵名
```javascript
const STORAGE_KEYS = {
  TRANSACTIONS: '@FinTranzo:transactions',
  CATEGORIES: '@FinTranzo:categories', 
  ACCOUNTS: '@FinTranzo:accounts',
  INITIALIZED: '@FinTranzo:initialized'
}
```

## 🚀 使用方式

### 首次使用
1. 打開 APP，系統會自動初始化預設數據
2. 預設類別和帳戶會自動創建
3. 用戶可以開始記錄交易

### 日常使用
1. **添加交易** - 數據會自動保存到本地
2. **編輯交易** - 修改會立即同步到本地存儲
3. **刪除交易** - 刪除操作會從本地存儲中移除
4. **關閉 APP** - 所有數據保持不變
5. **重新打開** - 自動從本地存儲加載所有數據

## 📊 數據持久化流程

### 應用啟動時
```
1. 檢查是否已初始化 (@FinTranzo:initialized)
2. 如果已初始化 → 從本地存儲加載數據
3. 如果未初始化 → 創建預設數據並保存
4. 設置初始化標記
```

### 數據操作時
```
1. 用戶執行操作（添加/編輯/刪除）
2. 更新內存中的數據
3. 自動保存到 AsyncStorage
4. 通知 UI 更新
```

## 🛠️ 開發者功能

### 測試本地存儲
運行測試腳本：
```bash
node test_local_storage.js
```

### 清除所有數據
```javascript
import { transactionDataService } from './src/services/transactionDataService';

// 清除所有本地數據
await transactionDataService.clearAllData();
```

### 手動保存數據
```javascript
// 添加交易（自動保存）
await transactionDataService.addTransaction(newTransaction);

// 更新交易（自動保存）
await transactionDataService.updateTransaction(id, updatedData);

// 刪除交易（自動保存）
await transactionDataService.deleteTransaction(id);
```

## 🔒 數據安全

### 本地存儲特點
- ✅ 數據存儲在用戶設備本地
- ✅ 不會上傳到任何服務器
- ✅ 只有用戶本人可以訪問
- ✅ 卸載 APP 時數據會被清除

### 備份建議
- 📱 定期備份手機數據
- 💾 重要數據可手動導出
- 🔄 未來版本將支援雲端同步（付費功能）

## 🆚 免費 vs 付費功能

### 免費用戶（當前版本）
- ✅ 本地存儲所有數據
- ✅ 完整的財務管理功能
- ✅ 無限制的交易記錄
- ❌ 不支援雲端同步
- ❌ 不支援多設備同步

### 付費用戶（未來功能）
- ✅ 包含所有免費功能
- ✅ 雲端數據同步
- ✅ 多設備數據同步
- ✅ 數據備份與恢復
- ✅ 高級分析報告

## 🐛 故障排除

### 數據丟失問題
1. 檢查是否意外清除了 APP 數據
2. 確認 AsyncStorage 權限正常
3. 查看控制台錯誤日誌

### 性能問題
1. 大量數據時可能影響啟動速度
2. 建議定期清理舊數據
3. 考慮數據分頁加載

### 調試方法
```javascript
// 檢查存儲狀態
console.log('交易數量:', transactionDataService.getTransactions().length);
console.log('類別數量:', transactionDataService.getCategories().length);
console.log('帳戶數量:', transactionDataService.getAccounts().length);
```

## 📞 技術支援

如果遇到本地存儲相關問題：
1. 查看控制台日誌
2. 運行測試腳本診斷
3. 嘗試清除數據重新初始化
4. 聯繫開發團隊

---

**注意：本地存儲功能已完全實現，用戶數據會在關閉 APP 後保持不變。**
