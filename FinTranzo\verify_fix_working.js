/**
 * 驗證修復是否正在工作的腳本
 */

console.log('🔍 ===== 驗證修復是否正在工作 =====\n');

// 檢查當前日期和環境
const now = new Date();
console.log('📅 當前時間:', now.toLocaleString('zh-TW'));
console.log('📅 當前月份:', now.getMonth() + 1, '月');
console.log('📅 當前日期:', now.getDate(), '號');
console.log('📅 當月天數:', new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate(), '天\n');

// 測試修復邏輯
console.log('🧪 ===== 測試修復邏輯 =====');

function testDateLogic() {
  const paymentDay = 31;
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth();
  const lastDayOfCurrentMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
  
  console.log(`測試場景: 設定還款日 ${paymentDay}號`);
  console.log(`當前月份: ${currentYear}年${currentMonth + 1}月`);
  console.log(`當月最後一天: ${lastDayOfCurrentMonth}號`);
  
  // 🔥 這是我們修復後的邏輯
  let actualPaymentDay;
  if (paymentDay > lastDayOfCurrentMonth) {
    actualPaymentDay = lastDayOfCurrentMonth;
    console.log(`🔥🔥🔥 修復邏輯 - 月末日期調整: 原定${paymentDay}號，調整為${actualPaymentDay}號`);
  } else {
    actualPaymentDay = paymentDay;
    console.log(`🔥🔥🔥 修復邏輯 - 無需調整: ${paymentDay}號正常`);
  }
  
  return {
    originalDay: paymentDay,
    actualDay: actualPaymentDay,
    shouldBeAdjusted: paymentDay > lastDayOfCurrentMonth,
    isCurrentMonthMay: currentMonth + 1 === 5,
    mayHas31Days: currentMonth + 1 === 5 && lastDayOfCurrentMonth === 31
  };
}

const testResult = testDateLogic();

console.log('\n📊 ===== 測試結果分析 =====');
console.log('原始設定日期:', testResult.originalDay, '號');
console.log('實際計算日期:', testResult.actualDay, '號');
console.log('是否需要調整:', testResult.shouldBeAdjusted ? '是' : '否');
console.log('當前是否為5月:', testResult.isCurrentMonthMay ? '是' : '否');
console.log('5月是否有31天:', testResult.mayHas31Days ? '是' : '否');

console.log('\n🎯 ===== 預期結果 =====');
if (testResult.isCurrentMonthMay && testResult.mayHas31Days) {
  if (testResult.actualDay === 31) {
    console.log('✅ 修復成功：5月31號正確顯示為31號');
  } else {
    console.log('❌ 修復失敗：5月31號被錯誤調整為', testResult.actualDay, '號');
  }
} else {
  console.log('⚠️ 當前不是5月或5月沒有31天，無法測試主要問題');
}

console.log('\n🔧 ===== 調試建議 =====');
console.log('1. 確保應用程序已重新啟動並清除緩存');
console.log('2. 在瀏覽器開發者工具中查找以下日誌:');
console.log('   - "🔥🔥🔥 修復1生效"');
console.log('   - "🔥🔥🔥 修復2生效"');
console.log('   - "🔥🔥🔥 修復3生效"');
console.log('3. 如果沒有看到這些日誌，說明修復代碼沒有被執行');
console.log('4. 創建新負債時應該會觸發這些日誌');

console.log('\n📝 ===== 測試步驟 =====');
console.log('請按照以下步驟測試:');
console.log('1. 清除瀏覽器緩存 (Ctrl+Shift+R)');
console.log('2. 打開開發者工具 (F12)');
console.log('3. 查看 Console 標籤');
console.log('4. 創建新負債:');
console.log('   - 名稱: 測試信用卡');
console.log('   - 餘額: 50000');
console.log('   - 月付金: 10000');
console.log('   - 還款帳戶: 銀行');
console.log('   - 還款日: 31號');
console.log('   - 期數: 12');
console.log('5. 保存後檢查:');
console.log('   - 記帳區是否顯示31號');
console.log('   - 收支分析是否包含還款記錄');

console.log('\n✨ 驗證腳本執行完成！');
