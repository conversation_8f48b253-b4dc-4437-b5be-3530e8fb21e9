#!/usr/bin/env python3
"""
使用Yahoo Finance API (直接HTTP請求) 更新ETF股價到Supabase
"""

import os
import sys
import time
import json
import requests
from datetime import datetime
from supabase import create_client, Client

# Supabase配置
SUPABASE_URL = "https://yrryyapzkgrsahranzvo.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlycnl5YXB6a2dyc2FocmFuenZvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODE3MzYzNSwiZXhwIjoyMDYzNzQ5NjM1fQ.xjiXVagzKgBBYXqtRjJiXwCgbRdEW0EpFzCIlJ26SSQ"

def get_etf_quote_yahoo_api(symbol):
    """使用Yahoo Finance API獲取ETF報價"""
    try:
        print(f"🔍 獲取 {symbol} 的Yahoo Finance報價...")
        
        # Yahoo Finance API URL
        url = f"https://query1.finance.yahoo.com/v8/finance/chart/{symbol}"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        data = response.json()
        
        if 'chart' not in data or not data['chart']['result']:
            print(f"⚠️ 沒有找到 {symbol} 的數據")
            return None
        
        result = data['chart']['result'][0]
        meta = result['meta']
        
        # 獲取價格數據
        current_price = meta.get('regularMarketPrice', 0)
        previous_close = meta.get('previousClose', 0)
        open_price = meta.get('regularMarketOpen', 0)
        high_price = meta.get('regularMarketDayHigh', 0)
        low_price = meta.get('regularMarketDayLow', 0)
        volume = meta.get('regularMarketVolume', 0)
        
        # 計算變化
        change_amount = current_price - previous_close if previous_close else 0
        change_percent = (change_amount / previous_close * 100) if previous_close else 0
        
        # 獲取交易日期
        market_time = meta.get('regularMarketTime', int(datetime.now().timestamp()))
        price_date = datetime.fromtimestamp(market_time).strftime('%Y-%m-%d')
        
        price_data = {
            'symbol': symbol,
            'price': round(float(current_price), 2),
            'open_price': round(float(open_price), 2),
            'high_price': round(float(high_price), 2),
            'low_price': round(float(low_price), 2),
            'volume': int(volume) if volume else 0,
            'change_amount': round(float(change_amount), 2),
            'change_percent': round(float(change_percent), 2),
            'previous_close': round(float(previous_close), 2),
            'price_date': price_date,
            'updated_at': datetime.now().isoformat()
        }
        
        print(f"✅ {symbol}: ${price_data['price']} ({price_data['change_percent']:+.2f}%)")
        return price_data
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 網絡請求失敗 {symbol}: {str(e)}")
        return None
    except Exception as e:
        print(f"❌ 獲取 {symbol} 報價時出錯: {str(e)}")
        return None

def update_etf_price_in_supabase(supabase: Client, price_data):
    """更新ETF價格到Supabase"""
    try:
        symbol = price_data['symbol']
        
        # 更新數據庫
        result = supabase.table('us_stocks').update({
            'price': price_data['price'],
            'open_price': price_data['open_price'],
            'high_price': price_data['high_price'],
            'low_price': price_data['low_price'],
            'volume': price_data['volume'],
            'change_amount': price_data['change_amount'],
            'change_percent': price_data['change_percent'],
            'previous_close': price_data['previous_close'],
            'price_date': price_data['price_date'],
            'updated_at': price_data['updated_at']
        }).eq('symbol', symbol).eq('is_etf', True).execute()
        
        if result.data:
            print(f"✅ 數據庫更新成功: {symbol}")
            return True
        else:
            print(f"⚠️ 數據庫更新沒有返回數據: {symbol}")
            return False
            
    except Exception as e:
        print(f"❌ 數據庫更新失敗 {symbol}: {str(e)}")
        return False

def get_etf_list_from_supabase(supabase: Client, limit=50):
    """從Supabase獲取ETF列表"""
    try:
        print("📊 從數據庫獲取ETF列表...")
        
        result = supabase.table('us_stocks').select('symbol, name').eq('is_etf', True).order('symbol').limit(limit).execute()
        
        if result.data:
            etf_list = [etf['symbol'] for etf in result.data]
            print(f"✅ 獲取到 {len(etf_list)} 個ETF")
            return etf_list
        else:
            print("⚠️ 沒有找到ETF數據")
            return []
            
    except Exception as e:
        print(f"❌ 獲取ETF列表失敗: {str(e)}")
        return []

def update_etf_prices_yahoo_simple(max_etfs=30):
    """使用Yahoo Finance API更新ETF價格"""
    print("🚀 開始使用Yahoo Finance API更新ETF價格...")
    print("=" * 60)
    
    try:
        # 創建Supabase客戶端
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        print("✅ Supabase連接成功")
        
        # 獲取ETF列表
        etf_symbols = get_etf_list_from_supabase(supabase, max_etfs)
        
        if not etf_symbols:
            print("❌ 沒有ETF需要更新")
            return False
        
        print(f"\n🎯 準備更新 {len(etf_symbols)} 個ETF的價格")
        print(f"🌐 使用Yahoo Finance API")
        
        success_count = 0
        error_count = 0
        
        for i, symbol in enumerate(etf_symbols):
            try:
                print(f"\n📈 處理 {symbol} ({i+1}/{len(etf_symbols)})")
                
                # 獲取股價數據
                price_data = get_etf_quote_yahoo_api(symbol)
                
                if price_data:
                    # 更新到數據庫
                    if update_etf_price_in_supabase(supabase, price_data):
                        success_count += 1
                    else:
                        error_count += 1
                else:
                    error_count += 1
                
                # 避免請求過快
                if i < len(etf_symbols) - 1:
                    print("⏳ 等待1秒...")
                    time.sleep(1)
                
            except KeyboardInterrupt:
                print("\n⚠️ 用戶中斷更新")
                break
            except Exception as e:
                print(f"❌ 處理 {symbol} 時發生錯誤: {str(e)}")
                error_count += 1
        
        # 顯示結果
        print("\n" + "=" * 60)
        print("🎉 ETF價格更新完成！")
        print(f"✅ 成功更新: {success_count} 個ETF")
        print(f"❌ 更新失敗: {error_count} 個ETF")
        
        if success_count > 0:
            print("\n📊 驗證更新結果...")
            # 驗證更新結果
            verify_result = supabase.table('us_stocks').select('symbol, name, price, change_percent, updated_at').eq('is_etf', True).not_.is_('price', 'null').order('updated_at', desc=True).limit(10).execute()
            
            if verify_result.data:
                print("✅ 最近更新的ETF:")
                for etf in verify_result.data:
                    updated_time = etf['updated_at'][:19] if etf['updated_at'] else 'N/A'
                    print(f"   {etf['symbol']}: ${etf['price']} ({etf['change_percent']:+.2f}%) - {updated_time}")
                
                # 測試ETF視圖
                print("\n📊 測試ETF視圖...")
                view_result = supabase.table('us_etf_view').select('*').limit(5).execute()
                if view_result.data:
                    print("✅ us_etf_view 視圖正常:")
                    for etf in view_result.data:
                        print(f"   {etf['symbol']}: {etf['name']} - ${etf.get('price', 'N/A')}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 更新過程中發生錯誤: {str(e)}")
        return False

def test_single_etf():
    """測試單個ETF獲取"""
    print("🧪 測試單個ETF獲取...")
    
    test_symbols = ['SPY', 'QQQ', 'IWM']
    
    for symbol in test_symbols:
        price_data = get_etf_quote_yahoo_api(symbol)
        if price_data:
            print(f"✅ 測試成功: {symbol}")
        else:
            print(f"❌ 測試失敗: {symbol}")
        time.sleep(1)

def main():
    """主函數"""
    print("📈 ETF價格更新工具 (Yahoo Finance API)")
    print("=" * 60)

    print(f"🗄️ Supabase URL: {SUPABASE_URL}")
    print(f"🌐 數據源: Yahoo Finance API")

    # 檢查命令行參數
    if len(sys.argv) > 1:
        if sys.argv[1] == 'test':
            test_single_etf()
            return True
        elif sys.argv[1] == 'auto':
            max_etfs = int(sys.argv[2]) if len(sys.argv) > 2 else 50
            print(f"🤖 自動模式：更新 {max_etfs} 個ETF")
        else:
            max_etfs = int(sys.argv[1])
            print(f"🎯 將更新 {max_etfs} 個ETF的價格")
    else:
        # 選擇操作
        print("\n請選擇操作:")
        print("1. 測試單個ETF獲取")
        print("2. 批量更新ETF價格")

        try:
            choice = input("請輸入選擇 (1 或 2，預設2): ").strip()
            if choice == '1':
                test_single_etf()
                return True

            # 詢問用戶更新數量
            max_etfs = input("\n請輸入要更新的ETF數量 (預設20，最多50): ").strip()
            if not max_etfs:
                max_etfs = 20
            else:
                max_etfs = min(int(max_etfs), 50)
        except ValueError:
            max_etfs = 20
        except EOFError:
            # 如果沒有輸入，使用預設值
            max_etfs = 50
            print(f"🤖 使用預設值：更新 {max_etfs} 個ETF")

    print(f"🎯 將更新 {max_etfs} 個ETF的價格")

    # 開始更新
    success = update_etf_prices_yahoo_simple(max_etfs=max_etfs)

    if success:
        print("\n✅ ETF價格更新成功！")
        print("🔍 您現在可以在前端搜索ETF並查看最新價格")
        print("📊 可以查看 us_etf_view 視圖獲取ETF數據")
        return True
    else:
        print("\n❌ ETF價格更新失敗")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 用戶中斷程序")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序執行失敗: {str(e)}")
        sys.exit(1)
