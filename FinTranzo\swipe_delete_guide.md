# 滑動刪除功能使用指南

## 🎯 功能概述

在記帳列表中，每筆交易都支持向左滑動刪除功能。根據交易類型的不同，提供不同的刪除選項。

## 📱 操作方式

### 基本滑動操作
1. **向左滑動**：在任何交易項目上向左滑動
2. **顯示刪除按鈕**：滑動超過閾值後會顯示紅色刪除按鈕
3. **點擊刪除**：點擊刪除按鈕觸發刪除選項

### 回彈機制
- 如果滑動距離不足，交易項目會自動回彈到原位
- 滑動距離超過80像素才會顯示刪除按鈕

## 🔄 普通交易刪除

### 操作流程
1. 向左滑動普通交易項目
2. 點擊紅色刪除按鈕
3. 確認刪除對話框出現
4. 選擇「刪除」或「取消」

### 刪除效果
- ✅ 立即從交易列表中移除
- ✅ 月曆上的標記相應更新
- ✅ 不影響其他交易記錄

## 🔁 循環交易刪除

### 三種刪除選項

#### 1. **單次刪除**
- **說明**：僅刪除當前這一筆交易記錄
- **效果**：
  - ✅ 只移除選中的交易記錄
  - ✅ 循環交易模板保持活躍
  - ✅ 未來的循環交易繼續生成
  - ✅ 其他月份的相同交易不受影響

#### 2. **向後刪除**
- **說明**：刪除包含這個月之後的所有相關交易
- **效果**：
  - ✅ 停用循環交易模板
  - ✅ 刪除當前月份及之後的所有相關交易
  - ✅ 保留之前月份的交易記錄
  - ✅ 月曆上未來的標記消失

#### 3. **全部刪除**
- **說明**：刪除這筆循環交易的所有相關記錄
- **效果**：
  - ✅ 完全刪除循環交易模板
  - ✅ 移除所有相關的交易記錄（過去、現在、未來）
  - ✅ 月曆上所有相關標記消失
  - ✅ 徹底清除這個循環交易

## 🎯 實際應用場景

### 場景1：誤記錄的午餐費用
```
操作：單次刪除
結果：只刪除這一筆午餐記錄，不影響其他
```

### 場景2：健身房會員到期
```
操作：向後刪除
結果：從這個月開始停止健身房費用，保留之前的記錄
```

### 場景3：搬家後不再需要房租記錄
```
操作：全部刪除
結果：完全移除所有房租相關記錄
```

### 場景4：信用卡換銀行
```
操作：向後刪除舊銀行記錄，新增新銀行循環交易
結果：保留舊記錄，從指定月份開始使用新銀行
```

## ⚠️ 注意事項

### 刪除確認
- 所有刪除操作都需要用戶確認
- 提供清楚的選項說明
- 支持取消操作

### 數據安全
- 刪除操作立即生效
- 建議在重要操作前仔細確認
- 未來可能添加撤銷功能

### 視覺反饋
- 滑動時有平滑的動畫效果
- 刪除按鈕有明顯的視覺提示
- 操作完成後項目立即消失

## 🔧 技術實現

### 手勢識別
- 使用 PanResponder 實現滑動手勢
- 支持水平滑動檢測
- 防止與垂直滾動衝突

### 動畫效果
- 使用 Animated API 實現平滑動畫
- 支持彈性回彈效果
- 流暢的滑動體驗

### 狀態管理
- 實時更新交易列表狀態
- 同步更新未來循環交易
- 保持數據一致性

## 📊 用戶體驗優化

### 操作便利性
- 直觀的滑動操作
- 清楚的視覺提示
- 快速的響應速度

### 錯誤預防
- 滑動閾值設定合理
- 確認對話框防止誤操作
- 清楚的選項說明

### 一致性
- 所有交易項目都支持滑動
- 統一的操作邏輯
- 一致的視覺設計

現在您可以在記帳列表中向左滑動任何交易項目來體驗刪除功能！
