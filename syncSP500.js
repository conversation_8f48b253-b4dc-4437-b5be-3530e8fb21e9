// 在環境變數檢查部分後新增
const SP500_CSV_PATH = process.env.EXPO_PUBLIC_SP500_CSV_PATH || '20250601135735.csv';



const { USStockSetupTester } = require('./testUSStockSetup');
const csv = require('csv-parser');
const fs = require('fs');
const { createReadStream } = require('fs');

class SP500Syncer extends USStockSetupTester {
  constructor() {
    super();
    this.batchSize = 5; // 符合 API 每分鐘限制
    this.delay = 60000; // 60 秒延遲
  }

  async syncSP500(csvPath) {
    const stocks = await this.parseCSV(csvPath);
    
    for (let i = 0; i < stocks.length; i += this.batchSize) {
      const batch = stocks.slice(i, i + this.batchSize);
      await this.processBatch(batch);
      console.log(`⏳ 已同步 ${i + batch.length}/${stocks.length} 支股票`);
      await new Promise(resolve => setTimeout(resolve, this.delay));
    }
  }

  async parseCSV(csvPath) {
    return new Promise((resolve) => {
      const results = [];
      createReadStream(csvPath)
        .pipe(csv())
        .on('data', (data) => {
          results.push({
            symbol: data.Symbol,
            name: data.Name,
            sector: data.Sector
          });
        })
        .on('end', () => resolve(results));
    });
  }

  async processBatch(batch) {
    for (const stock of batch) {
      try {
        const quote = await this.fetchStockData(stock.symbol);
        await this.upsertStockData({...stock, ...quote});
      } catch (error) {
        console.error(`❌ ${stock.symbol} 同步失敗:`, error.message);
      }
    }
  }

  async fetchStockData(symbol) {
    const params = new URLSearchParams({
      function: 'GLOBAL_QUOTE',
      symbol,
      apikey: 'QJTK95T7SA1661WM',
    });

    const response = await fetch(`https://www.alphavantage.co/query?${params}`);
    const data = await response.json();
    return this.transformQuote(data['Global Quote']);
  }

  transformQuote(quote) {
    return {
      price: parseFloat(quote['05. price']),
      open: parseFloat(quote['02. open']),
      high: parseFloat(quote['03. high']),
      low: parseFloat(quote['04. low']),
      volume: parseInt(quote['06. volume']),
      change: parseFloat(quote['09. change']),
      change_percent: quote['10. change percent'],
      previous_close: parseFloat(quote['08. previous close'])
    };
  }

  async upsertStockData(data) {
    const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/upsert_us_stock`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
      },
      body: JSON.stringify({
        ...data,
        is_sp500_stock: true,
        updated_at: new Date().toISOString()
      }),
    });

    if (!response.ok) {
      throw new Error(`數據庫寫入失敗: ${response.status}`);
    }
  }
}

// 執行同步
async function main() {
  const syncer = new SP500Syncer();
  await syncer.syncSP500('20250601135735.csv'); // 替換實際 CSV 路徑
  console.log('🎉 SP500 股票同步完成');
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ 同步失敗:', error);
    process.exit(1);
  });
}