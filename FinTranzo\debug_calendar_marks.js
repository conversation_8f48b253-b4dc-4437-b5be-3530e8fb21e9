/**
 * 調試日曆標記問題
 * 檢查交易記錄的實際日期和日曆標記
 */

console.log('🔍 ===== 調試日曆標記問題 =====\n');

// 模擬負債創建後的交易記錄
function simulateTransactionCreation() {
  console.log('💳 模擬負債創建流程...');
  
  // 模擬負債數據
  const liability = {
    id: 'test_liability',
    name: '測試信用卡',
    type: 'credit_card',
    balance: 50000,
    monthly_payment: 10000,
    payment_account: '銀行',
    payment_day: 31,
    payment_periods: 12,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
  
  console.log('📝 負債信息:', {
    name: liability.name,
    paymentDay: liability.payment_day,
    monthlyPayment: liability.monthly_payment
  });
  
  // 模擬日期計算（使用我們修復後的邏輯）
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();
  const currentDay = currentDate.getDate();
  const paymentDay = liability.payment_day;
  
  console.log('\n📅 日期計算:');
  console.log(`當前日期: ${currentYear}年${currentMonth + 1}月${currentDay}日`);
  console.log(`設定還款日: ${paymentDay}號`);
  
  // 🔥 使用修復後的邏輯
  const lastDayOfCurrentMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
  console.log(`當月最後一天: ${lastDayOfCurrentMonth}號`);
  
  let actualPaymentDay;
  if (paymentDay > lastDayOfCurrentMonth) {
    actualPaymentDay = lastDayOfCurrentMonth;
    console.log(`🔥 需要調整: 原定${paymentDay}號 → ${actualPaymentDay}號`);
  } else {
    actualPaymentDay = paymentDay;
    console.log(`🔥 無需調整: ${paymentDay}號正常`);
  }
  
  // 創建交易記錄的日期
  let transactionDate;
  if (currentDay < paymentDay) {
    // 本月還款
    transactionDate = new Date(currentYear, currentMonth, actualPaymentDay);
    console.log(`✅ 本月還款: ${transactionDate.toLocaleDateString('zh-TW')}`);
  } else {
    // 下月還款
    const nextYear = currentMonth === 11 ? currentYear + 1 : currentYear;
    const nextMonth = currentMonth === 11 ? 0 : currentMonth + 1;
    const nextMonthLastDay = new Date(nextYear, nextMonth + 1, 0).getDate();
    const nextActualPaymentDay = paymentDay > nextMonthLastDay ? nextMonthLastDay : paymentDay;
    
    transactionDate = new Date(nextYear, nextMonth, nextActualPaymentDay);
    console.log(`⏰ 下月還款: ${transactionDate.toLocaleDateString('zh-TW')}`);
  }
  
  // 模擬創建的交易記錄
  const transaction = {
    id: `debt_payment_${Date.now()}`,
    amount: liability.monthly_payment,
    type: 'expense',
    description: liability.name,
    category: '還款',
    account: liability.payment_account,
    date: transactionDate.toISOString(),
    is_recurring: true,
    recurring_frequency: 'monthly',
    max_occurrences: liability.payment_periods,
  };
  
  console.log('\n💰 創建的交易記錄:');
  console.log(`ID: ${transaction.id}`);
  console.log(`金額: ${transaction.amount}`);
  console.log(`描述: ${transaction.description}`);
  console.log(`類別: ${transaction.category}`);
  console.log(`帳戶: ${transaction.account}`);
  console.log(`日期 (ISO): ${transaction.date}`);
  console.log(`日期 (本地): ${new Date(transaction.date).toLocaleDateString('zh-TW')}`);
  
  return transaction;
}

const createdTransaction = simulateTransactionCreation();

// 模擬日曆標記邏輯
function simulateCalendarMarking() {
  console.log('\n📅 ===== 模擬日曆標記邏輯 =====');
  
  // 模擬現有交易
  const existingTransactions = [
    {
      id: '1',
      amount: 500,
      type: 'expense',
      description: '午餐',
      category: '餐飲',
      account: '現金',
      date: new Date(2025, 4, 20).toISOString() // 5月20日
    },
    {
      id: '2',
      amount: 80000,
      type: 'income',
      description: '薪水',
      category: '薪水',
      account: '銀行',
      date: new Date(2025, 4, 1).toISOString() // 5月1日
    }
  ];
  
  // 添加新創建的還款交易
  const allTransactions = [...existingTransactions, createdTransaction];
  
  console.log('📋 所有交易記錄:');
  allTransactions.forEach((t, index) => {
    const dateStr = t.date.split('T')[0]; // 模擬 TransactionsScreen 的邏輯
    const localDate = new Date(t.date).toLocaleDateString('zh-TW');
    console.log(`${index + 1}. ${t.description} - ${dateStr} (${localDate})`);
  });
  
  // 模擬日曆標記生成
  console.log('\n🗓️ 日曆標記:');
  const markedDates = {};
  
  allTransactions.forEach(transaction => {
    const date = transaction.date.split('T')[0]; // YYYY-MM-DD 格式
    const jsDate = new Date(transaction.date);
    const day = jsDate.getDate();
    
    if (!markedDates[date]) {
      markedDates[date] = {
        marked: true,
        transactions: []
      };
    }
    
    markedDates[date].transactions.push({
      description: transaction.description,
      amount: transaction.amount,
      type: transaction.type
    });
    
    console.log(`標記日期: ${date} (${day}號) - ${transaction.description}`);
  });
  
  return markedDates;
}

const calendarMarks = simulateCalendarMarking();

// 檢查31號是否被正確標記
function checkMay31Marking() {
  console.log('\n🎯 ===== 檢查5月31號標記 =====');
  
  const may31Key = '2025-05-31';
  const hasMay31Mark = calendarMarks[may31Key];
  
  console.log(`檢查日期鍵: ${may31Key}`);
  console.log(`是否有標記: ${hasMay31Mark ? '是' : '否'}`);
  
  if (hasMay31Mark) {
    console.log('✅ 5月31號有交易標記');
    console.log('交易詳情:');
    hasMay31Mark.transactions.forEach(t => {
      console.log(`  - ${t.description}: ${t.type === 'income' ? '+' : '-'}${t.amount}`);
    });
  } else {
    console.log('❌ 5月31號沒有交易標記');
    
    // 檢查是否有其他日期的還款交易
    console.log('\n🔍 檢查其他日期的還款交易:');
    Object.entries(calendarMarks).forEach(([date, mark]) => {
      const hasDebtPayment = mark.transactions.some(t => t.description.includes('信用卡') || t.description.includes('還款'));
      if (hasDebtPayment) {
        const jsDate = new Date(date + 'T00:00:00');
        const day = jsDate.getDate();
        console.log(`發現還款交易: ${date} (${day}號)`);
        mark.transactions.forEach(t => {
          if (t.description.includes('信用卡') || t.description.includes('還款')) {
            console.log(`  - ${t.description}: ${t.type === 'income' ? '+' : '-'}${t.amount}`);
          }
        });
      }
    });
  }
}

checkMay31Marking();

// 總結和建議
console.log('\n📊 ===== 總結和建議 =====');

const may31Mark = calendarMarks['2025-05-31'];
const hasCorrectMay31Mark = may31Mark && may31Mark.transactions.some(t => 
  (t.description.includes('信用卡') || t.description.includes('還款')) && t.amount === 10000
);

if (hasCorrectMay31Mark) {
  console.log('✅ 修復成功: 5月31號正確顯示還款交易');
} else {
  console.log('❌ 修復失敗: 5月31號沒有正確的還款交易標記');
  
  console.log('\n🔧 可能的問題:');
  console.log('1. 交易記錄的日期被錯誤計算');
  console.log('2. 日期格式化問題');
  console.log('3. 時區問題導致日期偏移');
  console.log('4. 修復代碼沒有被正確執行');
  
  console.log('\n🛠️ 建議的解決方案:');
  console.log('1. 檢查瀏覽器控制台是否有 "🔥🔥🔥 修復" 相關日誌');
  console.log('2. 清除瀏覽器緩存並重新啟動應用');
  console.log('3. 檢查交易記錄的實際日期值');
  console.log('4. 驗證日期創建邏輯是否正確執行');
}

console.log('\n✨ 調試完成！');
