/**
 * 從 CSV 檔案匯入台股資料到 Supabase
 * 使用您提供的真實台股資料
 */

import { createClient } from '@supabase/supabase-js';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';

// 載入環境變數
dotenv.config();

// 型別定義
interface StockData {
  code: string;
  name: string;
  market_type: 'TSE' | 'OTC' | 'ETF';
  closing_price: number;
  price_date: string;
}

// Supabase 客戶端
const supabase = createClient(
  process.env.EXPO_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

class CSVStockImporter {
  private csvFile = path.join(__dirname, '../STOCK_DAY_AVG_ALL.json');

  /**
   * 解析 CSV 檔案
   */
  parseCSVFile(): StockData[] {
    try {
      console.log('📁 讀取 CSV 檔案:', this.csvFile);
      
      if (!fs.existsSync(this.csvFile)) {
        throw new Error(`檔案不存在: ${this.csvFile}`);
      }

      const csvContent = fs.readFileSync(this.csvFile, 'utf8');
      const lines = csvContent.trim().split('\n');
      
      console.log(`📊 總行數: ${lines.length}`);
      
      // 跳過標題行
      const dataLines = lines.slice(1);
      const stocks: StockData[] = [];
      const today = new Date().toISOString().split('T')[0];

      dataLines.forEach((line, index) => {
        try {
          // 移除引號並分割 CSV
          const cleanLine = line.replace(/"/g, '');
          const parts = cleanLine.split(',');
          
          if (parts.length >= 4) {
            let [date, codeRaw, nameRaw, closingPriceRaw] = parts;

            // 清理代號和名稱格式
            let code = codeRaw.replace('Code:', '').trim();
            let name = nameRaw.replace('Name:', '').trim();

            // 跳過太長的代號 (資料庫限制 10 字符)
            if (code.length > 10) {
              console.warn(`⚠️ 跳過代號太長: ${code} (${code.length} 字符)`);
              return;
            }

            // 處理價格格式 "ClosingPrice:179.75" -> "179.75"
            let priceStr = closingPriceRaw;
            if (priceStr.includes('ClosingPrice:')) {
              priceStr = priceStr.replace('ClosingPrice:', '');
            }

            const price = parseFloat(priceStr);
            if (isNaN(price) || price <= 0 || priceStr.trim() === '') {
              console.warn(`⚠️ 跳過無效價格: ${code} ${name} - 價格: "${closingPriceRaw}"`);
              return;
            }

            // 判斷市場類型 (更精確的分類)
            let market_type: 'TSE' | 'OTC' | 'ETF';
            if (code.startsWith('00') && code.length <= 5) {
              market_type = 'ETF';
            } else if (code.match(/^[1-9]\d{3}$/)) {
              // 4位數字，1000-9999 為上市股票
              market_type = 'TSE';
            } else if (code.match(/^[1-9]\d{3}[A-Z]$/)) {
              // 4位數字+字母 為上市特別股
              market_type = 'TSE';
            } else {
              // 其他為上櫃股票
              market_type = 'OTC';
            }

            stocks.push({
              code: code.trim(),
              name: name.trim(),
              market_type,
              closing_price: price,
              price_date: today
            });
          }
        } catch (error) {
          console.warn(`⚠️ 第 ${index + 2} 行解析錯誤: ${line.substring(0, 50)}...`);
        }
      });

      console.log(`✅ 成功解析 ${stocks.length} 檔股票`);
      
      // 顯示市場分布
      const tseCount = stocks.filter(s => s.market_type === 'TSE').length;
      const otcCount = stocks.filter(s => s.market_type === 'OTC').length;
      const etfCount = stocks.filter(s => s.market_type === 'ETF').length;
      
      console.log(`📊 市場分布:`);
      console.log(`   上市 (TSE): ${tseCount} 檔`);
      console.log(`   上櫃 (OTC): ${otcCount} 檔`);
      console.log(`   ETF: ${etfCount} 檔`);

      return stocks;
    } catch (error) {
      console.error('❌ 解析 CSV 檔案失敗:', error);
      return [];
    }
  }

  /**
   * 批量更新資料庫
   */
  async updateDatabase(stocks: StockData[]): Promise<void> {
    try {
      console.log(`💾 開始更新資料庫: ${stocks.length} 檔股票...`);
      
      const batchSize = 100;
      let successCount = 0;
      let errorCount = 0;

      for (let i = 0; i < stocks.length; i += batchSize) {
        const batch = stocks.slice(i, i + batchSize);
        
        try {
          const { error } = await supabase
            .from('taiwan_stocks')
            .upsert(batch, { 
              onConflict: 'code',
              ignoreDuplicates: false 
            });

          if (error) {
            console.error(`❌ 批次 ${Math.floor(i/batchSize) + 1} 更新失敗:`, error);
            errorCount += batch.length;
          } else {
            successCount += batch.length;
            console.log(`✅ 批次 ${Math.floor(i/batchSize) + 1} 更新成功: ${batch.length} 檔`);
          }
        } catch (error) {
          console.error(`❌ 批次 ${Math.floor(i/batchSize) + 1} 執行錯誤:`, error);
          errorCount += batch.length;
        }

        // 避免 API 限制，稍作延遲
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      console.log(`🎉 資料庫更新完成!`);
      console.log(`   成功: ${successCount} 檔`);
      console.log(`   失敗: ${errorCount} 檔`);
    } catch (error) {
      console.error('❌ 資料庫更新失敗:', error);
      throw error;
    }
  }

  /**
   * 驗證資料庫資料
   */
  async verifyDatabase(): Promise<void> {
    try {
      console.log('🔍 驗證資料庫資料...');

      // 檢查總數
      const { count, error: countError } = await supabase
        .from('taiwan_stocks')
        .select('*', { count: 'exact', head: true });

      if (countError) {
        throw countError;
      }

      console.log(`📊 資料庫總股票數: ${count}`);

      // 檢查市場分布
      const { data: summary, error: summaryError } = await supabase
        .from('v_stock_summary')
        .select('*');

      if (summaryError) {
        throw summaryError;
      }

      console.log('📈 市場統計:');
      summary?.forEach(item => {
        console.log(`   ${item.market_type}: ${item.stock_count} 檔, 平均價格: NT$${item.avg_price}`);
      });

      // 測試搜尋功能
      const { data: searchResult, error: searchError } = await supabase
        .rpc('search_stocks', { search_term: '台積', limit_count: 3 });

      if (searchError) {
        console.warn('⚠️ 搜尋功能測試失敗:', searchError);
      } else {
        console.log('🔍 搜尋測試結果:');
        searchResult?.forEach((stock: any) => {
          console.log(`   ${stock.code} ${stock.name} NT$${stock.closing_price}`);
        });
      }

    } catch (error) {
      console.error('❌ 驗證失敗:', error);
    }
  }

  /**
   * 主要執行函數
   */
  async execute(): Promise<void> {
    try {
      console.log('🚀 開始匯入台股 CSV 資料...');
      console.log(`⏰ 執行時間: ${new Date().toLocaleString('zh-TW')}`);

      // 解析 CSV 檔案
      const stocks = this.parseCSVFile();
      
      if (stocks.length === 0) {
        console.warn('⚠️ 沒有解析到任何股票資料');
        return;
      }

      // 更新資料庫
      await this.updateDatabase(stocks);

      // 驗證結果
      await this.verifyDatabase();

      console.log('🎉 CSV 資料匯入完成!');
    } catch (error) {
      console.error('❌ 執行失敗:', error);
      process.exit(1);
    }
  }
}

// 執行腳本
if (require.main === module) {
  const importer = new CSVStockImporter();
  importer.execute();
}

export default CSVStockImporter;
