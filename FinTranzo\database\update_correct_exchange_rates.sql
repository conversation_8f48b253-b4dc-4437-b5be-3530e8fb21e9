-- =====================================================
-- 更新正確的匯率資料
-- 2025-06-01 即期中間價: 29.925
-- =====================================================

-- 刪除舊的測試匯率資料
DELETE FROM exchange_rates WHERE currency = 'USD';

-- 插入正確的 2025-06-01 匯率資料
-- 即期買入：29.900，即期賣出：29.950，中間價：29.925
INSERT INTO exchange_rates (date, currency, cash_buy, cash_sell, spot_buy, spot_sell) VALUES
('2025-06-01', 'USD', 29.800, 30.050, 29.900, 29.950),
('2025-05-31', 'USD', 29.850, 30.100, 29.920, 29.970),
('2025-05-30', 'USD', 29.900, 30.150, 29.940, 29.990),
('2025-05-29', 'USD', 29.950, 30.200, 29.960, 30.010),
('2025-05-28', 'USD', 30.000, 30.250, 29.980, 30.030)
ON CONFLICT (date, currency) DO UPDATE SET
    cash_buy = EXCLUDED.cash_buy,
    cash_sell = EXCLUDED.cash_sell,
    spot_buy = EXCLUDED.spot_buy,
    spot_sell = EXCLUDED.spot_sell,
    updated_at = NOW();

-- 驗證更新後的匯率資料
SELECT 
    '✅ 更新後匯率驗證' as check_type,
    date,
    currency,
    cash_buy,
    cash_sell,
    spot_buy,
    spot_sell
FROM exchange_rates 
ORDER BY date DESC;

-- 添加即期中間價計算函數
CREATE OR REPLACE FUNCTION get_spot_mid_rate(
    target_currency VARCHAR(3) DEFAULT 'USD'
)
RETURNS DECIMAL(8,3) AS $$
DECLARE
    mid_rate DECIMAL(8,3);
BEGIN
    SELECT (spot_buy + spot_sell) / 2.0 INTO mid_rate
    FROM exchange_rates
    WHERE currency = target_currency
    ORDER BY date DESC
    LIMIT 1;

    -- 如果沒有資料，返回預設中間價
    IF mid_rate IS NULL THEN
        mid_rate := 29.925; -- 2025-06-01 即期中間價
    END IF;

    RETURN mid_rate;
END;
$$ LANGUAGE plpgsql;

-- 測試即期中間價功能
SELECT
    '✅ 即期中間價測試' as test_type,
    get_spot_mid_rate('USD') as spot_mid_rate;

-- 測試匯率轉換功能 (使用即期中間價)
SELECT
    '✅ 匯率轉換測試' as test_type,
    convert_usd_to_twd(100.00) as usd_100_to_twd,
    convert_twd_to_usd(2992.50) as twd_2992_to_usd;

-- 測試獲取最新匯率
SELECT 
    '✅ 最新匯率測試' as test_type,
    * 
FROM get_latest_exchange_rate('USD');

SELECT 
    '🎉 匯率資料更新完成！' as status,
    '當前美元匯率約 29.917' as current_rate,
    NOW() as update_time;
