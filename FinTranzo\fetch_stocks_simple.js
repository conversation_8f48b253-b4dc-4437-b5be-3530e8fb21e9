// 簡化版台股資料獲取腳本
const https = require('https');
require('dotenv').config();

const { createClient } = require('@supabase/supabase-js');

// Supabase 配置
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ 環境變數未設置');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🚀 開始獲取台股資料...');
console.log('⏰ 開始時間:', new Date().toLocaleString('zh-TW'));

// 使用 https 模組獲取資料
function fetchStockData() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'openapi.twse.com.tw',
      path: '/v1/exchangeReport/STOCK_DAY_AVG_ALL',
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve(jsonData);
        } catch (error) {
          reject(new Error('JSON 解析失敗: ' + error.message));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

// 處理股票資料
function processStockData(rawData) {
  console.log('🔄 處理股票資料...');

  const processedData = [];
  let validCount = 0;

  rawData.forEach((stock, index) => {
    // 只處理4位數股票代號
    if (!/^\d{4}$/.test(stock.Code)) {
      return;
    }

    // 解析價格
    const closingPrice = parseFloat(stock.ClosingPrice?.replace(/,/g, '') || '0');
    const monthlyAvgPrice = parseFloat(stock.MonthlyAveragePrice?.replace(/,/g, '') || '0');

    if (closingPrice <= 0) {
      return;
    }

    // 轉換日期格式 (1140529 -> 2025-05-29)
    let dateStr = stock.Date;
    if (dateStr && dateStr.length === 7) {
      const year = parseInt(dateStr.substring(0, 3)) + 1911; // 民國年轉西元年
      const month = dateStr.substring(3, 5);
      const day = dateStr.substring(5, 7);
      dateStr = `${year}-${month}-${day}`;
    }

    processedData.push({
      code: stock.Code,
      name: stock.Name,
      closing_price: closingPrice,
      date: dateStr
    });

    validCount++;
  });

  console.log(`✅ 處理完成，有效資料: ${validCount} 筆`);
  return processedData;
}

// 更新資料庫
async function updateDatabase(stockData) {
  console.log('💾 開始更新資料庫...');

  const batchSize = 50;
  let totalUpdated = 0;

  for (let i = 0; i < stockData.length; i += batchSize) {
    const batch = stockData.slice(i, i + batchSize);
    const batchNum = Math.floor(i / batchSize) + 1;
    const totalBatches = Math.ceil(stockData.length / batchSize);

    console.log(`📦 處理第 ${batchNum}/${totalBatches} 批 (${batch.length} 筆)...`);

    try {
      const { data, error } = await supabase.rpc('update_daily_stock_prices', {
        stock_data: batch
      });

      if (error) {
        console.error(`❌ 第 ${batchNum} 批失敗:`, error.message);
      } else {
        totalUpdated += batch.length;
        console.log(`✅ 第 ${batchNum} 批完成`);
      }

      // 短暫延遲
      await new Promise(resolve => setTimeout(resolve, 100));

    } catch (error) {
      console.error(`❌ 第 ${batchNum} 批錯誤:`, error.message);
    }
  }

  console.log(`🎉 資料庫更新完成，共 ${totalUpdated} 筆`);
  return totalUpdated;
}

// 主要執行函數
async function main() {
  try {
    // 1. 獲取資料
    console.log('📡 從台灣證交所獲取資料...');
    const rawData = await fetchStockData();
    console.log(`✅ 獲取成功，原始資料: ${rawData.length} 筆`);

    // 2. 處理資料
    const processedData = processStockData(rawData);

    if (processedData.length === 0) {
      console.error('❌ 沒有有效的股票資料');
      return;
    }

    // 3. 更新資料庫
    const updatedCount = await updateDatabase(processedData);

    // 4. 顯示結果
    console.log('\n🎉 執行完成！');
    console.log(`📊 更新統計: ${updatedCount} 筆股票資料`);
    console.log(`📅 資料日期: ${processedData[0]?.date || '未知'}`);
    console.log('⏰ 完成時間:', new Date().toLocaleString('zh-TW'));

    // 顯示範例資料
    console.log('\n📋 範例股票資料:');
    processedData.slice(0, 10).forEach(stock => {
      console.log(`  ${stock.code} ${stock.name}: NT$${stock.closing_price}`);
    });

  } catch (error) {
    console.error('❌ 執行失敗:', error.message);
  }
}

// 執行
main();
