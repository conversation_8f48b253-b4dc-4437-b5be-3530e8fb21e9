// 測試台股 API 和資料庫連接
console.log('🚀 開始測試台股系統...');

// 載入環境變數
require('dotenv').config();

console.log('📋 環境變數檢查:');
console.log('- EXPO_PUBLIC_SUPABASE_URL:', !!process.env.EXPO_PUBLIC_SUPABASE_URL);
console.log('- SUPABASE_SERVICE_ROLE_KEY:', !!process.env.SUPABASE_SERVICE_ROLE_KEY);

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ 環境變數未設置');
  process.exit(1);
}

console.log('✅ 環境變數正常');

// 測試 Supabase 連接
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testDatabase() {
  try {
    console.log('🔍 測試資料庫連接...');

    // 測試簡單查詢
    const { data, error } = await supabase
      .from('taiwan_stocks')
      .select('count(*)', { count: 'exact', head: true });

    if (error) {
      console.error('❌ 資料庫查詢失敗:', error.message);
      return false;
    }

    console.log('✅ 資料庫連接成功');
    return true;

  } catch (error) {
    console.error('❌ 資料庫測試失敗:', error.message);
    return false;
  }
}

async function testAPI() {
  try {
    console.log('🌐 測試台灣證交所 API...');

    // 使用 node-fetch 或內建 fetch
    const fetch = globalThis.fetch || require('node-fetch');

    const response = await fetch('https://openapi.twse.com.tw/v1/exchangeReport/STOCK_DAY_AVG_ALL', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    if (!response.ok) {
      console.error('❌ API 請求失敗:', response.status, response.statusText);
      return false;
    }

    const data = await response.json();
    console.log('✅ API 連接成功，獲得', data.length, '筆資料');

    // 顯示前3筆資料
    console.log('📊 範例資料:');
    data.slice(0, 3).forEach((stock, index) => {
      console.log(`  ${index + 1}. ${stock.Code} ${stock.Name}: ${stock.ClosingPrice}`);
    });

    return true;

  } catch (error) {
    console.error('❌ API 測試失敗:', error.message);
    return false;
  }
}

async function main() {
  console.log('⏰ 開始時間:', new Date().toLocaleString('zh-TW'));

  const dbOk = await testDatabase();
  const apiOk = await testAPI();

  if (dbOk && apiOk) {
    console.log('🎉 所有測試通過！可以執行完整的資料獲取腳本');
  } else {
    console.log('❌ 測試失敗，請檢查問題');
  }

  console.log('⏰ 結束時間:', new Date().toLocaleString('zh-TW'));
}

main().catch(console.error);
