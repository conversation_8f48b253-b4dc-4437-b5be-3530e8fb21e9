#!/usr/bin/env python3
"""
檢查搜索函數的定義和結果
"""

import requests

# Supabase配置
SUPABASE_URL = "https://yrryyapzkgrsahranzvo.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlycnl5YXB6a2dyc2FocmFuenZvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODE3MzYzNSwiZXhwIjoyMDYzNzQ5NjM1fQ.xjiXVagzKgBBYXqtRjJiXwCgbRdEW0EpFzCIlJ26SSQ"

def make_supabase_request(endpoint, method='GET', data=None):
    """發送Supabase請求"""
    url = f"{SUPABASE_URL}/rest/v1/{endpoint}"
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'application/json',
        'Prefer': 'return=representation'
    }
    
    try:
        if method == 'GET':
            response = requests.get(url, headers=headers)
        elif method == 'POST':
            response = requests.post(url, headers=headers, json=data)
        
        response.raise_for_status()
        return response.json() if response.content else []
    except Exception as e:
        print(f"❌ 請求失敗: {str(e)}")
        if hasattr(e, 'response') and e.response:
            print(f"   響應內容: {e.response.text}")
        return None

def test_direct_query():
    """直接查詢數據庫測試"""
    print("🔍 直接查詢數據庫中的ETF:")
    print("=" * 50)
    
    # 直接查詢ETF
    etfs = make_supabase_request('us_stocks?is_etf=eq.true&symbol=ilike.*qqq*&select=symbol,name,is_etf,asset_type&limit=5')
    
    if etfs:
        print(f"✅ 直接查詢找到 {len(etfs)} 個ETF:")
        for etf in etfs:
            print(f"   {etf['symbol']}: {etf['name']}")
            print(f"      is_etf: {etf['is_etf']} ✅")
            print(f"      asset_type: {etf['asset_type']}")
            print()
    else:
        print("❌ 直接查詢失敗")

def test_etf_search_function():
    """測試ETF搜索函數"""
    print("🔍 測試ETF搜索函數:")
    print("=" * 50)
    
    search_data = {
        'search_term': 'qqq',
        'limit_count': 5
    }
    
    results = make_supabase_request('rpc/search_us_etf', method='POST', data=search_data)
    
    if results:
        print(f"✅ 函數搜索找到 {len(results)} 個結果:")
        for result in results:
            is_etf = result.get('is_etf', 'N/A')
            asset_type = result.get('asset_type', 'N/A')
            print(f"   {result['symbol']}: {result['name']}")
            print(f"      is_etf: {is_etf} {'✅' if is_etf else '❌'}")
            print(f"      asset_type: {asset_type}")
            print()
    else:
        print("❌ 函數搜索失敗")

def test_stock_search_function():
    """測試普通股票搜索函數"""
    print("🔍 測試普通股票搜索函數:")
    print("=" * 50)
    
    search_data = {
        'search_term': 'qqq',
        'sp500_only': False,
        'limit_count': 5
    }
    
    results = make_supabase_request('rpc/search_us_stocks', method='POST', data=search_data)
    
    if results:
        print(f"✅ 股票搜索找到 {len(results)} 個結果:")
        for result in results:
            is_etf = result.get('is_etf', 'N/A')
            asset_type = result.get('asset_type', 'N/A')
            print(f"   {result['symbol']}: {result['name']}")
            print(f"      is_etf: {is_etf} {'✅' if is_etf else '❌'}")
            print(f"      asset_type: {asset_type}")
            print()
    else:
        print("❌ 股票搜索失敗")

def main():
    """主函數"""
    print("🔧 搜索函數檢查工具")
    print("=" * 50)
    
    # 1. 直接查詢測試
    test_direct_query()
    
    # 2. ETF搜索函數測試
    test_etf_search_function()
    
    # 3. 普通股票搜索函數測試
    test_stock_search_function()

if __name__ == "__main__":
    main()
