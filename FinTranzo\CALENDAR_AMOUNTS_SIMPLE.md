# 📅 月曆收支總額顯示功能 - 簡化版本

## 🎯 功能概述

由於 `react-native-calendars` 的自定義日期組件實現複雜，我們採用了一個更實用的解決方案：

**在月曆下方顯示當月收支總額摘要**

## 📱 視覺效果

```
6 月 2025
[月曆顯示區域]

📊 本月收支總額一覽
┌─────────────────────────────────────┐
│  1日     15日     16日     25日     │
│ -5,000  +30,000   -776   +1,252    │
│  1筆     1筆      1筆     1筆      │
│                                     │
│  27日     30日                      │
│ -786     -679                       │
│  1筆     1筆                        │
└─────────────────────────────────────┘
```

## 🔧 技術實現

### 1. 每日收支總額摘要

```typescript
const renderDailyAmountSummary = () => {
  const currentDate = new Date(currentMonth);
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();

  // 獲取當月所有有交易的日期
  const daysWithTransactions = [];
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);

  for (let day = 1; day <= lastDay.getDate(); day++) {
    const date = new Date(year, month, day);
    const dateString = date.toISOString().split('T')[0];
    const summary = getDayTransactionSummary(dateString);
    
    if (summary.count > 0) {
      const netAmount = summary.income - summary.expense;
      daysWithTransactions.push({
        date: dateString,
        day: day,
        netAmount,
        income: summary.income,
        expense: summary.expense,
        count: summary.count,
      });
    }
  }

  return (
    <View style={styles.dailySummaryGrid}>
      <Text style={styles.summaryTitle}>📊 本月收支總額一覽</Text>
      <View style={styles.summaryGrid}>
        {daysWithTransactions.map((dayData) => (
          <View key={dayData.date} style={styles.summaryItem}>
            <Text style={styles.summaryDate}>{dayData.day}日</Text>
            <Text style={[
              styles.summaryAmount,
              dayData.netAmount > 0 ? styles.positiveAmount : styles.negativeAmount,
            ]}>
              {formatNetAmount(dayData.netAmount)}
            </Text>
            <Text style={styles.summaryCount}>{dayData.count}筆</Text>
          </View>
        ))}
      </View>
    </View>
  );
};
```

### 2. 金額格式化

```typescript
const formatNetAmount = (amount: number) => {
  if (amount === 0) return '';
  const absAmount = Math.abs(amount);
  
  // 簡化顯示：超過萬元顯示萬
  let formattedAmount: string;
  if (absAmount >= 10000) {
    const wanAmount = Math.round(absAmount / 1000) / 10;
    formattedAmount = wanAmount % 1 === 0 ? `${Math.round(wanAmount)}萬` : `${wanAmount}萬`;
  } else {
    formattedAmount = absAmount.toLocaleString();
  }
  
  return amount > 0 ? `+${formattedAmount}` : `-${formattedAmount}`;
};
```

## 🎨 樣式配置

### 摘要容器樣式

```typescript
amountSummaryContainer: {
  backgroundColor: '#fff',
  margin: 16,
  borderRadius: 12,
  padding: 16,
  shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 8,
  elevation: 3,
},
```

### 摘要項目樣式

```typescript
summaryItem: {
  width: '30%',
  backgroundColor: '#f8f9fa',
  borderRadius: 8,
  padding: 12,
  marginBottom: 8,
  alignItems: 'center',
},
summaryAmount: {
  fontSize: 16,
  fontWeight: 'bold',
  marginBottom: 2,
},
positiveAmount: {
  color: '#34C759', // 綠色
},
negativeAmount: {
  color: '#FF3B30', // 紅色
},
```

## 🧪 測試方法

### 1. 自動添加測試資料

在開發模式下，應用程式會自動添加測試交易：

```typescript
// 開發模式下添加測試資料
if (__DEV__) {
  const currentTransactions = transactionDataService.getTransactions();
  if (currentTransactions.length === 0) {
    console.log('🧪 添加月曆測試交易資料...');
    const testTransactions = [
      {
        amount: 5000,
        type: 'expense' as const,
        description: '餐飲',
        category: '餐飲',
        account: '現金',
        date: new Date().toISOString(),
      },
      {
        amount: 30000,
        type: 'income' as const,
        description: '薪水',
        category: '薪水',
        account: '銀行',
        date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 昨天
      },
    ];
    
    testTransactions.forEach(transaction => {
      transactionDataService.addTransaction(transaction);
    });
    
    console.log('✅ 月曆測試資料已添加');
  }
}
```

### 2. 手動測試步驟

1. **啟動應用程式**
   ```bash
   cd FinTranzo
   npx expo start --clear
   ```

2. **進入記帳頁面**
   - 點擊底部導航的「記帳」

3. **查看收支總額摘要**
   - 月曆下方會顯示「📊 本月收支總額一覽」
   - 每個有交易的日期會顯示為一個卡片
   - 卡片包含：日期、收支總額、交易筆數

4. **添加更多交易**
   - 點擊「+」按鈕添加交易
   - 摘要會自動更新

## 📊 預期顯示效果

### 測試資料範例

| 日期 | 收入 | 支出 | 淨額 | 顯示 | 顏色 |
|------|------|------|------|------|------|
| 今天 | 0 | 5,000 | -5,000 | -5,000 (1筆) | 紅色 |
| 昨天 | 30,000 | 0 | +30,000 | +3萬 (1筆) | 綠色 |

## ✅ 功能優勢

### 🎯 **實用性**
- **清晰易讀**：每日收支一目了然
- **快速瀏覽**：不需要點擊每個日期
- **智能摘要**：只顯示有交易的日期

### 🔧 **技術穩定性**
- **兼容性好**：不依賴複雜的自定義組件
- **性能優秀**：渲染效率高
- **維護簡單**：代碼結構清晰

### 📱 **用戶體驗**
- **視覺清晰**：顏色區分收入/支出
- **信息完整**：顯示金額和交易筆數
- **響應迅速**：月份切換時自動更新

## 🚀 使用說明

1. **查看月曆**：進入記帳頁面即可看到月曆
2. **查看摘要**：月曆下方顯示當月收支總額摘要
3. **理解顏色**：綠色表示收入日，紅色表示支出日
4. **查看詳情**：點擊月曆中的日期查看詳細交易
5. **切換月份**：滑動或點擊標題切換月份，摘要自動更新

這個簡化版本提供了實用的收支總額顯示功能，雖然不是直接在月曆日期下方顯示，但提供了更清晰、更完整的信息展示。
