// 測試台股 API 和 Supabase 設置的腳本
// 這個腳本會檢查 Supabase 連接、測試台股 API，並設置初始數據

const { createClient } = require('@supabase/supabase-js');
const fetch = require('node-fetch');

// 從環境變數讀取 Supabase 配置
const supabaseUrl = 'https://yrryyapzkgrsahranzvo.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlycnl5YXB6a2dyc2FocmFuenZvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxNzM2MzUsImV4cCI6MjA2Mzc0OTYzNX0.TccJJ9KGG6R4KiaDb-548kRkhTaPMODYa6vlQsj8dmM';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 台灣證交所 API 端點
const TWSE_API_URL = 'https://openapi.twse.com.tw/v1/exchangeReport/STOCK_DAY_AVG_ALL';

async function testSupabaseConnection() {
  console.log('🔍 測試 Supabase 連接...');
  
  try {
    const { data, error } = await supabase
      .from('taiwan_stocks')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('❌ Supabase 連接失敗:', error.message);
      return false;
    }
    
    console.log('✅ Supabase 連接成功');
    return true;
  } catch (error) {
    console.error('❌ Supabase 連接測試失敗:', error.message);
    return false;
  }
}

async function testTaiwanStockAPI() {
  console.log('🔍 測試台灣證交所 API...');
  
  try {
    const response = await fetch(TWSE_API_URL);
    
    if (!response.ok) {
      throw new Error(`API 請求失敗: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    console.log(`✅ 台股 API 連接成功，獲取到 ${data.length} 筆股票資料`);
    
    // 顯示前3筆資料作為範例
    if (data.length > 0) {
      console.log('📊 範例資料:');
      data.slice(0, 3).forEach((stock, index) => {
        console.log(`  ${index + 1}. ${stock.Code} ${stock.Name} - NT$${stock.ClosingPrice}`);
      });
    }
    
    return data;
  } catch (error) {
    console.error('❌ 台股 API 測試失敗:', error.message);
    return null;
  }
}

async function checkDatabaseSchema() {
  console.log('🔍 檢查數據庫表結構...');
  
  try {
    // 檢查 taiwan_stocks 表是否存在
    const { data, error } = await supabase
      .from('taiwan_stocks')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('❌ taiwan_stocks 表不存在或無法訪問:', error.message);
      console.log('💡 請在 Supabase SQL Editor 中執行 taiwan_stocks_schema.sql');
      return false;
    }
    
    console.log('✅ taiwan_stocks 表存在');
    
    // 檢查是否有數據
    const { count } = await supabase
      .from('taiwan_stocks')
      .select('*', { count: 'exact', head: true });
    
    console.log(`📊 taiwan_stocks 表中有 ${count || 0} 筆記錄`);
    
    return true;
  } catch (error) {
    console.error('❌ 檢查數據庫表結構失敗:', error.message);
    return false;
  }
}

async function insertSampleData() {
  console.log('🔄 插入範例台股資料...');
  
  // 範例台股資料
  const sampleStocks = [
    {
      date: new Date().toISOString().split('T')[0],
      code: '2330',
      name: '台積電',
      closing_price: 1000.00,
      monthly_average_price: 995.50
    },
    {
      date: new Date().toISOString().split('T')[0],
      code: '2317',
      name: '鴻海',
      closing_price: 180.50,
      monthly_average_price: 178.20
    },
    {
      date: new Date().toISOString().split('T')[0],
      code: '2454',
      name: '聯發科',
      closing_price: 1200.00,
      monthly_average_price: 1180.30
    },
    {
      date: new Date().toISOString().split('T')[0],
      code: '2881',
      name: '富邦金',
      closing_price: 85.20,
      monthly_average_price: 83.50
    },
    {
      date: new Date().toISOString().split('T')[0],
      code: '2412',
      name: '中華電',
      closing_price: 125.50,
      monthly_average_price: 124.80
    }
  ];
  
  try {
    const { data, error } = await supabase
      .from('taiwan_stocks')
      .upsert(sampleStocks, {
        onConflict: 'date,code'
      });
    
    if (error) {
      console.error('❌ 插入範例資料失敗:', error.message);
      return false;
    }
    
    console.log('✅ 成功插入範例台股資料');
    return true;
  } catch (error) {
    console.error('❌ 插入範例資料失敗:', error.message);
    return false;
  }
}

async function testStockSearch() {
  console.log('🔍 測試股票搜尋功能...');
  
  try {
    // 測試搜尋台積電
    const { data, error } = await supabase
      .rpc('search_stocks', { search_term: '2330' });
    
    if (error) {
      console.error('❌ 股票搜尋失敗:', error.message);
      return false;
    }
    
    if (data && data.length > 0) {
      console.log('✅ 股票搜尋功能正常');
      console.log('📊 搜尋結果:');
      data.forEach((stock, index) => {
        console.log(`  ${index + 1}. ${stock.code} ${stock.name} - NT$${stock.closing_price}`);
      });
    } else {
      console.log('⚠️ 搜尋結果為空，可能需要先插入資料');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 測試股票搜尋失敗:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 開始台股 API 和 Supabase 設置測試\n');
  
  // 1. 測試 Supabase 連接
  const supabaseOk = await testSupabaseConnection();
  if (!supabaseOk) {
    console.log('❌ Supabase 連接失敗，請檢查配置');
    return;
  }
  
  console.log('');
  
  // 2. 檢查數據庫表結構
  const schemaOk = await checkDatabaseSchema();
  if (!schemaOk) {
    console.log('❌ 數據庫表結構不正確，請先執行 SQL 腳本');
    return;
  }
  
  console.log('');
  
  // 3. 插入範例資料
  await insertSampleData();
  
  console.log('');
  
  // 4. 測試股票搜尋
  await testStockSearch();
  
  console.log('');
  
  // 5. 測試台股 API（可選）
  console.log('🔍 測試台股 API（可能需要較長時間）...');
  const apiData = await testTaiwanStockAPI();
  
  console.log('\n🎉 測試完成！');
  console.log('\n📋 下一步：');
  console.log('1. 確保 Supabase 中有 taiwan_stocks 表和相關函數');
  console.log('2. 在 React Native 應用中測試股票搜尋功能');
  console.log('3. 在資產管理中選擇台股類型並測試自動填入功能');
}

// 執行測試
main().catch(console.error);
