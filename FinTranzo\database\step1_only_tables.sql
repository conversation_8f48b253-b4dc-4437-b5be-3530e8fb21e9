-- =====================================================
-- 步驟 1: 只建立資料表 (不包含函數)
-- FinTranzo Supabase 設定 - 安全版本
-- 執行日期: 2025-06-01
-- =====================================================

-- 建立市場類型枚舉
DO $$ BEGIN
    CREATE TYPE market_type AS ENUM ('TSE', 'OTC', 'ETF');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- =====================================================
-- 1. 台股資料表
-- =====================================================

-- 建立台股資料表
CREATE TABLE IF NOT EXISTS taiwan_stocks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    code VARCHAR(10) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    market_type market_type NOT NULL,
    closing_price DECIMAL(10,2),
    volume BIGINT,
    change_amount DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    price_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 台股索引
CREATE INDEX IF NOT EXISTS idx_taiwan_stocks_code ON taiwan_stocks(code);
CREATE INDEX IF NOT EXISTS idx_taiwan_stocks_name ON taiwan_stocks(name);
CREATE INDEX IF NOT EXISTS idx_taiwan_stocks_market_type ON taiwan_stocks(market_type);
CREATE INDEX IF NOT EXISTS idx_taiwan_stocks_price_date ON taiwan_stocks(price_date DESC);
CREATE INDEX IF NOT EXISTS idx_taiwan_stocks_code_search ON taiwan_stocks(code varchar_pattern_ops);
CREATE INDEX IF NOT EXISTS idx_taiwan_stocks_name_search ON taiwan_stocks(name varchar_pattern_ops);

-- =====================================================
-- 2. 匯率資料表
-- =====================================================

-- 建立匯率資料表
CREATE TABLE IF NOT EXISTS exchange_rates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    date DATE NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    cash_buy DECIMAL(8,3) NOT NULL,
    cash_sell DECIMAL(8,3) NOT NULL,
    spot_buy DECIMAL(8,3) NOT NULL,
    spot_sell DECIMAL(8,3) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 匯率索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_exchange_rates_date_currency 
ON exchange_rates(date, currency);

CREATE INDEX IF NOT EXISTS idx_exchange_rates_date 
ON exchange_rates(date DESC);

CREATE INDEX IF NOT EXISTS idx_exchange_rates_currency 
ON exchange_rates(currency);

-- =====================================================
-- 3. 更新時間觸發器函數
-- =====================================================

-- 台股更新時間觸發器函數
CREATE OR REPLACE FUNCTION update_taiwan_stocks_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 匯率更新時間觸發器函數
CREATE OR REPLACE FUNCTION update_exchange_rates_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. 建立觸發器
-- =====================================================

-- 台股觸發器
DROP TRIGGER IF EXISTS trigger_update_taiwan_stocks_updated_at ON taiwan_stocks;
CREATE TRIGGER trigger_update_taiwan_stocks_updated_at
    BEFORE UPDATE ON taiwan_stocks
    FOR EACH ROW
    EXECUTE FUNCTION update_taiwan_stocks_updated_at();

-- 匯率觸發器
DROP TRIGGER IF EXISTS trigger_update_exchange_rates_updated_at ON exchange_rates;
CREATE TRIGGER trigger_update_exchange_rates_updated_at
    BEFORE UPDATE ON exchange_rates
    FOR EACH ROW
    EXECUTE FUNCTION update_exchange_rates_updated_at();

-- =====================================================
-- 5. RLS (Row Level Security) 政策
-- =====================================================

-- 台股表 RLS
ALTER TABLE taiwan_stocks ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Allow public read access" ON taiwan_stocks;
CREATE POLICY "Allow public read access" ON taiwan_stocks
    FOR SELECT USING (true);

DROP POLICY IF EXISTS "Allow service role write access" ON taiwan_stocks;
CREATE POLICY "Allow service role write access" ON taiwan_stocks
    FOR ALL USING (auth.role() = 'service_role');

-- 匯率表 RLS
ALTER TABLE exchange_rates ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Allow public read access" ON exchange_rates;
CREATE POLICY "Allow public read access" ON exchange_rates
    FOR SELECT USING (true);

DROP POLICY IF EXISTS "Allow service role write access" ON exchange_rates;
CREATE POLICY "Allow service role write access" ON exchange_rates
    FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- 6. 插入測試資料
-- =====================================================

-- 插入台股測試資料
INSERT INTO taiwan_stocks (code, name, market_type, closing_price, volume, price_date) VALUES
('2330', '台積電', 'TSE', 967.00, 50000000, '2025-06-01'),
('2317', '鴻海', 'TSE', 156.00, 30000000, '2025-06-01'),
('2454', '聯發科', 'TSE', 1200.00, 15000000, '2025-06-01'),
('0050', '元大台灣50', 'ETF', 179.75, 8000000, '2025-06-01'),
('0056', '元大高股息', 'ETF', 34.08, 25000000, '2025-06-01'),
('8446', '華研', 'OTC', 156.00, 2000000, '2025-06-01'),
('3293', '鈊象', 'OTC', 780.00, 1500000, '2025-06-01')
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    market_type = EXCLUDED.market_type,
    closing_price = EXCLUDED.closing_price,
    volume = EXCLUDED.volume,
    price_date = EXCLUDED.price_date,
    updated_at = NOW();

-- 插入匯率測試資料 (2025-06-01 及前幾天)
INSERT INTO exchange_rates (date, currency, cash_buy, cash_sell, spot_buy, spot_sell) VALUES
('2025-06-01', 'USD', 31.000, 31.500, 31.200, 31.300),
('2025-05-31', 'USD', 30.950, 31.450, 31.150, 31.250),
('2025-05-30', 'USD', 31.100, 31.600, 31.300, 31.400),
('2025-05-29', 'USD', 31.050, 31.550, 31.250, 31.350),
('2025-05-28', 'USD', 30.900, 31.400, 31.100, 31.200)
ON CONFLICT (date, currency) DO UPDATE SET
    cash_buy = EXCLUDED.cash_buy,
    cash_sell = EXCLUDED.cash_sell,
    spot_buy = EXCLUDED.spot_buy,
    spot_sell = EXCLUDED.spot_sell,
    updated_at = NOW();

-- =====================================================
-- 7. 驗證資料表建立
-- =====================================================

-- 檢查資料表是否存在
SELECT 
    '✅ 資料表檢查' as check_type,
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('taiwan_stocks', 'exchange_rates')
ORDER BY table_name;

-- 驗證台股資料
SELECT 
    '✅ 台股資料驗證' as check_type,
    COUNT(*) as total_stocks,
    COUNT(*) FILTER (WHERE market_type = 'TSE') as tse_count,
    COUNT(*) FILTER (WHERE market_type = 'OTC') as otc_count,
    COUNT(*) FILTER (WHERE market_type = 'ETF') as etf_count
FROM taiwan_stocks;

-- 驗證匯率資料
SELECT 
    '✅ 匯率資料驗證' as check_type,
    COUNT(*) as total_rates,
    MIN(date) as earliest_date,
    MAX(date) as latest_date
FROM exchange_rates;

-- 簡單查詢測試
SELECT 
    '✅ 台股查詢測試' as test_type,
    code, name, market_type, closing_price
FROM taiwan_stocks 
WHERE code = '2330';

SELECT 
    '✅ 匯率查詢測試' as test_type,
    date, currency, spot_buy, spot_sell
FROM exchange_rates 
WHERE date = '2025-06-01' AND currency = 'USD';

-- =====================================================
-- 步驟 1 完成
-- =====================================================

SELECT 
    '🎉 步驟 1 完成：資料表建立成功！' as status,
    '請繼續執行步驟 2 建立函數' as next_step,
    NOW() as setup_time;
