/**
 * SP500 批量同步腳本
 * 分批處理 SP500 股票，避免 API 限制
 * 支援斷點續傳和進度保存
 * 
 * 執行方式: 
 * node scripts/batchSyncSP500.js --batch 1    # 處理第1批 (1-50)
 * node scripts/batchSyncSP500.js --batch 2    # 處理第2批 (51-100)
 * node scripts/batchSyncSP500.js --auto       # 自動處理所有批次
 */

const fs = require('fs');
const path = require('path');

// 配置
const ALPHA_VANTAGE_API_KEY = 'QJTK95T7SA1661WM';
const ALPHA_VANTAGE_BASE_URL = 'https://www.alphavantage.co/query';
const BATCH_SIZE = 50; // 每批處理50個股票
const DELAY_BETWEEN_CALLS = 12000; // 12秒間隔
const DELAY_BETWEEN_BATCHES = 300000; // 批次間隔5分鐘

// Supabase 配置
const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co';
const SUPABASE_ANON_KEY = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key';

class BatchSP500Synchronizer {
  constructor() {
    this.progressFile = path.join(__dirname, 'sp500_sync_progress.json');
    this.logFile = path.join(__dirname, 'sp500_sync.log');
    this.progress = this.loadProgress();
  }

  /**
   * 載入進度
   */
  loadProgress() {
    try {
      if (fs.existsSync(this.progressFile)) {
        const data = fs.readFileSync(this.progressFile, 'utf-8');
        return JSON.parse(data);
      }
    } catch (error) {
      console.log('⚠️ 無法載入進度檔案，將重新開始');
    }
    
    return {
      completedBatches: [],
      failedStocks: [],
      lastBatchTime: null,
      totalProcessed: 0,
      totalSuccess: 0,
      totalFailed: 0
    };
  }

  /**
   * 儲存進度
   */
  saveProgress() {
    try {
      fs.writeFileSync(this.progressFile, JSON.stringify(this.progress, null, 2));
    } catch (error) {
      console.error('❌ 儲存進度失敗:', error);
    }
  }

  /**
   * 記錄日誌
   */
  log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}\n`;
    
    console.log(message);
    
    try {
      fs.appendFileSync(this.logFile, logMessage);
    } catch (error) {
      console.error('❌ 寫入日誌失敗:', error);
    }
  }

  /**
   * 讀取 SP500 清單
   */
  readSP500CSV() {
    try {
      const csvPath = path.join(__dirname, '../database/20250601135735.csv');
      const csvContent = fs.readFileSync(csvPath, 'utf-8');
      
      const lines = csvContent.split('\n').filter(line => line.trim());
      const stocks = [];
      
      for (const line of lines) {
        const [symbol, name] = line.split(',').map(item => item.trim());
        if (symbol && name) {
          stocks.push({
            symbol: symbol.replace(/﻿/g, ''), // 清除 BOM
            name: name
          });
        }
      }
      
      this.log(`📋 成功讀取 ${stocks.length} 個 SP500 股票`);
      return stocks;
    } catch (error) {
      this.log(`❌ 讀取 CSV 檔案失敗: ${error.message}`);
      return [];
    }
  }

  /**
   * 獲取股票批次
   */
  getStockBatch(stocks, batchNumber) {
    const startIndex = (batchNumber - 1) * BATCH_SIZE;
    const endIndex = Math.min(startIndex + BATCH_SIZE, stocks.length);
    
    if (startIndex >= stocks.length) {
      return [];
    }
    
    return stocks.slice(startIndex, endIndex);
  }

  /**
   * 計算總批次數
   */
  getTotalBatches(stocks) {
    return Math.ceil(stocks.length / BATCH_SIZE);
  }

  /**
   * 從 Alpha Vantage 獲取股票資料
   */
  async fetchStockData(symbol) {
    try {
      const params = new URLSearchParams({
        function: 'GLOBAL_QUOTE',
        symbol: symbol,
        apikey: ALPHA_VANTAGE_API_KEY,
      });

      const response = await fetch(`${ALPHA_VANTAGE_BASE_URL}?${params}`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      
      // 檢查 API 限制
      if (data.Note || data.Information) {
        if ((data.Note && data.Note.includes('API call frequency')) ||
            (data.Information && data.Information.includes('API call frequency'))) {
          throw new Error('API_LIMIT_EXCEEDED');
        }
      }

      if (!data['Global Quote'] || !data['Global Quote']['01. symbol']) {
        throw new Error('No data returned');
      }

      const quote = data['Global Quote'];
      
      return {
        symbol: quote['01. symbol'],
        price: parseFloat(quote['05. price']) || 0,
        open_price: parseFloat(quote['02. open']) || 0,
        high_price: parseFloat(quote['03. high']) || 0,
        low_price: parseFloat(quote['04. low']) || 0,
        volume: parseInt(quote['06. volume']) || 0,
        change_amount: parseFloat(quote['09. change']) || 0,
        change_percent: parseFloat(quote['10. change percent'].replace('%', '')) || 0,
        previous_close: parseFloat(quote['08. previous close']) || 0,
        price_date: quote['07. latest trading day'],
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * 儲存到 Supabase
   */
  async saveToSupabase(stockData, stockName) {
    try {
      const payload = {
        stock_symbol: stockData.symbol,
        stock_name: stockName,
        stock_price: stockData.price,
        stock_open: stockData.open_price,
        stock_high: stockData.high_price,
        stock_low: stockData.low_price,
        stock_volume: stockData.volume,
        stock_change: stockData.change_amount,
        stock_change_percent: stockData.change_percent,
        stock_previous_close: stockData.previous_close,
        is_sp500_stock: true
      };

      const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/upsert_us_stock`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Supabase error: ${response.status} - ${errorText}`);
      }

      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 處理單個批次
   */
  async processBatch(batchNumber, stocks) {
    const batch = this.getStockBatch(stocks, batchNumber);
    
    if (batch.length === 0) {
      this.log(`⚠️ 批次 ${batchNumber} 沒有股票需要處理`);
      return { success: 0, failed: 0 };
    }

    this.log(`🚀 開始處理批次 ${batchNumber} (${batch.length} 個股票)`);
    
    let successCount = 0;
    let failedCount = 0;
    
    for (let i = 0; i < batch.length; i++) {
      const stock = batch[i];
      
      try {
        this.log(`🔄 [${i + 1}/${batch.length}] 處理 ${stock.symbol} (${stock.name})`);
        
        // 獲取股票資料
        const stockData = await this.fetchStockData(stock.symbol);
        
        // 儲存到資料庫
        await this.saveToSupabase(stockData, stock.name);
        
        this.log(`✅ ${stock.symbol} 處理成功 - $${stockData.price}`);
        successCount++;
        
      } catch (error) {
        if (error.message === 'API_LIMIT_EXCEEDED') {
          this.log(`⚠️ API 限制，等待 60 秒後重試 ${stock.symbol}`);
          await this.delay(60000);
          i--; // 重試當前股票
          continue;
        }
        
        this.log(`❌ ${stock.symbol} 處理失敗: ${error.message}`);
        this.progress.failedStocks.push({
          symbol: stock.symbol,
          name: stock.name,
          error: error.message,
          timestamp: new Date().toISOString()
        });
        failedCount++;
      }
      
      // API 限制延遲
      if (i < batch.length - 1) {
        await this.delay(DELAY_BETWEEN_CALLS);
      }
    }
    
    // 更新進度
    this.progress.completedBatches.push(batchNumber);
    this.progress.lastBatchTime = new Date().toISOString();
    this.progress.totalProcessed += batch.length;
    this.progress.totalSuccess += successCount;
    this.progress.totalFailed += failedCount;
    this.saveProgress();
    
    this.log(`📊 批次 ${batchNumber} 完成 - 成功: ${successCount}, 失敗: ${failedCount}`);
    
    return { success: successCount, failed: failedCount };
  }

  /**
   * 延遲函數
   */
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 顯示進度摘要
   */
  showProgressSummary(stocks) {
    const totalBatches = this.getTotalBatches(stocks);
    const completedBatches = this.progress.completedBatches.length;
    const remainingBatches = totalBatches - completedBatches;
    
    console.log(`\n📊 進度摘要:`);
    console.log(`   總股票數: ${stocks.length}`);
    console.log(`   總批次數: ${totalBatches}`);
    console.log(`   已完成批次: ${completedBatches}`);
    console.log(`   剩餘批次: ${remainingBatches}`);
    console.log(`   已處理股票: ${this.progress.totalProcessed}`);
    console.log(`   成功: ${this.progress.totalSuccess}`);
    console.log(`   失敗: ${this.progress.totalFailed}`);
    
    if (this.progress.failedStocks.length > 0) {
      console.log(`   失敗股票: ${this.progress.failedStocks.map(s => s.symbol).join(', ')}`);
    }
    
    if (this.progress.lastBatchTime) {
      console.log(`   最後更新: ${this.progress.lastBatchTime}`);
    }
    console.log('');
  }

  /**
   * 自動處理所有批次
   */
  async autoProcessAll() {
    const stocks = this.readSP500CSV();
    if (stocks.length === 0) return;

    const totalBatches = this.getTotalBatches(stocks);
    this.log(`🤖 自動模式：將處理 ${totalBatches} 個批次`);
    
    for (let batchNumber = 1; batchNumber <= totalBatches; batchNumber++) {
      // 跳過已完成的批次
      if (this.progress.completedBatches.includes(batchNumber)) {
        this.log(`⏭️ 跳過已完成的批次 ${batchNumber}`);
        continue;
      }
      
      await this.processBatch(batchNumber, stocks);
      
      // 批次間延遲
      if (batchNumber < totalBatches) {
        this.log(`⏳ 批次間休息 ${DELAY_BETWEEN_BATCHES / 1000} 秒...`);
        await this.delay(DELAY_BETWEEN_BATCHES);
      }
    }
    
    this.log('🎉 所有批次處理完成！');
    this.showProgressSummary(stocks);
  }

  /**
   * 處理指定批次
   */
  async processSingleBatch(batchNumber) {
    const stocks = this.readSP500CSV();
    if (stocks.length === 0) return;

    const totalBatches = this.getTotalBatches(stocks);
    
    if (batchNumber < 1 || batchNumber > totalBatches) {
      this.log(`❌ 無效的批次號: ${batchNumber} (有效範圍: 1-${totalBatches})`);
      return;
    }
    
    if (this.progress.completedBatches.includes(batchNumber)) {
      this.log(`⚠️ 批次 ${batchNumber} 已經完成`);
      return;
    }
    
    await this.processBatch(batchNumber, stocks);
    this.showProgressSummary(stocks);
  }

  /**
   * 重試失敗的股票
   */
  async retryFailedStocks() {
    if (this.progress.failedStocks.length === 0) {
      this.log('✅ 沒有失敗的股票需要重試');
      return;
    }
    
    this.log(`🔄 重試 ${this.progress.failedStocks.length} 個失敗的股票`);
    
    const failedStocks = [...this.progress.failedStocks];
    this.progress.failedStocks = []; // 清空失敗列表
    
    let successCount = 0;
    
    for (const stock of failedStocks) {
      try {
        this.log(`🔄 重試 ${stock.symbol}`);
        
        const stockData = await this.fetchStockData(stock.symbol);
        await this.saveToSupabase(stockData, stock.name);
        
        this.log(`✅ ${stock.symbol} 重試成功`);
        successCount++;
        
      } catch (error) {
        this.log(`❌ ${stock.symbol} 重試失敗: ${error.message}`);
        this.progress.failedStocks.push(stock);
      }
      
      await this.delay(DELAY_BETWEEN_CALLS);
    }
    
    this.saveProgress();
    this.log(`📊 重試完成 - 成功: ${successCount}, 仍失敗: ${this.progress.failedStocks.length}`);
  }
}

// 主程序
async function main() {
  const synchronizer = new BatchSP500Synchronizer();
  const args = process.argv.slice(2);
  
  if (args.includes('--help')) {
    console.log(`
SP500 批量同步工具

使用方式:
  node scripts/batchSyncSP500.js --batch 1     # 處理第1批
  node scripts/batchSyncSP500.js --auto        # 自動處理所有批次
  node scripts/batchSyncSP500.js --retry       # 重試失敗的股票
  node scripts/batchSyncSP500.js --status      # 顯示進度狀態
  node scripts/batchSyncSP500.js --reset       # 重置進度

特點:
- 每批處理 ${BATCH_SIZE} 個股票
- 支援斷點續傳
- 自動處理 API 限制
- 詳細的進度記錄
    `);
    return;
  }
  
  if (args.includes('--status')) {
    const stocks = synchronizer.readSP500CSV();
    synchronizer.showProgressSummary(stocks);
    return;
  }
  
  if (args.includes('--reset')) {
    synchronizer.progress = {
      completedBatches: [],
      failedStocks: [],
      lastBatchTime: null,
      totalProcessed: 0,
      totalSuccess: 0,
      totalFailed: 0
    };
    synchronizer.saveProgress();
    console.log('🔄 進度已重置');
    return;
  }
  
  if (args.includes('--retry')) {
    await synchronizer.retryFailedStocks();
    return;
  }
  
  if (args.includes('--auto')) {
    await synchronizer.autoProcessAll();
    return;
  }
  
  const batchIndex = args.indexOf('--batch');
  if (batchIndex !== -1 && args[batchIndex + 1]) {
    const batchNumber = parseInt(args[batchIndex + 1]);
    if (isNaN(batchNumber)) {
      console.error('❌ 無效的批次號');
      return;
    }
    await synchronizer.processSingleBatch(batchNumber);
    return;
  }
  
  // 預設顯示狀態
  const stocks = synchronizer.readSP500CSV();
  synchronizer.showProgressSummary(stocks);
  console.log('使用 --help 查看使用說明');
}

// 錯誤處理
process.on('unhandledRejection', (reason, promise) => {
  console.error('未處理的 Promise 拒絕:', reason);
  process.exit(1);
});

process.on('SIGINT', () => {
  console.log('\n⚠️ 收到中斷信號，正在安全退出...');
  process.exit(0);
});

// 執行主程序
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 程序執行失敗:', error);
    process.exit(1);
  });
}

module.exports = { BatchSP500Synchronizer };