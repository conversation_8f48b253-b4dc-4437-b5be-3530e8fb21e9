-- =====================================================
-- 快速修正匯率為正確的 29.925 中間價
-- 即期買入：29.900，即期賣出：29.950
-- =====================================================

-- 更新 2025-06-01 的匯率資料
UPDATE exchange_rates 
SET 
    spot_buy = 29.900,
    spot_sell = 29.950,
    updated_at = NOW()
WHERE date = '2025-06-01' AND currency = 'USD';

-- 如果沒有 2025-06-01 的資料，則插入
INSERT INTO exchange_rates (date, currency, cash_buy, cash_sell, spot_buy, spot_sell) 
VALUES ('2025-06-01', 'USD', 29.800, 30.050, 29.900, 29.950)
ON CONFLICT (date, currency) DO UPDATE SET
    cash_buy = EXCLUDED.cash_buy,
    cash_sell = EXCLUDED.cash_sell,
    spot_buy = EXCLUDED.spot_buy,
    spot_sell = EXCLUDED.spot_sell,
    updated_at = NOW();

-- 驗證更新結果
SELECT 
    '✅ 匯率驗證' as check_type,
    date,
    spot_buy,
    spot_sell,
    (spot_buy + spot_sell) / 2.0 as calculated_mid_rate
FROM exchange_rates 
WHERE date = '2025-06-01' AND currency = 'USD';

-- 測試即期中間價函數
SELECT 
    '✅ 中間價測試' as test_type,
    get_spot_mid_rate('USD') as spot_mid_rate;

-- 確認結果應該是 29.925
SELECT 
    '🎯 預期結果' as info,
    '29.925' as expected_mid_rate,
    get_spot_mid_rate('USD') as actual_mid_rate,
    CASE 
        WHEN get_spot_mid_rate('USD') = 29.925 THEN '✅ 正確'
        ELSE '❌ 需要修正'
    END as status;
