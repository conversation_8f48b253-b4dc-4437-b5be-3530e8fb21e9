# 🗑️ 左滑刪除功能

## 功能概述

為資產負債表添加了左滑刪除功能，用戶可以通過左滑任何資產或負債項目來顯示刪除按鈕，點擊垃圾桶圖標即可刪除該項目。

## ✨ 主要特性

### 1. 直觀的手勢操作
- **左滑顯示** - 在任何資產或負債項目上向左滑動
- **垃圾桶圖標** - 顯示紅色背景的垃圾桶刪除按鈕
- **確認對話框** - 點擊刪除按鈕後顯示確認對話框

### 2. 安全的刪除機制
- **二次確認** - 防止誤刪，需要用戶確認
- **即時更新** - 刪除後立即更新資產負債表
- **總額重算** - 自動重新計算總資產、總負債和淨資產

### 3. 支援所有項目類型
- ✅ 現金資產
- ✅ 銀行資產
- ✅ 股票資產（台股、美股）
- ✅ 不動產
- ✅ 其他資產類型
- ✅ 所有負債類型

## 🎯 使用方法

### 刪除資產
1. 在資產負債表中找到要刪除的資產項目
2. 向左滑動該項目
3. 點擊出現的紅色垃圾桶圖標
4. 在確認對話框中點擊「刪除」

### 刪除負債
1. 在資產負債表中找到要刪除的負債項目
2. 向左滑動該項目
3. 點擊出現的紅色垃圾桶圖標
4. 在確認對話框中點擊「刪除」

## 🔧 技術實現

### 使用的技術
- **react-native-gesture-handler** - 提供滑動手勢支持
- **Swipeable 組件** - 實現左滑功能
- **Animated.View** - 提供流暢的動畫效果
- **Alert 對話框** - 實現刪除確認

### 核心組件
```typescript
// 左滑刪除按鈕渲染
const renderRightActions = (onDelete: () => void) => {
  return (
    <Animated.View style={styles.deleteAction}>
      <TouchableOpacity style={styles.deleteButton} onPress={onDelete}>
        <Ionicons name="trash" size={20} color="#fff" />
      </TouchableOpacity>
    </Animated.View>
  );
};

// 包裝在 Swipeable 組件中
<Swipeable
  renderRightActions={() => renderRightActions(() => handleDelete(item.id))}
  rightThreshold={40}
>
  {/* 項目內容 */}
</Swipeable>
```

### 刪除處理函數
```typescript
const handleDeleteAsset = (assetId: string) => {
  const asset = assets.find(a => a.id === assetId);
  Alert.alert(
    '確認刪除',
    `確定要刪除資產 "${asset.name}" 嗎？`,
    [
      { text: '取消', style: 'cancel' },
      {
        text: '刪除',
        style: 'destructive',
        onPress: () => setAssets(prev => prev.filter(a => a.id !== assetId)),
      },
    ]
  );
};
```

## 🎨 視覺設計

### 刪除按鈕樣式
- **背景顏色**: #FF3B30 (紅色)
- **圖標**: 白色垃圾桶
- **寬度**: 60px
- **圓角**: 12px
- **動畫**: 流暢的滑動動畫

### 確認對話框
- **標題**: "確認刪除"
- **內容**: 顯示具體項目名稱
- **按鈕**: "取消" 和 "刪除"
- **樣式**: "刪除" 按鈕為紅色警告樣式

## 📱 用戶體驗

### 優點
1. **直觀操作** - 符合用戶習慣的左滑刪除手勢
2. **安全機制** - 二次確認防止誤刪
3. **即時反饋** - 刪除後立即更新界面
4. **視覺清晰** - 紅色背景和垃圾桶圖標清楚表示刪除操作

### 注意事項
1. **不可恢復** - 刪除操作無法撤銷，請謹慎操作
2. **數據同步** - 如果連接數據庫，需要確保數據同步
3. **性能考慮** - 大量項目時保持流暢的滑動體驗

## 🚀 未來改進

### 可能的增強功能
1. **撤銷功能** - 添加刪除後的撤銷選項
2. **批量刪除** - 支持選擇多個項目批量刪除
3. **回收站** - 實現軟刪除和回收站功能
4. **自定義手勢** - 支持其他手勢操作（如編輯）

這個功能大大提升了用戶管理資產負債的便利性，讓刪除操作變得更加直觀和高效！
