-- =====================================================
-- 美國ETF數據庫設置 - 修復版本
-- 解決函數衝突問題
-- =====================================================

-- 0. 檢查 us_stocks 表是否存在
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'us_stocks'
    ) THEN
        RAISE EXCEPTION 'us_stocks 表不存在，請先創建基礎表結構';
    END IF;
    RAISE NOTICE '✅ us_stocks 表存在，繼續執行';
END $$;

-- 1. 為 us_stocks 表添加 ETF 標識字段（如果不存在）
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'us_stocks' AND column_name = 'is_etf'
    ) THEN
        ALTER TABLE us_stocks ADD COLUMN is_etf BOOLEAN DEFAULT false;
        RAISE NOTICE '✅ 已添加 is_etf 字段';
    ELSE
        RAISE NOTICE '⚠️  is_etf 字段已存在，跳過';
    END IF;
END $$;

-- 2. 為 us_stocks 表添加資產類型字段（如果不存在）
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'us_stocks' AND column_name = 'asset_type'
    ) THEN
        ALTER TABLE us_stocks ADD COLUMN asset_type VARCHAR(20) DEFAULT 'STOCK';
        RAISE NOTICE '✅ 已添加 asset_type 字段';
    ELSE
        RAISE NOTICE '⚠️  asset_type 字段已存在，跳過';
    END IF;
END $$;

-- 3. 創建 ETF 專用視圖
DROP VIEW IF EXISTS us_etf_view;
CREATE VIEW us_etf_view AS
SELECT 
    symbol,
    name,
    sector,
    price,
    change_amount,
    change_percent,
    volume,
    market_cap,
    price_date,
    updated_at
FROM us_stocks
WHERE is_etf = true
ORDER BY market_cap DESC NULLS LAST;

-- 4. 刪除並重新創建 ETF 搜索函數
DROP FUNCTION IF EXISTS search_us_etf(TEXT, INTEGER);
DROP FUNCTION IF EXISTS search_us_etf(TEXT);

CREATE FUNCTION search_us_etf(
    search_term TEXT,
    limit_count INTEGER DEFAULT 50
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    sector VARCHAR(100),
    price DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.sector, s.price, s.change_percent, s.market_cap
    FROM us_stocks s
    WHERE 
        s.is_etf = true
        AND (s.symbol ILIKE '%' || search_term || '%' OR s.name ILIKE '%' || search_term || '%')
    ORDER BY s.market_cap DESC NULLS LAST
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 5. 刪除並重新創建統計函數
DROP FUNCTION IF EXISTS get_us_stock_stats();

CREATE FUNCTION get_us_stock_stats()
RETURNS TABLE (
    total_stocks BIGINT,
    stock_count BIGINT,
    etf_count BIGINT,
    sp500_count BIGINT,
    last_updated TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_stocks,
        COUNT(*) FILTER (WHERE is_etf = false OR is_etf IS NULL) as stock_count,
        COUNT(*) FILTER (WHERE is_etf = true) as etf_count,
        COUNT(*) FILTER (WHERE is_sp500 = true) as sp500_count,
        MAX(updated_at) as last_updated
    FROM us_stocks;
END;
$$ LANGUAGE plpgsql;

-- 6. 創建索引以提高查詢性能
CREATE INDEX IF NOT EXISTS idx_us_stocks_is_etf ON us_stocks(is_etf);
CREATE INDEX IF NOT EXISTS idx_us_stocks_asset_type ON us_stocks(asset_type);
CREATE INDEX IF NOT EXISTS idx_us_stocks_symbol_etf ON us_stocks(symbol) WHERE is_etf = true;

-- 7. 創建 ETF 分類視圖
DROP VIEW IF EXISTS us_etf_by_sector;
CREATE VIEW us_etf_by_sector AS
SELECT 
    sector,
    COUNT(*) as etf_count,
    AVG(price) as avg_price,
    SUM(market_cap) as total_market_cap
FROM us_stocks
WHERE is_etf = true AND price IS NOT NULL
GROUP BY sector
ORDER BY etf_count DESC;

-- 8. 權限設置
GRANT SELECT ON us_etf_view TO anon, authenticated;
GRANT SELECT ON us_etf_by_sector TO anon, authenticated;
GRANT EXECUTE ON FUNCTION search_us_etf(TEXT, INTEGER) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_us_stock_stats() TO anon, authenticated;

-- 9. 驗證設置
DO $$
DECLARE
    etf_count INTEGER;
    total_count INTEGER;
BEGIN
    -- 檢查現有ETF數量
    SELECT COUNT(*) INTO etf_count FROM us_stocks WHERE is_etf = true;
    SELECT COUNT(*) INTO total_count FROM us_stocks;
    
    RAISE NOTICE '📊 數據庫統計:';
    RAISE NOTICE '   總股票數: %', total_count;
    RAISE NOTICE '   ETF數量: %', etf_count;
    RAISE NOTICE '✅ ETF數據庫結構設置完成！';
END $$;
