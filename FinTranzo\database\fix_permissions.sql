-- =====================================================
-- 修正 Supabase 權限問題
-- 允許匿名用戶寫入美股資料
-- =====================================================

-- =====================================================
-- 1. 修正美股表 RLS 政策
-- =====================================================

-- 刪除現有的限制性政策
DROP POLICY IF EXISTS "Allow service role write access" ON us_stocks;
DROP POLICY IF EXISTS "Allow public read access" ON us_stocks;

-- 創建更寬鬆的政策，允許所有操作
CREATE POLICY "Allow all operations on us_stocks" ON us_stocks
    FOR ALL USING (true)
    WITH CHECK (true);

-- =====================================================
-- 2. 修正同步狀態表 RLS 政策
-- =====================================================

-- 刪除現有的限制性政策
DROP POLICY IF EXISTS "Allow service role write access" ON sync_status;
DROP POLICY IF EXISTS "Allow public read access" ON sync_status;

-- 創建更寬鬆的政策，允許所有操作
CREATE POLICY "Allow all operations on sync_status" ON sync_status
    FOR ALL USING (true)
    WITH CHECK (true);

-- =====================================================
-- 3. 確保表格權限正確
-- =====================================================

-- 授予 anon 角色完整權限
GRANT ALL ON us_stocks TO anon;
GRANT ALL ON sync_status TO anon;

-- 授予 authenticated 角色完整權限
GRANT ALL ON us_stocks TO authenticated;
GRANT ALL ON sync_status TO authenticated;

-- 授予 service_role 角色完整權限
GRANT ALL ON us_stocks TO service_role;
GRANT ALL ON sync_status TO service_role;

-- =====================================================
-- 4. 測試插入資料
-- =====================================================

-- 插入測試資料以驗證權限
INSERT INTO us_stocks (
    symbol, name, sector, price, is_sp500, created_at, updated_at
) VALUES (
    'TEST', 'Test Company', 'Technology', 100.00, true, NOW(), NOW()
) ON CONFLICT (symbol) DO UPDATE SET
    price = EXCLUDED.price,
    updated_at = NOW();

-- 檢查插入是否成功
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM us_stocks WHERE symbol = 'TEST') 
        THEN '✅ 權限修正成功！可以寫入資料'
        ELSE '❌ 權限修正失敗！仍無法寫入'
    END as permission_status;

-- =====================================================
-- 5. 顯示當前權限狀態
-- =====================================================

-- 檢查 RLS 狀態
SELECT
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables
WHERE tablename IN ('us_stocks', 'sync_status');

-- 檢查政策
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename IN ('us_stocks', 'sync_status');

-- =====================================================
-- 修正完成
-- =====================================================

SELECT 
    '🎉 權限修正完成！' as status,
    '已允許所有角色讀寫美股資料' as fix_applied,
    '現在可以正常存儲股價資料' as result,
    NOW() as fix_time;
