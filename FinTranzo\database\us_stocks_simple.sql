-- =====================================================
-- 美股資料庫設定 (簡化修正版)
-- 解決函數衝突問題
-- 執行日期: 2025-06-01
-- =====================================================

-- =====================================================
-- 1. 清理現有函數 (解決衝突)
-- =====================================================

-- 刪除可能存在的舊函數
DROP FUNCTION IF EXISTS get_us_stock_stats();
DROP FUNCTION IF EXISTS search_us_stocks(TEXT, BOOLEAN, INTEGER);
DROP FUNCTION IF EXISTS get_us_stock_by_symbol(VARCHAR);
DROP FUNCTION IF EXISTS get_popular_us_stocks(INTEGER);
DROP FUNCTION IF EXISTS get_us_stocks_by_sector(VARCHAR, INTEGER);
DROP FUNCTION IF EXISTS update_sync_status(VARCHAR, VARCHAR, INTEGER, INTEGER, INTEGER, INTEGER, TEXT);
DROP FUNCTION IF EXISTS get_sync_status(VARCHAR);
DROP FUNCTION IF EXISTS upsert_us_stock(VARCHAR, VARCHAR, VARCHAR, VARCHAR, DECIMAL, DECIMAL, DECIMAL, DECIMAL, BIGINT, DECIMAL, DECIMAL, DECIMAL, BIGINT, BOOLEAN);
DROP FUNCTION IF EXISTS check_stock_data_quality();

-- =====================================================
-- 2. 美股資料表
-- =====================================================

-- 建立美股資料表
CREATE TABLE IF NOT EXISTS us_stocks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL UNIQUE,
    name VARCHAR(200) NOT NULL,
    sector VARCHAR(100),
    industry VARCHAR(100),
    market_cap BIGINT,
    price DECIMAL(10,2),
    open_price DECIMAL(10,2),
    high_price DECIMAL(10,2),
    low_price DECIMAL(10,2),
    volume BIGINT,
    change_amount DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    previous_close DECIMAL(10,2),
    price_date DATE DEFAULT CURRENT_DATE,
    is_sp500 BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引
CREATE INDEX IF NOT EXISTS idx_us_stocks_symbol ON us_stocks(symbol);
CREATE INDEX IF NOT EXISTS idx_us_stocks_name ON us_stocks(name);
CREATE INDEX IF NOT EXISTS idx_us_stocks_price_not_null ON us_stocks(price) WHERE price IS NOT NULL;

-- =====================================================
-- 3. 同步狀態表
-- =====================================================

CREATE TABLE IF NOT EXISTS sync_status (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    sync_type VARCHAR(50) NOT NULL UNIQUE,
    total_items INTEGER DEFAULT 0,
    completed_items INTEGER DEFAULT 0,
    failed_items INTEGER DEFAULT 0,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'pending',
    api_requests_used INTEGER DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 插入同步狀態記錄
INSERT INTO sync_status (sync_type, status) 
VALUES ('us_stocks', 'pending')
ON CONFLICT (sync_type) DO NOTHING;

-- =====================================================
-- 4. 核心函數
-- =====================================================

-- 搜尋美股函數
CREATE FUNCTION search_us_stocks(
    search_term TEXT,
    sp500_only BOOLEAN DEFAULT true,
    limit_count INTEGER DEFAULT 50
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    sector VARCHAR(100),
    price DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.sector, s.price, s.change_percent, s.market_cap
    FROM us_stocks s
    WHERE 
        (sp500_only = false OR s.is_sp500 = true)
        AND (s.symbol ILIKE '%' || search_term || '%' OR s.name ILIKE '%' || search_term || '%')
        AND s.price IS NOT NULL
    ORDER BY 
        CASE 
            WHEN s.symbol = UPPER(search_term) THEN 1
            WHEN s.symbol ILIKE search_term || '%' THEN 2
            ELSE 3
        END,
        s.market_cap DESC NULLS LAST
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 獲取美股統計函數
CREATE FUNCTION get_us_stock_stats()
RETURNS TABLE (
    total_stocks BIGINT,
    sp500_count BIGINT,
    stocks_with_prices BIGINT,
    sectors_count BIGINT,
    last_updated DATE,
    avg_price DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_stocks,
        COUNT(*) FILTER (WHERE is_sp500 = true) as sp500_count,
        COUNT(*) FILTER (WHERE price IS NOT NULL) as stocks_with_prices,
        COUNT(DISTINCT sector) as sectors_count,
        MAX(price_date) as last_updated,
        AVG(price) as avg_price
    FROM us_stocks;
END;
$$ LANGUAGE plpgsql;

-- 獲取指定股票資訊
CREATE FUNCTION get_us_stock_by_symbol(
    stock_symbol VARCHAR(10)
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    sector VARCHAR(100),
    industry VARCHAR(100),
    price DECIMAL(10,2),
    open_price DECIMAL(10,2),
    high_price DECIMAL(10,2),
    low_price DECIMAL(10,2),
    volume BIGINT,
    change_amount DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT,
    price_date DATE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.sector, s.industry, s.price, s.open_price,
        s.high_price, s.low_price, s.volume, s.change_amount, 
        s.change_percent, s.market_cap, s.price_date, s.updated_at
    FROM us_stocks s
    WHERE s.symbol = stock_symbol;
END;
$$ LANGUAGE plpgsql;

-- 更新同步狀態函數
CREATE FUNCTION update_sync_status(
    p_sync_type VARCHAR(50),
    p_status VARCHAR(20),
    p_total_items INTEGER DEFAULT NULL,
    p_completed_items INTEGER DEFAULT NULL,
    p_failed_items INTEGER DEFAULT NULL,
    p_api_requests_used INTEGER DEFAULT NULL,
    p_error_message TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    UPDATE sync_status 
    SET 
        status = p_status,
        total_items = COALESCE(p_total_items, total_items),
        completed_items = COALESCE(p_completed_items, completed_items),
        failed_items = COALESCE(p_failed_items, failed_items),
        api_requests_used = COALESCE(p_api_requests_used, api_requests_used),
        error_message = p_error_message,
        last_sync_at = CASE WHEN p_status = 'completed' THEN NOW() ELSE last_sync_at END,
        updated_at = NOW()
    WHERE sync_type = p_sync_type;
    
    IF NOT FOUND THEN
        INSERT INTO sync_status (
            sync_type, status, total_items, completed_items, failed_items, 
            api_requests_used, error_message, last_sync_at
        ) VALUES (
            p_sync_type, p_status, p_total_items, p_completed_items, p_failed_items,
            p_api_requests_used, p_error_message, 
            CASE WHEN p_status = 'completed' THEN NOW() ELSE NULL END
        );
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 獲取同步狀態函數
CREATE FUNCTION get_sync_status(p_sync_type VARCHAR(50) DEFAULT 'us_stocks')
RETURNS TABLE (
    sync_type VARCHAR(50),
    status VARCHAR(20),
    total_items INTEGER,
    completed_items INTEGER,
    failed_items INTEGER,
    completion_rate DECIMAL(5,2),
    api_requests_used INTEGER,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.sync_type,
        s.status,
        s.total_items,
        s.completed_items,
        s.failed_items,
        CASE 
            WHEN s.total_items > 0 THEN ROUND((s.completed_items::DECIMAL / s.total_items) * 100, 2)
            ELSE 0
        END as completion_rate,
        s.api_requests_used,
        s.last_sync_at,
        s.error_message
    FROM sync_status s
    WHERE s.sync_type = p_sync_type;
END;
$$ LANGUAGE plpgsql;

-- 插入或更新美股資料函數
CREATE FUNCTION upsert_us_stock(
    stock_symbol VARCHAR(10),
    stock_name VARCHAR(200),
    stock_sector VARCHAR(100) DEFAULT NULL,
    stock_industry VARCHAR(100) DEFAULT NULL,
    stock_price DECIMAL(10,2) DEFAULT NULL,
    stock_open DECIMAL(10,2) DEFAULT NULL,
    stock_high DECIMAL(10,2) DEFAULT NULL,
    stock_low DECIMAL(10,2) DEFAULT NULL,
    stock_volume BIGINT DEFAULT NULL,
    stock_change DECIMAL(10,2) DEFAULT NULL,
    stock_change_percent DECIMAL(5,2) DEFAULT NULL,
    stock_previous_close DECIMAL(10,2) DEFAULT NULL,
    stock_market_cap BIGINT DEFAULT NULL,
    is_sp500_stock BOOLEAN DEFAULT true
)
RETURNS UUID AS $$
DECLARE
    stock_id UUID;
BEGIN
    INSERT INTO us_stocks (
        symbol, name, sector, industry, price, open_price, high_price, low_price,
        volume, change_amount, change_percent, previous_close, market_cap, is_sp500
    ) VALUES (
        stock_symbol, stock_name, stock_sector, stock_industry, stock_price, 
        stock_open, stock_high, stock_low, stock_volume, stock_change, 
        stock_change_percent, stock_previous_close, stock_market_cap, is_sp500_stock
    )
    ON CONFLICT (symbol) 
    DO UPDATE SET
        name = EXCLUDED.name,
        sector = EXCLUDED.sector,
        industry = EXCLUDED.industry,
        price = EXCLUDED.price,
        open_price = EXCLUDED.open_price,
        high_price = EXCLUDED.high_price,
        low_price = EXCLUDED.low_price,
        volume = EXCLUDED.volume,
        change_amount = EXCLUDED.change_amount,
        change_percent = EXCLUDED.change_percent,
        previous_close = EXCLUDED.previous_close,
        market_cap = EXCLUDED.market_cap,
        price_date = CURRENT_DATE,
        updated_at = NOW()
    RETURNING id INTO stock_id;
    
    RETURN stock_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. RLS 政策
-- =====================================================

-- 美股表 RLS
ALTER TABLE us_stocks ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Allow public read access" ON us_stocks;
CREATE POLICY "Allow public read access" ON us_stocks
    FOR SELECT USING (true);

DROP POLICY IF EXISTS "Allow service role write access" ON us_stocks;
CREATE POLICY "Allow service role write access" ON us_stocks
    FOR ALL USING (auth.role() = 'service_role');

-- 同步狀態表 RLS
ALTER TABLE sync_status ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Allow public read access" ON sync_status;
CREATE POLICY "Allow public read access" ON sync_status
    FOR SELECT USING (true);

DROP POLICY IF EXISTS "Allow service role write access" ON sync_status;
CREATE POLICY "Allow service role write access" ON sync_status
    FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- 設定完成
-- =====================================================

SELECT 
    '🎉 美股資料庫簡化版設定完成！' as status,
    '已解決函數衝突問題' as fix_applied,
    '準備執行一次性股價同步' as next_step,
    NOW() as setup_time;
