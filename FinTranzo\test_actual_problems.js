/**
 * 測試實際問題的腳本
 */

console.log('🧪 開始測試實際問題...');

// 測試問題1：31號日期邏輯
function testDateLogic() {
  console.log('\n📅 測試問題1：31號日期邏輯');
  console.log('='.repeat(50));
  
  const currentYear = 2025;
  const currentMonth = 4; // 5月（從0開始）
  const paymentDay = 31;
  
  console.log('測試場景：2025年5月，設定還款日31號');
  
  // 測試原始邏輯（可能有問題的）
  console.log('\n❌ 原始邏輯測試：');
  const originalDate = new Date(currentYear, currentMonth, paymentDay);
  console.log(`new Date(${currentYear}, ${currentMonth}, ${paymentDay})`);
  console.log('結果:', originalDate.toLocaleDateString('zh-TW'));
  console.log('實際日期:', originalDate.getDate());
  console.log('實際月份:', originalDate.getMonth() + 1);
  
  // 測試修復後的邏輯
  console.log('\n✅ 修復後邏輯測試：');
  const lastDayOfCurrentMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
  console.log('5月最後一天:', lastDayOfCurrentMonth);
  
  let actualPaymentDay;
  if (paymentDay > lastDayOfCurrentMonth) {
    actualPaymentDay = lastDayOfCurrentMonth;
    console.log(`月末調整: 原定${paymentDay}號 -> ${actualPaymentDay}號`);
  } else {
    actualPaymentDay = paymentDay;
    console.log(`無需調整: ${paymentDay}號`);
  }
  
  const fixedDate = new Date(currentYear, currentMonth, actualPaymentDay);
  console.log(`new Date(${currentYear}, ${currentMonth}, ${actualPaymentDay})`);
  console.log('結果:', fixedDate.toLocaleDateString('zh-TW'));
  console.log('實際日期:', fixedDate.getDate());
  console.log('實際月份:', fixedDate.getMonth() + 1);
  
  // 檢查是否有問題
  const hasDateProblem = originalDate.getDate() !== paymentDay;
  console.log('\n🔍 問題檢查:');
  console.log('原始邏輯是否有問題:', hasDateProblem);
  console.log('修復後邏輯是否正確:', fixedDate.getDate() === actualPaymentDay);
  
  return hasDateProblem;
}

// 測試問題2：交易記錄顯示
function testTransactionDisplay() {
  console.log('\n💰 測試問題2：交易記錄顯示');
  console.log('='.repeat(50));
  
  // 模擬初始交易數據
  const mockTransactions = [
    {
      id: '1',
      amount: 500,
      type: 'expense',
      description: '午餐',
      category: '餐飲',
      account: '現金',
      date: new Date().toISOString()
    },
    {
      id: '2',
      amount: 80000,
      type: 'income',
      description: '薪水',
      category: '薪水',
      account: '銀行',
      date: new Date().toISOString()
    }
  ];
  
  console.log('初始交易記錄:');
  mockTransactions.forEach((t, i) => {
    console.log(`  ${i + 1}. ${t.description}: ${t.type === 'income' ? '+' : '-'}${t.amount} (${t.category})`);
  });
  
  // 模擬添加負債還款交易
  const newDebtPayment = {
    id: '3',
    amount: 10000,
    type: 'expense',
    description: '房貸',
    category: '還款',
    account: '銀行',
    date: new Date().toISOString(),
    is_recurring: true,
    recurring_frequency: 'monthly'
  };
  
  const allTransactions = [...mockTransactions, newDebtPayment];
  
  console.log('\n添加負債還款後的交易記錄:');
  allTransactions.forEach((t, i) => {
    console.log(`  ${i + 1}. ${t.description}: ${t.type === 'income' ? '+' : '-'}${t.amount} (${t.category})`);
  });
  
  // 測試收支分析篩選
  console.log('\n🔍 收支分析篩選測試:');
  
  // 全部交易
  const allFiltered = allTransactions;
  console.log('全部交易:', allFiltered.length, '筆');
  allFiltered.forEach(t => {
    console.log(`  - ${t.description}: ${t.type === 'income' ? '+' : '-'}${t.amount}`);
  });
  
  // 支出交易
  const expenseFiltered = allTransactions.filter(t => t.type === 'expense');
  console.log('\n支出交易:', expenseFiltered.length, '筆');
  expenseFiltered.forEach(t => {
    console.log(`  - ${t.description}: -${t.amount}`);
  });
  
  // 還款交易
  const debtPayments = allTransactions.filter(t => t.category === '還款');
  console.log('\n還款交易:', debtPayments.length, '筆');
  debtPayments.forEach(t => {
    console.log(`  - ${t.description}: -${t.amount} (${t.category})`);
  });
  
  // 檢查是否有還款交易
  const hasDebtPayment = debtPayments.length > 0;
  console.log('\n🔍 問題檢查:');
  console.log('是否有還款交易:', hasDebtPayment);
  console.log('還款交易是否正確顯示:', hasDebtPayment && debtPayments[0].category === '還款');
  
  return hasDebtPayment;
}

// 測試JavaScript Date的行為
function testJavaScriptDateBehavior() {
  console.log('\n🔬 測試JavaScript Date的行為');
  console.log('='.repeat(50));
  
  // 測試各種月份的31號
  const testCases = [
    { year: 2025, month: 0, day: 31, name: '1月31號' },  // 1月有31天
    { year: 2025, month: 1, day: 31, name: '2月31號' },  // 2月只有28天
    { year: 2025, month: 3, day: 31, name: '4月31號' },  // 4月只有30天
    { year: 2025, month: 4, day: 31, name: '5月31號' },  // 5月有31天
  ];
  
  testCases.forEach(testCase => {
    const date = new Date(testCase.year, testCase.month, testCase.day);
    console.log(`${testCase.name}:`);
    console.log(`  輸入: new Date(${testCase.year}, ${testCase.month}, ${testCase.day})`);
    console.log(`  結果: ${date.toLocaleDateString('zh-TW')} (${date.getDate()}號)`);
    console.log(`  是否正確: ${date.getDate() === testCase.day}`);
    console.log('');
  });
}

// 執行所有測試
function runAllTests() {
  console.log('🧪 執行所有測試');
  console.log('='.repeat(70));
  
  const dateHasProblem = testDateLogic();
  const transactionWorking = testTransactionDisplay();
  
  testJavaScriptDateBehavior();
  
  console.log('\n📊 測試總結');
  console.log('='.repeat(50));
  console.log('問題1 - 日期邏輯有問題:', dateHasProblem);
  console.log('問題2 - 交易記錄正常工作:', transactionWorking);
  
  if (dateHasProblem) {
    console.log('\n❌ 發現問題1：JavaScript Date會自動調整無效日期');
    console.log('   解決方案：在創建Date之前先檢查月份的最大天數');
  }
  
  if (transactionWorking) {
    console.log('\n✅ 問題2：交易記錄邏輯本身是正確的');
    console.log('   可能的問題：數據同步或事件觸發問題');
  }
}

// 執行測試
runAllTests();

console.log('\n✅ 測試完成');
