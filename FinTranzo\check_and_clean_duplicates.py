#!/usr/bin/env python3
"""
檢查並清理Supabase中的重複美股數據
"""

import os
import sys
import requests
from datetime import datetime

# Supabase配置
SUPABASE_URL = "https://yrryyapzkgrsahranzvo.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlycnl5YXB6a2dyc2FocmFuenZvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODE3MzYzNSwiZXhwIjoyMDYzNzQ5NjM1fQ.xjiXVagzKgBBYXqtRjJiXwCgbRdEW0EpFzCIlJ26SSQ"

def make_supabase_request(endpoint, method='GET', data=None):
    """發送Supabase請求"""
    url = f"{SUPABASE_URL}/rest/v1/{endpoint}"
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'application/json',
        'Prefer': 'return=representation'
    }
    
    try:
        if method == 'GET':
            response = requests.get(url, headers=headers)
        elif method == 'POST':
            response = requests.post(url, headers=headers, json=data)
        elif method == 'DELETE':
            response = requests.delete(url, headers=headers)
        elif method == 'PATCH':
            response = requests.patch(url, headers=headers, json=data)
        
        response.raise_for_status()
        return response.json() if response.content else []
    except Exception as e:
        print(f"❌ 請求失敗: {str(e)}")
        return None

def check_duplicate_symbols():
    """檢查重複的股票代號"""
    print("🔍 檢查重複的股票代號...")
    
    # 獲取所有股票數據
    all_stocks = make_supabase_request('us_stocks?select=id,symbol,name,is_etf,asset_type,created_at&order=symbol')
    
    if not all_stocks:
        print("❌ 無法獲取股票數據")
        return {}
    
    print(f"📊 總共有 {len(all_stocks)} 條記錄")
    
    # 按symbol分組
    symbol_groups = {}
    for stock in all_stocks:
        symbol = stock['symbol']
        if symbol not in symbol_groups:
            symbol_groups[symbol] = []
        symbol_groups[symbol].append(stock)
    
    # 找出重複的symbol
    duplicates = {}
    for symbol, stocks in symbol_groups.items():
        if len(stocks) > 1:
            duplicates[symbol] = stocks
    
    print(f"🔍 發現 {len(duplicates)} 個重複的股票代號")
    
    return duplicates

def analyze_duplicates(duplicates):
    """分析重複數據的詳情"""
    print("\n📋 重複數據分析:")
    print("=" * 60)
    
    for symbol, stocks in duplicates.items():
        print(f"\n📈 {symbol} ({len(stocks)} 條記錄):")
        
        for i, stock in enumerate(stocks, 1):
            etf_status = "✅ ETF" if stock.get('is_etf') else "❌ 非ETF"
            asset_type = stock.get('asset_type', 'N/A')
            created_at = stock.get('created_at', 'N/A')[:19] if stock.get('created_at') else 'N/A'
            
            print(f"   {i}. ID: {stock['id']}")
            print(f"      名稱: {stock['name']}")
            print(f"      ETF狀態: {etf_status}")
            print(f"      資產類型: {asset_type}")
            print(f"      創建時間: {created_at}")

def clean_duplicates(duplicates, dry_run=True):
    """清理重複數據"""
    print(f"\n🧹 {'模擬' if dry_run else '執行'}清理重複數據...")
    print("=" * 60)
    
    deleted_count = 0
    kept_count = 0
    
    for symbol, stocks in duplicates.items():
        print(f"\n處理 {symbol}:")
        
        # 排序規則：ETF優先，然後按創建時間
        stocks_sorted = sorted(stocks, key=lambda x: (
            not x.get('is_etf', False),  # ETF優先 (False排在前面)
            x.get('created_at', '')      # 創建時間早的優先
        ))
        
        # 保留第一個，刪除其他
        keep_stock = stocks_sorted[0]
        delete_stocks = stocks_sorted[1:]
        
        print(f"   ✅ 保留: ID {keep_stock['id']} ({'ETF' if keep_stock.get('is_etf') else '股票'})")
        kept_count += 1
        
        for stock in delete_stocks:
            print(f"   ❌ 刪除: ID {stock['id']} ({'ETF' if stock.get('is_etf') else '股票'})")
            
            if not dry_run:
                # 實際刪除
                result = make_supabase_request(f'us_stocks?id=eq.{stock["id"]}', method='DELETE')
                if result is not None:
                    print(f"      ✅ 已刪除 ID {stock['id']}")
                    deleted_count += 1
                else:
                    print(f"      ❌ 刪除失敗 ID {stock['id']}")
            else:
                deleted_count += 1
    
    print(f"\n📊 清理結果:")
    print(f"   保留記錄: {kept_count}")
    print(f"   {'將刪除' if dry_run else '已刪除'}記錄: {deleted_count}")
    
    return deleted_count

def verify_cleanup():
    """驗證清理結果"""
    print("\n🔍 驗證清理結果...")
    
    duplicates = check_duplicate_symbols()
    
    if not duplicates:
        print("✅ 沒有發現重複數據，清理成功！")
        return True
    else:
        print(f"⚠️ 仍有 {len(duplicates)} 個重複的股票代號")
        return False

def check_specific_symbols():
    """檢查特定的問題股票代號"""
    problem_symbols = ['QQQ', 'QQQM', 'SPY', 'VOO', 'VTI']
    
    print("\n🎯 檢查問題股票代號:")
    print("=" * 40)
    
    for symbol in problem_symbols:
        stocks = make_supabase_request(f'us_stocks?symbol=eq.{symbol}&select=id,symbol,name,is_etf,asset_type')
        
        if stocks:
            print(f"\n📈 {symbol} ({len(stocks)} 條記錄):")
            for stock in stocks:
                etf_status = "✅ ETF" if stock.get('is_etf') else "❌ 非ETF"
                print(f"   ID: {stock['id']} - {stock['name']} - {etf_status}")
        else:
            print(f"\n📈 {symbol}: 沒有找到記錄")

def main():
    """主函數"""
    print("🔧 美股重複數據檢查和清理工具")
    print("=" * 60)
    
    # 1. 檢查特定問題股票
    check_specific_symbols()
    
    # 2. 檢查所有重複數據
    duplicates = check_duplicate_symbols()
    
    if not duplicates:
        print("\n✅ 沒有發現重複數據！")
        return
    
    # 3. 分析重複數據
    analyze_duplicates(duplicates)
    
    # 4. 詢問是否清理
    print(f"\n發現 {len(duplicates)} 個重複的股票代號")
    print("清理策略: 保留ETF版本，刪除非ETF版本；如果都是同類型，保留較早創建的")
    
    choice = input("\n請選擇操作:\n1. 模擬清理 (查看將要刪除的記錄)\n2. 實際清理\n3. 退出\n請輸入選擇 (1-3): ").strip()
    
    if choice == '1':
        # 模擬清理
        clean_duplicates(duplicates, dry_run=True)
    elif choice == '2':
        # 實際清理
        confirm = input("⚠️ 確定要執行實際清理嗎？這將永久刪除重複記錄！(y/N): ").strip().lower()
        if confirm == 'y':
            deleted_count = clean_duplicates(duplicates, dry_run=False)
            if deleted_count > 0:
                print(f"\n✅ 成功刪除 {deleted_count} 條重複記錄")
                verify_cleanup()
            else:
                print("\n❌ 沒有刪除任何記錄")
        else:
            print("❌ 取消清理操作")
    else:
        print("👋 退出程序")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 用戶中斷程序")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序執行失敗: {str(e)}")
        sys.exit(1)
