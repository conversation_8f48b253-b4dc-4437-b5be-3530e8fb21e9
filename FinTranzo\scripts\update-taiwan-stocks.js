/**
 * GitHub Actions - 台股每日更新腳本
 * 基於現有的 fetch-taiwan-stocks.ts 改寫為 JavaScript
 */

const { createClient } = require('@supabase/supabase-js');

// Supabase 配置
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ 缺少 Supabase 環境變數');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * 使用 FinMind API 獲取台股報價
 */
async function fetchTaiwanStockPrice(stockCode) {
  try {
    const today = new Date().toISOString().split('T')[0];
    const finmindToken = process.env.FINMIND_TOKEN;
    
    let url = `https://api.finmindtrade.com/api/v4/data?dataset=TaiwanStockPrice&data_id=${stockCode}&start_date=${today}`;
    if (finmindToken) {
      url += `&token=${finmindToken}`;
    }
    
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (data.data && data.data.length > 0) {
      const latestData = data.data[data.data.length - 1];
      return {
        code: stockCode,
        price: parseFloat(latestData.close),
        change_percent: parseFloat(latestData.change_percent || 0),
        volume: parseInt(latestData.Trading_Volume || 0),
        updated_at: new Date().toISOString()
      };
    }
    
    return null;
  } catch (error) {
    console.error(`❌ 獲取 ${stockCode} 失敗:`, error.message);
    return null;
  }
}

/**
 * 批量更新台股價格
 */
async function updateTaiwanStocks() {
  try {
    console.log('🚀 開始更新台股價格...');
    
    // 獲取需要更新的股票列表（GitHub Actions 可以處理更多）
    const { data: stocks, error: fetchError } = await supabase
      .from('taiwan_stocks')
      .select('code')
      .limit(100); // GitHub Actions 有更多時間，可以處理更多股票
    
    if (fetchError) {
      throw fetchError;
    }
    
    console.log(`📊 找到 ${stocks.length} 支股票需要更新`);
    
    let successCount = 0;
    let failedCount = 0;
    
    // 批量處理，每次處理10支股票
    for (let i = 0; i < stocks.length; i += 10) {
      const batch = stocks.slice(i, i + 10);
      console.log(`📈 處理第 ${Math.floor(i/10) + 1} 批，股票 ${i + 1}-${Math.min(i + 10, stocks.length)}`);
      
      const promises = batch.map(stock => fetchTaiwanStockPrice(stock.code));
      const results = await Promise.all(promises);
      
      // 更新成功獲取的股票價格
      for (const result of results) {
        if (result) {
          const { error: updateError } = await supabase
            .from('taiwan_stocks')
            .update({
              closing_price: result.price,
              change_percent: result.change_percent,
              volume: result.volume,
              updated_at: result.updated_at
            })
            .eq('code', result.code);
          
          if (updateError) {
            console.error(`❌ 更新 ${result.code} 失敗:`, updateError);
            failedCount++;
          } else {
            console.log(`✅ 更新 ${result.code}: $${result.price}`);
            successCount++;
          }
        } else {
          failedCount++;
        }
      }
      
      // 避免 API 限制，每批次間隔1秒
      if (i + 10 < stocks.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    console.log(`✅ 台股更新完成: 成功 ${successCount}, 失敗 ${failedCount}`);
    
    // 記錄更新日誌
    const { error: logError } = await supabase
      .from('update_logs')
      .insert({
        type: 'taiwan_stocks',
        success_count: successCount,
        failed_count: failedCount,
        total_count: stocks.length,
        updated_at: new Date().toISOString()
      });
    
    if (logError) {
      console.warn('⚠️ 記錄日誌失敗:', logError);
    }
    
    return {
      success: true,
      updated: successCount,
      failed: failedCount,
      total: stocks.length
    };
    
  } catch (error) {
    console.error('❌ 台股更新失敗:', error);
    throw error;
  }
}

/**
 * 主函數
 */
async function main() {
  try {
    console.log('🇹🇼 GitHub Actions - 台股更新開始');
    console.log('⏰ 執行時間:', new Date().toLocaleString('zh-TW'));
    
    const result = await updateTaiwanStocks();
    
    console.log('📊 更新結果:', result);
    console.log('🎉 台股更新完成！');
    
    process.exit(0);
    
  } catch (error) {
    console.error('💥 執行失敗:', error);
    process.exit(1);
  }
}

// 執行主函數
if (require.main === module) {
  main();
}

module.exports = { updateTaiwanStocks };
