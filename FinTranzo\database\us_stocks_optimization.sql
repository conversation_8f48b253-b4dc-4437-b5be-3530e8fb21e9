-- =====================================================
-- 美股資料庫優化補充
-- 針對新的 API → Supabase → 用戶 架構進行優化
-- 執行日期: 2025-06-01
-- =====================================================

-- =====================================================
-- 1. 添加中文名稱支援
-- =====================================================

-- 添加中文名稱欄位
ALTER TABLE us_stocks 
ADD COLUMN IF NOT EXISTS chinese_name VARCHAR(100);

-- 為中文名稱添加索引
CREATE INDEX IF NOT EXISTS idx_us_stocks_chinese_name ON us_stocks(chinese_name);
CREATE INDEX IF NOT EXISTS idx_us_stocks_chinese_search ON us_stocks(chinese_name varchar_pattern_ops);

-- 更新搜尋函數以支援中文搜尋
CREATE OR REPLACE FUNCTION search_us_stocks(
    search_term TEXT,
    sp500_only BOOLEAN DEFAULT true,
    limit_count INTEGER DEFAULT 50
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    sector VARCHAR(100),
    price DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.sector, s.price, s.change_percent, s.market_cap
    FROM us_stocks s
    WHERE 
        (sp500_only = false OR s.is_sp500 = true)
        AND (
            s.symbol ILIKE '%' || search_term || '%' 
            OR s.name ILIKE '%' || search_term || '%'
            OR s.chinese_name ILIKE '%' || search_term || '%'
        )
        AND s.price IS NOT NULL  -- 只返回有價格的股票
    ORDER BY 
        CASE 
            WHEN s.symbol = UPPER(search_term) THEN 1  -- 完全匹配代號優先
            WHEN s.symbol ILIKE search_term || '%' THEN 2  -- 代號開頭匹配次優先
            ELSE 3
        END,
        s.market_cap DESC NULLS LAST
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 2. 同步狀態追蹤
-- =====================================================

-- 創建同步狀態表
CREATE TABLE IF NOT EXISTS sync_status (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    sync_type VARCHAR(50) NOT NULL,
    total_items INTEGER DEFAULT 0,
    completed_items INTEGER DEFAULT 0,
    failed_items INTEGER DEFAULT 0,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    next_sync_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'pending', -- pending, running, completed, failed
    error_message TEXT,
    api_requests_used INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 同步狀態索引
CREATE INDEX IF NOT EXISTS idx_sync_status_type ON sync_status(sync_type);
CREATE INDEX IF NOT EXISTS idx_sync_status_status ON sync_status(status);
CREATE INDEX IF NOT EXISTS idx_sync_status_last_sync ON sync_status(last_sync_at DESC);

-- 插入美股同步狀態記錄
INSERT INTO sync_status (sync_type, status) 
VALUES ('us_stocks', 'pending')
ON CONFLICT DO NOTHING;

-- =====================================================
-- 3. 同步狀態管理函數
-- =====================================================

-- 更新同步狀態函數
CREATE OR REPLACE FUNCTION update_sync_status(
    p_sync_type VARCHAR(50),
    p_status VARCHAR(20),
    p_total_items INTEGER DEFAULT NULL,
    p_completed_items INTEGER DEFAULT NULL,
    p_failed_items INTEGER DEFAULT NULL,
    p_api_requests_used INTEGER DEFAULT NULL,
    p_error_message TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    UPDATE sync_status 
    SET 
        status = p_status,
        total_items = COALESCE(p_total_items, total_items),
        completed_items = COALESCE(p_completed_items, completed_items),
        failed_items = COALESCE(p_failed_items, failed_items),
        api_requests_used = COALESCE(p_api_requests_used, api_requests_used),
        error_message = p_error_message,
        last_sync_at = CASE WHEN p_status = 'completed' THEN NOW() ELSE last_sync_at END,
        updated_at = NOW()
    WHERE sync_type = p_sync_type;
    
    -- 如果記錄不存在，則插入
    IF NOT FOUND THEN
        INSERT INTO sync_status (
            sync_type, status, total_items, completed_items, failed_items, 
            api_requests_used, error_message, last_sync_at
        ) VALUES (
            p_sync_type, p_status, p_total_items, p_completed_items, p_failed_items,
            p_api_requests_used, p_error_message, 
            CASE WHEN p_status = 'completed' THEN NOW() ELSE NULL END
        );
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 獲取同步狀態函數
CREATE OR REPLACE FUNCTION get_sync_status(p_sync_type VARCHAR(50) DEFAULT 'us_stocks')
RETURNS TABLE (
    sync_type VARCHAR(50),
    status VARCHAR(20),
    total_items INTEGER,
    completed_items INTEGER,
    failed_items INTEGER,
    completion_rate DECIMAL(5,2),
    api_requests_used INTEGER,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.sync_type,
        s.status,
        s.total_items,
        s.completed_items,
        s.failed_items,
        CASE 
            WHEN s.total_items > 0 THEN ROUND((s.completed_items::DECIMAL / s.total_items) * 100, 2)
            ELSE 0
        END as completion_rate,
        s.api_requests_used,
        s.last_sync_at,
        s.error_message
    FROM sync_status s
    WHERE s.sync_type = p_sync_type;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. 資料品質檢查函數
-- =====================================================

-- 檢查資料品質函數
CREATE OR REPLACE FUNCTION check_stock_data_quality()
RETURNS TABLE (
    metric VARCHAR(50),
    value BIGINT,
    description TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'total_stocks'::VARCHAR(50) as metric,
        COUNT(*) as value,
        '總股票數量'::TEXT as description
    FROM us_stocks
    
    UNION ALL
    
    SELECT 
        'stocks_with_prices'::VARCHAR(50),
        COUNT(*) as value,
        '有價格的股票數量'::TEXT
    FROM us_stocks 
    WHERE price IS NOT NULL
    
    UNION ALL
    
    SELECT 
        'stocks_without_prices'::VARCHAR(50),
        COUNT(*) as value,
        '沒有價格的股票數量'::TEXT
    FROM us_stocks 
    WHERE price IS NULL
    
    UNION ALL
    
    SELECT 
        'recent_updates'::VARCHAR(50),
        COUNT(*) as value,
        '最近24小時更新的股票'::TEXT
    FROM us_stocks 
    WHERE updated_at > NOW() - INTERVAL '24 hours'
    
    UNION ALL
    
    SELECT 
        'outdated_data'::VARCHAR(50),
        COUNT(*) as value,
        '超過7天未更新的股票'::TEXT
    FROM us_stocks 
    WHERE updated_at < NOW() - INTERVAL '7 days'
    
    UNION ALL
    
    SELECT 
        'price_anomalies'::VARCHAR(50),
        COUNT(*) as value,
        '價格異常的股票 (>$1000 或 <$1)'::TEXT
    FROM us_stocks 
    WHERE price > 1000 OR price < 1;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. 批量 UPSERT 優化
-- =====================================================

-- 優化的批量插入/更新函數
CREATE OR REPLACE FUNCTION batch_upsert_us_stocks(stock_data JSONB)
RETURNS TABLE (
    inserted_count INTEGER,
    updated_count INTEGER,
    error_count INTEGER
) AS $$
DECLARE
    stock_record JSONB;
    insert_count INTEGER := 0;
    update_count INTEGER := 0;
    err_count INTEGER := 0;
    existing_symbol BOOLEAN;
BEGIN
    FOR stock_record IN SELECT * FROM jsonb_array_elements(stock_data)
    LOOP
        BEGIN
            -- 檢查股票是否已存在
            SELECT EXISTS(SELECT 1 FROM us_stocks WHERE symbol = stock_record->>'symbol') INTO existing_symbol;
            
            -- 執行 UPSERT
            INSERT INTO us_stocks (
                symbol, name, chinese_name, sector, industry, price, open_price, 
                high_price, low_price, volume, change_amount, change_percent, 
                previous_close, market_cap, price_date, is_sp500
            ) VALUES (
                stock_record->>'symbol',
                stock_record->>'name',
                stock_record->>'chinese_name',
                stock_record->>'sector',
                stock_record->>'industry',
                (stock_record->>'price')::DECIMAL(10,2),
                (stock_record->>'open_price')::DECIMAL(10,2),
                (stock_record->>'high_price')::DECIMAL(10,2),
                (stock_record->>'low_price')::DECIMAL(10,2),
                (stock_record->>'volume')::BIGINT,
                (stock_record->>'change_amount')::DECIMAL(10,2),
                (stock_record->>'change_percent')::DECIMAL(5,2),
                (stock_record->>'previous_close')::DECIMAL(10,2),
                (stock_record->>'market_cap')::BIGINT,
                COALESCE((stock_record->>'price_date')::DATE, CURRENT_DATE),
                COALESCE((stock_record->>'is_sp500')::BOOLEAN, true)
            )
            ON CONFLICT (symbol) 
            DO UPDATE SET
                name = EXCLUDED.name,
                chinese_name = EXCLUDED.chinese_name,
                sector = EXCLUDED.sector,
                industry = EXCLUDED.industry,
                price = EXCLUDED.price,
                open_price = EXCLUDED.open_price,
                high_price = EXCLUDED.high_price,
                low_price = EXCLUDED.low_price,
                volume = EXCLUDED.volume,
                change_amount = EXCLUDED.change_amount,
                change_percent = EXCLUDED.change_percent,
                previous_close = EXCLUDED.previous_close,
                market_cap = EXCLUDED.market_cap,
                price_date = EXCLUDED.price_date,
                updated_at = NOW();
            
            -- 計數
            IF existing_symbol THEN
                update_count := update_count + 1;
            ELSE
                insert_count := insert_count + 1;
            END IF;
            
        EXCEPTION WHEN OTHERS THEN
            err_count := err_count + 1;
            RAISE NOTICE '處理股票 % 時發生錯誤: %', stock_record->>'symbol', SQLERRM;
        END;
    END LOOP;
    
    RETURN QUERY SELECT insert_count, update_count, err_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 6. 清理和維護函數
-- =====================================================

-- 清理舊資料函數
CREATE OR REPLACE FUNCTION cleanup_old_stock_data(days_to_keep INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 刪除超過指定天數且沒有價格的記錄
    DELETE FROM us_stocks 
    WHERE price IS NULL 
    AND created_at < NOW() - (days_to_keep || ' days')::INTERVAL;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 重建索引函數
CREATE OR REPLACE FUNCTION rebuild_stock_indexes()
RETURNS VOID AS $$
BEGIN
    -- 重建主要索引
    REINDEX INDEX idx_us_stocks_symbol;
    REINDEX INDEX idx_us_stocks_name;
    REINDEX INDEX idx_us_stocks_chinese_name;
    REINDEX INDEX idx_us_stocks_symbol_search;
    REINDEX INDEX idx_us_stocks_name_search;
    REINDEX INDEX idx_us_stocks_chinese_search;
    
    -- 更新表統計
    ANALYZE us_stocks;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 7. RLS 政策更新
-- =====================================================

-- 更新 RLS 政策以支援同步狀態表
ALTER TABLE sync_status ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Allow public read access" ON sync_status;
CREATE POLICY "Allow public read access" ON sync_status
    FOR SELECT USING (true);

DROP POLICY IF EXISTS "Allow service role write access" ON sync_status;
CREATE POLICY "Allow service role write access" ON sync_status
    FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- 8. 註解更新
-- =====================================================

COMMENT ON COLUMN us_stocks.chinese_name IS '中文名稱 (如: 蘋果)';
COMMENT ON TABLE sync_status IS '同步狀態追蹤表';
COMMENT ON FUNCTION update_sync_status IS '更新同步狀態';
COMMENT ON FUNCTION get_sync_status IS '獲取同步狀態';
COMMENT ON FUNCTION check_stock_data_quality IS '檢查資料品質';
COMMENT ON FUNCTION batch_upsert_us_stocks IS '批量插入/更新股票資料';

-- =====================================================
-- 優化完成
-- =====================================================

SELECT 
    '🎉 美股資料庫優化完成！' as status,
    '支援中文搜尋、同步狀態追蹤、資料品質檢查' as features,
    NOW() as optimization_time;
