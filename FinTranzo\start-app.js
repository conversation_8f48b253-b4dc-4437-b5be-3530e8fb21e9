#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting FinTranzo...');
console.log('📁 Working directory:', __dirname);

// 確保在正確的目錄中
process.chdir(__dirname);

// 啟動 Expo
const expo = spawn('npx', ['expo', 'start', '--web'], {
  stdio: 'inherit',
  shell: true,
  cwd: __dirname
});

expo.on('error', (error) => {
  console.error('❌ Failed to start Expo:', error);
});

expo.on('close', (code) => {
  console.log(`Expo process exited with code ${code}`);
});
