/**
 * GitHub Actions - 美股每日更新腳本
 */

const { createClient } = require('@supabase/supabase-js');

// Supabase 配置
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ 缺少 Supabase 環境變數');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * 使用 Alpha Vantage API 獲取美股報價
 */
async function fetchUSStockPrice(symbol) {
  try {
    const apiKey = process.env.ALPHA_VANTAGE_API_KEY;
    if (!apiKey) {
      console.warn('⚠️ 缺少 Alpha Vantage API 金鑰，使用免費 API');
      return await fetchUSStockPriceFree(symbol);
    }
    
    const response = await fetch(
      `https://www.alphavantage.co/query?function=GLOBAL_QUOTE&symbol=${symbol}&apikey=${apiKey}`
    );
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    const quote = data['Global Quote'];
    
    if (quote && quote['05. price']) {
      const price = parseFloat(quote['05. price']);
      const change = parseFloat(quote['09. change']);
      const changePercent = parseFloat(quote['10. change percent'].replace('%', ''));
      
      return {
        symbol,
        price,
        change,
        change_percent: changePercent,
        updated_at: new Date().toISOString()
      };
    }
    
    // 如果 Alpha Vantage 失敗，嘗試免費 API
    return await fetchUSStockPriceFree(symbol);
  } catch (error) {
    console.error(`❌ Alpha Vantage 獲取 ${symbol} 失敗:`, error.message);
    return await fetchUSStockPriceFree(symbol);
  }
}

/**
 * 使用免費 API 獲取美股報價（備用方案）
 */
async function fetchUSStockPriceFree(symbol) {
  try {
    // 使用 Yahoo Finance API（非官方）
    const response = await fetch(
      `https://query1.finance.yahoo.com/v8/finance/chart/${symbol}`
    );
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    const result = data.chart.result[0];
    
    if (result && result.meta) {
      const price = result.meta.regularMarketPrice;
      const previousClose = result.meta.previousClose;
      const change = price - previousClose;
      const changePercent = (change / previousClose) * 100;
      
      return {
        symbol,
        price,
        change,
        change_percent: changePercent,
        updated_at: new Date().toISOString()
      };
    }
    
    return null;
  } catch (error) {
    console.error(`❌ 免費 API 獲取 ${symbol} 失敗:`, error.message);
    return null;
  }
}

/**
 * 批量更新美股價格
 */
async function updateUSStocks() {
  try {
    const batchType = process.env.BATCH_TYPE || 'ALL';
    console.log(`🚀 開始更新美股價格 - 批次類型: ${batchType}`);

    // 根據批次類型獲取不同的股票
    let query = supabase.from('us_stocks').select('symbol, name, market_cap');

    switch (batchType) {
      case 'LARGE':
        // 大型股 (市值 > 100億，約200-300支)
        query = query.gte('market_cap', 10000000000).limit(300);
        console.log('🏢 處理美股大型股 (市值 > 100億)');
        break;
      case 'MID':
        // 中型股 (市值 10億-100億，約200-300支)
        query = query.gte('market_cap', 1000000000).lt('market_cap', 10000000000).limit(300);
        console.log('🏪 處理美股中型股 (市值 10億-100億)');
        break;
      case 'ETF':
        // ETF (約400支)
        query = query.eq('type', 'ETF').limit(400);
        console.log('📊 處理美股ETF');
        break;
      default:
        // 全部股票 (用於測試)
        query = query.limit(100);
        console.log('🔄 處理全部股票 (測試模式)');
    }

    const { data: stocks, error: fetchError } = await query;
    
    if (fetchError) {
      throw fetchError;
    }
    
    console.log(`📊 找到 ${stocks.length} 支美股需要更新`);
    
    let successCount = 0;
    let failedCount = 0;
    
    // 批量處理，提高效率
    const batchSize = 5; // 美股API限制較嚴格，使用較小批次
    const totalBatches = Math.ceil(stocks.length / batchSize);

    for (let i = 0; i < stocks.length; i += batchSize) {
      const batch = stocks.slice(i, i + batchSize);
      const currentBatch = Math.floor(i/batchSize) + 1;
      console.log(`📈 處理第 ${currentBatch}/${totalBatches} 批，股票 ${i + 1}-${Math.min(i + batchSize, stocks.length)}`);

      // 並行處理批次內的股票
      const promises = batch.map(async (stock, index) => {
        // 為避免API限制，批次內也要有小延遲
        await new Promise(resolve => setTimeout(resolve, index * 200));
        return {
          stock,
          result: await fetchUSStockPrice(stock.symbol)
        };
      });

      const batchResults = await Promise.all(promises);

      // 更新結果
      for (const { stock, result } of batchResults) {
        if (result) {
          const { error: updateError } = await supabase
            .from('us_stocks')
            .update({
              price: result.price,
              change_amount: result.change,
              change_percent: result.change_percent,
              updated_at: result.updated_at
            })
            .eq('symbol', result.symbol);

          if (updateError) {
            console.error(`❌ 更新 ${result.symbol} 失敗:`, updateError);
            failedCount++;
          } else {
            console.log(`✅ 更新 ${result.symbol}: $${result.price}`);
            successCount++;
          }
        } else {
          console.log(`❌ 無法獲取 ${stock.symbol} 的價格`);
          failedCount++;
        }
      }

      // 批次間等待，避免API限制
      if (i + batchSize < stocks.length) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      // 每50支股票報告一次進度
      if ((i + batchSize) % 50 === 0 || i + batchSize >= stocks.length) {
        console.log(`📊 進度報告: ${Math.min(i + batchSize, stocks.length)}/${stocks.length} (${Math.round((Math.min(i + batchSize, stocks.length) / stocks.length) * 100)}%)`);
      }
    }
    
    console.log(`✅ 美股更新完成: 成功 ${successCount}, 失敗 ${failedCount}`);
    
    // 記錄更新日誌
    const { error: logError } = await supabase
      .from('update_logs')
      .insert({
        type: 'us_stocks',
        success_count: successCount,
        failed_count: failedCount,
        total_count: stocks.length,
        updated_at: new Date().toISOString()
      });
    
    if (logError) {
      console.warn('⚠️ 記錄日誌失敗:', logError);
    }
    
    return {
      success: true,
      updated: successCount,
      failed: failedCount,
      total: stocks.length
    };
    
  } catch (error) {
    console.error('❌ 美股更新失敗:', error);
    throw error;
  }
}

/**
 * 主函數
 */
async function main() {
  try {
    console.log('🇺🇸 GitHub Actions - 美股更新開始');
    console.log('⏰ 執行時間:', new Date().toLocaleString('zh-TW'));
    
    const result = await updateUSStocks();
    
    console.log('📊 更新結果:', result);
    console.log('🎉 美股更新完成！');
    
    process.exit(0);
    
  } catch (error) {
    console.error('💥 執行失敗:', error);
    process.exit(1);
  }
}

// 執行主函數
if (require.main === module) {
  main();
}

module.exports = { updateUSStocks };
