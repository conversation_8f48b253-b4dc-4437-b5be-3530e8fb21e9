-- =====================================================
-- 美股資料庫完整設定
-- API → Supabase → 用戶 架構
-- 執行日期: 2025-06-01
-- =====================================================

-- =====================================================
-- 1. 美股資料表
-- =====================================================

-- 建立美股資料表
CREATE TABLE IF NOT EXISTS us_stocks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL UNIQUE,
    name VARCHAR(200) NOT NULL,
    sector VARCHAR(100),
    industry VARCHAR(100),
    market_cap BIGINT,
    price DECIMAL(10,2),
    open_price DECIMAL(10,2),
    high_price DECIMAL(10,2),
    low_price DECIMAL(10,2),
    volume BIGINT,
    change_amount DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    previous_close DECIMAL(10,2),
    price_date DATE DEFAULT CURRENT_DATE,
    is_sp500 BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 美股索引
CREATE INDEX IF NOT EXISTS idx_us_stocks_symbol ON us_stocks(symbol);
CREATE INDEX IF NOT EXISTS idx_us_stocks_name ON us_stocks(name);
CREATE INDEX IF NOT EXISTS idx_us_stocks_sector ON us_stocks(sector);
CREATE INDEX IF NOT EXISTS idx_us_stocks_price_date ON us_stocks(price_date DESC);
CREATE INDEX IF NOT EXISTS idx_us_stocks_symbol_search ON us_stocks(symbol varchar_pattern_ops);
CREATE INDEX IF NOT EXISTS idx_us_stocks_name_search ON us_stocks(name varchar_pattern_ops);
CREATE INDEX IF NOT EXISTS idx_us_stocks_sp500 ON us_stocks(is_sp500);
CREATE INDEX IF NOT EXISTS idx_us_stocks_market_cap ON us_stocks(market_cap DESC);
CREATE INDEX IF NOT EXISTS idx_us_stocks_price_not_null ON us_stocks(price) WHERE price IS NOT NULL;

-- =====================================================
-- 2. 同步狀態追蹤表
-- =====================================================

-- 創建同步狀態表
CREATE TABLE IF NOT EXISTS sync_status (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    sync_type VARCHAR(50) NOT NULL,
    total_items INTEGER DEFAULT 0,
    completed_items INTEGER DEFAULT 0,
    failed_items INTEGER DEFAULT 0,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'pending', -- pending, running, completed, failed
    api_requests_used INTEGER DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 同步狀態索引
CREATE INDEX IF NOT EXISTS idx_sync_status_type ON sync_status(sync_type);
CREATE INDEX IF NOT EXISTS idx_sync_status_status ON sync_status(status);
CREATE INDEX IF NOT EXISTS idx_sync_status_last_sync ON sync_status(last_sync_at DESC);

-- 插入美股同步狀態記錄
INSERT INTO sync_status (sync_type, status) 
VALUES ('us_stocks', 'pending')
ON CONFLICT DO NOTHING;

-- =====================================================
-- 3. 更新時間觸發器
-- =====================================================

-- 美股更新時間觸發器
CREATE OR REPLACE FUNCTION update_us_stocks_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_us_stocks_updated_at ON us_stocks;
CREATE TRIGGER trigger_update_us_stocks_updated_at
    BEFORE UPDATE ON us_stocks
    FOR EACH ROW
    EXECUTE FUNCTION update_us_stocks_updated_at();

-- 同步狀態更新觸發器
CREATE OR REPLACE FUNCTION update_sync_status_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_sync_status_updated_at ON sync_status;
CREATE TRIGGER trigger_update_sync_status_updated_at
    BEFORE UPDATE ON sync_status
    FOR EACH ROW
    EXECUTE FUNCTION update_sync_status_updated_at();

-- =====================================================
-- 4. 美股查詢函數
-- =====================================================

-- 搜尋美股函數 (優化版)
CREATE OR REPLACE FUNCTION search_us_stocks(
    search_term TEXT,
    sp500_only BOOLEAN DEFAULT true,
    limit_count INTEGER DEFAULT 50
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    sector VARCHAR(100),
    price DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.sector, s.price, s.change_percent, s.market_cap
    FROM us_stocks s
    WHERE 
        (sp500_only = false OR s.is_sp500 = true)
        AND (s.symbol ILIKE '%' || search_term || '%' OR s.name ILIKE '%' || search_term || '%')
        AND s.price IS NOT NULL  -- 只返回有價格的股票
    ORDER BY 
        CASE 
            WHEN s.symbol = UPPER(search_term) THEN 1  -- 完全匹配代號優先
            WHEN s.symbol ILIKE search_term || '%' THEN 2  -- 代號開頭匹配次優先
            ELSE 3
        END,
        s.market_cap DESC NULLS LAST
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 獲取美股統計函數
CREATE OR REPLACE FUNCTION get_us_stock_stats()
RETURNS TABLE (
    total_stocks BIGINT,
    sp500_count BIGINT,
    stocks_with_prices BIGINT,
    sectors_count BIGINT,
    last_updated DATE,
    avg_price DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_stocks,
        COUNT(*) FILTER (WHERE is_sp500 = true) as sp500_count,
        COUNT(*) FILTER (WHERE price IS NOT NULL) as stocks_with_prices,
        COUNT(DISTINCT sector) as sectors_count,
        MAX(price_date) as last_updated,
        AVG(price) as avg_price
    FROM us_stocks;
END;
$$ LANGUAGE plpgsql;

-- 獲取指定股票資訊
CREATE OR REPLACE FUNCTION get_us_stock_by_symbol(
    stock_symbol VARCHAR(10)
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    sector VARCHAR(100),
    industry VARCHAR(100),
    price DECIMAL(10,2),
    open_price DECIMAL(10,2),
    high_price DECIMAL(10,2),
    low_price DECIMAL(10,2),
    volume BIGINT,
    change_amount DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT,
    price_date DATE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.sector, s.industry, s.price, s.open_price,
        s.high_price, s.low_price, s.volume, s.change_amount, 
        s.change_percent, s.market_cap, s.price_date, s.updated_at
    FROM us_stocks s
    WHERE s.symbol = stock_symbol;
END;
$$ LANGUAGE plpgsql;

-- 獲取熱門股票 (按市值排序)
CREATE OR REPLACE FUNCTION get_popular_us_stocks(
    limit_count INTEGER DEFAULT 20
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    sector VARCHAR(100),
    price DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.sector, s.price, s.change_percent, s.market_cap
    FROM us_stocks s
    WHERE s.is_sp500 = true AND s.price IS NOT NULL
    ORDER BY s.market_cap DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 按行業分類獲取股票
CREATE OR REPLACE FUNCTION get_us_stocks_by_sector(
    target_sector VARCHAR(100),
    limit_count INTEGER DEFAULT 20
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    price DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.price, s.change_percent, s.market_cap
    FROM us_stocks s
    WHERE s.sector = target_sector AND s.price IS NOT NULL
    ORDER BY s.market_cap DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. 同步狀態管理函數
-- =====================================================

-- 更新同步狀態函數
CREATE OR REPLACE FUNCTION update_sync_status(
    p_sync_type VARCHAR(50),
    p_status VARCHAR(20),
    p_total_items INTEGER DEFAULT NULL,
    p_completed_items INTEGER DEFAULT NULL,
    p_failed_items INTEGER DEFAULT NULL,
    p_api_requests_used INTEGER DEFAULT NULL,
    p_error_message TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    UPDATE sync_status 
    SET 
        status = p_status,
        total_items = COALESCE(p_total_items, total_items),
        completed_items = COALESCE(p_completed_items, completed_items),
        failed_items = COALESCE(p_failed_items, failed_items),
        api_requests_used = COALESCE(p_api_requests_used, api_requests_used),
        error_message = p_error_message,
        last_sync_at = CASE WHEN p_status = 'completed' THEN NOW() ELSE last_sync_at END
    WHERE sync_type = p_sync_type;
    
    -- 如果記錄不存在，則插入
    IF NOT FOUND THEN
        INSERT INTO sync_status (
            sync_type, status, total_items, completed_items, failed_items, 
            api_requests_used, error_message, last_sync_at
        ) VALUES (
            p_sync_type, p_status, p_total_items, p_completed_items, p_failed_items,
            p_api_requests_used, p_error_message, 
            CASE WHEN p_status = 'completed' THEN NOW() ELSE NULL END
        );
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 獲取同步狀態函數
CREATE OR REPLACE FUNCTION get_sync_status(p_sync_type VARCHAR(50) DEFAULT 'us_stocks')
RETURNS TABLE (
    sync_type VARCHAR(50),
    status VARCHAR(20),
    total_items INTEGER,
    completed_items INTEGER,
    failed_items INTEGER,
    completion_rate DECIMAL(5,2),
    api_requests_used INTEGER,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.sync_type,
        s.status,
        s.total_items,
        s.completed_items,
        s.failed_items,
        CASE 
            WHEN s.total_items > 0 THEN ROUND((s.completed_items::DECIMAL / s.total_items) * 100, 2)
            ELSE 0
        END as completion_rate,
        s.api_requests_used,
        s.last_sync_at,
        s.error_message
    FROM sync_status s
    WHERE s.sync_type = p_sync_type;
END;
$$ LANGUAGE plpgsql;
