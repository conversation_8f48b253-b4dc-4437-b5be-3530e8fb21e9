import os
import csv
import time
import requests
import pandas as pd
from datetime import datetime
from fastapi import FastAPI, HTTPException, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
from supabase import create_client, Client
from typing import List, Optional

# Load environment variables
load_dotenv()

# Initialize FastAPI
app = FastAPI(title="US Stock Data API",
             description="API for fetching and managing US stock data")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize Supabase
url: str = os.getenv('EXPO_PUBLIC_SUPABASE_URL')
key: str = os.getenv('EXPO_PUBLIC_SUPABASE_ANON_KEY')
supabase: Client = create_client(url, key)

# Alpha Vantage API configuration
ALPHA_VANTAGE_API_KEY = os.getenv('EXPO_PUBLIC_ALPHA_VANTAGE_API_KEY')
ALPHA_VANTAGE_BASE_URL = 'https://www.alphavantage.co/query'

# Constants
CSV_FILE = 'database/20250601135735.csv'
RATE_LIMIT_DELAY = 15  # Delay in seconds between API calls to respect rate limits

def load_sp500_symbols() -> List[dict]:
    """Load S&P 500 symbols from CSV file"""
    stocks = []
    with open(CSV_FILE, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        for row in reader:
            if len(row) >= 2:
                stocks.append({
                    'symbol': row[0],
                    'name': row[1],
                    'sector': row[2] if len(row) > 2 else 'Unknown'
                })
    return stocks

def fetch_stock_data(symbol: str) -> dict:
    """Fetch stock data from Alpha Vantage API"""
    try:
        # First, get the global quote for current price
        params = {
            'function': 'GLOBAL_QUOTE',
            'symbol': symbol,
            'apikey': ALPHA_VANTAGE_API_KEY
        }
        
        response = requests.get(ALPHA_VANTAGE_BASE_URL, params=params)
        data = response.json()
        
        if 'Global Quote' in data and data['Global Quote']:
            quote = data['Global Quote']
            return {
                'symbol': symbol,
                'price': float(quote.get('05. price', 0)),
                'change': float(quote.get('09. change', 0)),
                'change_percent': float(quote.get('10. change percent', '0%').rstrip('%')),
                'last_updated': datetime.utcnow().isoformat()
            }
        
        # If global quote fails, try overview
        params = {
            'function': 'OVERVIEW',
            'symbol': symbol,
            'apikey': ALPHA_VANTAGE_API_KEY
        }
        
        response = requests.get(ALPHA_VANTAGE_API_KEY, params=params)
        data = response.json()
        
        if 'Symbol' in data:
            return {
                'symbol': data['Symbol'],
                'name': data.get('Name', ''),
                'sector': data.get('Sector', 'Unknown'),
                'price': float(data.get('Price', 0)),
                'last_updated': datetime.utcnow().isoformat()
            }
            
        return None
        
    except Exception as e:
        print(f"Error fetching data for {symbol}: {str(e)}")
        return None

def update_stock_in_database(stock_data: dict):
    """Update or insert stock data in Supabase"""
    try:
        # Check if stock exists
        response = supabase.table('us_stocks').select('symbol').eq('symbol', stock_data['symbol']).execute()
        
        if response.data:
            # Update existing stock
            supabase.table('us_stocks').update({
                'price': stock_data.get('price'),
                'change': stock_data.get('change', 0),
                'change_percent': stock_data.get('change_percent', 0),
                'updated_at': datetime.utcnow().isoformat(),
                'name': stock_data.get('name', ''),
                'sector': stock_data.get('sector', 'Unknown')
            }).eq('symbol', stock_data['symbol']).execute()
        else:
            # Insert new stock
            supabase.table('us_stocks').insert({
                'symbol': stock_data['symbol'],
                'name': stock_data.get('name', ''),
                'sector': stock_data.get('sector', 'Unknown'),
                'price': stock_data.get('price', 0),
                'change': stock_data.get('change', 0),
                'change_percent': stock_data.get('change_percent', 0),
                'is_sp500': True,
                'created_at': datetime.utcnow().isoformat(),
                'updated_at': datetime.utcnow().isoformat()
            }).execute()
            
    except Exception as e:
        print(f"Error updating database for {stock_data.get('symbol')}: {str(e)}")

def update_sync_status(symbol: str, status: str, message: str = ''):
    """Update sync status in the database"""
    try:
        supabase.table('sync_status').upsert({
            'symbol': symbol,
            'status': status,
            'message': message,
            'last_updated': datetime.utcnow().isoformat()
        }).execute()
    except Exception as e:
        print(f"Error updating sync status for {symbol}: {str(e)}")

@app.get("/api/stocks/sync", tags=["Stocks"])
async def sync_stock_data():
    """Sync stock data for all S&P 500 stocks"""
    stocks = load_sp500_symbols()
    synced = 0
    
    for stock in stocks:
        symbol = stock['symbol']
        try:
            update_sync_status(symbol, 'in_progress')
            stock_data = fetch_stock_data(symbol)
            
            if stock_data:
                update_stock_in_database(stock_data)
                update_sync_status(symbol, 'completed')
                synced += 1
            else:
                update_sync_status(symbol, 'failed', 'No data returned from API')
            
            # Respect API rate limits (5 requests per minute for free tier)
            time.sleep(RATE_LIMIT_DELAY)
            
        except Exception as e:
            update_sync_status(symbol, 'error', str(e))
            print(f"Error processing {symbol}: {str(e)}")
    
    return {"status": "completed", "synced": synced, "total": len(stocks)}

@app.get("/api/stocks/search", tags=["Stocks"])
async def search_stocks(
    query: str = Query(..., min_length=1, description="Stock symbol or company name to search for")
):
    """Search for stocks by symbol or company name"""
    try:
        # Search by symbol (exact match)
        response = supabase.table('us_stocks') \
            .select('*') \
            .ilike('symbol', f'%{query.upper()}%') \
            .execute()
        
        if response.data:
            return response.data
        
        # If no match by symbol, search by name
        response = supabase.table('us_stocks') \
            .select('*') \
            .ilike('name', f'%{query}%') \
            .limit(10) \
            .execute()
            
        return response.data
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/stocks/{symbol}", tags=["Stocks"])
async def get_stock(symbol: str):
    """Get stock data by symbol"""
    try:
        response = supabase.table('us_stocks') \
            .select('*') \
            .eq('symbol', symbol.upper()) \
            .execute()
        
        if not response.data:
            # Try to fetch fresh data if not in database
            stock_data = fetch_stock_data(symbol.upper())
            if stock_data:
                update_stock_in_database(stock_data)
                return stock_data
            raise HTTPException(status_code=404, detail="Stock not found")
            
        return response.data[0]
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/stocks", tags=["Stocks"])
async def list_stocks(
    limit: int = Query(20, ge=1, le=100, description="Number of stocks to return"),
    offset: int = Query(0, ge=0, description="Pagination offset")
):
    """List all stocks with pagination"""
    try:
        response = supabase.table('us_stocks') \
            .select('*') \
            .order('symbol') \
            .range(offset, offset + limit - 1) \
            .execute()
            
        return {
            "data": response.data,
            "count": len(response.data),
            "offset": offset,
            "limit": limit
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app:app", host="0.0.0.0", port=8000, reload=True)
