// 檢查當前日期和5月的天數
const now = new Date();
console.log('當前日期:', now.toLocaleDateString('zh-TW'));
console.log('當前年份:', now.getFullYear());
console.log('當前月份:', now.getMonth() + 1);
console.log('當前日期:', now.getDate());

// 檢查5月的天數
const may2025 = new Date(2025, 4 + 1, 0); // 5月的最後一天
console.log('2025年5月的天數:', may2025.getDate());

// 測試31號在5月是否需要調整
const paymentDay = 31;
const currentMonth = 4; // 5月 (0-based)
const lastDayOfMay = new Date(2025, currentMonth + 1, 0).getDate();

console.log(`設定還款日: ${paymentDay}號`);
console.log(`5月最後一天: ${lastDayOfMay}號`);
console.log(`是否需要調整: ${paymentDay > lastDayOfMay ? '是' : '否'}`);

if (paymentDay > lastDayOfMay) {
  console.log(`❌ 錯誤：31號被調整為${lastDayOfMay}號`);
} else {
  console.log(`✅ 正確：31號不需要調整`);
}
