-- 台股資料庫架構 (Enhanced) - 支援上市、上櫃、ETF
-- 適用於 Supabase PostgreSQL
-- 更新日期：2025-05-29

-- 刪除舊資料表（如果存在）
DROP TABLE IF EXISTS taiwan_stocks CASCADE;

-- 建立股票市場類型枚舉
CREATE TYPE market_type AS ENUM ('TSE', 'OTC', 'ETF');

-- 建立台股資料表
CREATE TABLE taiwan_stocks (
    -- 基本資訊
    code VARCHAR(10) PRIMARY KEY,              -- 股票代號 (如: 2330, 00878)
    name VARCHAR(100) NOT NULL,                -- 股票名稱
    market_type market_type NOT NULL,          -- 市場類型 (TSE/OTC/ETF)
    
    -- 價格資訊
    closing_price DECIMAL(10,2),               -- 收盤價
    opening_price DECIMAL(10,2),               -- 開盤價
    highest_price DECIMAL(10,2),               -- 最高價
    lowest_price DECIMAL(10,2),                -- 最低價
    
    -- 交易資訊
    volume BIGINT DEFAULT 0,                   -- 成交量
    transaction_count INTEGER DEFAULT 0,       -- 成交筆數
    turnover DECIMAL(15,2) DEFAULT 0,          -- 成交金額
    
    -- 漲跌資訊
    price_change DECIMAL(10,2) DEFAULT 0,      -- 漲跌價差
    change_percent DECIMAL(5,2) DEFAULT 0,     -- 漲跌幅 (%)
    
    -- 時間戳記
    price_date DATE NOT NULL,                  -- 價格日期
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 索引約束
    CONSTRAINT valid_price CHECK (closing_price >= 0),
    CONSTRAINT valid_volume CHECK (volume >= 0),
    CONSTRAINT valid_change_percent CHECK (change_percent >= -100 AND change_percent <= 100)
);

-- 建立索引以提升查詢效能
CREATE INDEX idx_taiwan_stocks_market_type ON taiwan_stocks(market_type);
CREATE INDEX idx_taiwan_stocks_price_date ON taiwan_stocks(price_date);
CREATE INDEX idx_taiwan_stocks_code_date ON taiwan_stocks(code, price_date);
-- 使用標準全文搜尋 (不依賴中文配置)
CREATE INDEX idx_taiwan_stocks_name ON taiwan_stocks USING gin(to_tsvector('simple', name));
-- 為股票代號和名稱建立 LIKE 查詢索引
CREATE INDEX idx_taiwan_stocks_code_like ON taiwan_stocks(code varchar_pattern_ops);
CREATE INDEX idx_taiwan_stocks_name_like ON taiwan_stocks(name varchar_pattern_ops);

-- 建立更新時間觸發器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_taiwan_stocks_updated_at 
    BEFORE UPDATE ON taiwan_stocks 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 建立 RLS (Row Level Security) 政策
ALTER TABLE taiwan_stocks ENABLE ROW LEVEL SECURITY;

-- 允許所有人讀取股票資料
CREATE POLICY "Allow public read access" ON taiwan_stocks
    FOR SELECT USING (true);

-- 只允許認證用戶更新資料（用於自動更新腳本）
CREATE POLICY "Allow authenticated updates" ON taiwan_stocks
    FOR ALL USING (auth.role() = 'authenticated');

-- 建立批量更新函數
CREATE OR REPLACE FUNCTION upsert_stock_data(
    p_code VARCHAR(10),
    p_name VARCHAR(100),
    p_market_type market_type,
    p_closing_price DECIMAL(10,2),
    p_opening_price DECIMAL(10,2),
    p_highest_price DECIMAL(10,2),
    p_lowest_price DECIMAL(10,2),
    p_volume BIGINT,
    p_transaction_count INTEGER,
    p_turnover DECIMAL(15,2),
    p_price_change DECIMAL(10,2),
    p_change_percent DECIMAL(5,2),
    p_price_date DATE
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO taiwan_stocks (
        code, name, market_type, closing_price, opening_price, 
        highest_price, lowest_price, volume, transaction_count, 
        turnover, price_change, change_percent, price_date
    ) VALUES (
        p_code, p_name, p_market_type, p_closing_price, p_opening_price,
        p_highest_price, p_lowest_price, p_volume, p_transaction_count,
        p_turnover, p_price_change, p_change_percent, p_price_date
    )
    ON CONFLICT (code) DO UPDATE SET
        name = EXCLUDED.name,
        market_type = EXCLUDED.market_type,
        closing_price = EXCLUDED.closing_price,
        opening_price = EXCLUDED.opening_price,
        highest_price = EXCLUDED.highest_price,
        lowest_price = EXCLUDED.lowest_price,
        volume = EXCLUDED.volume,
        transaction_count = EXCLUDED.transaction_count,
        turnover = EXCLUDED.turnover,
        price_change = EXCLUDED.price_change,
        change_percent = EXCLUDED.change_percent,
        price_date = EXCLUDED.price_date,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- 建立查詢視圖
CREATE VIEW v_stock_summary AS
SELECT 
    market_type,
    COUNT(*) as stock_count,
    AVG(closing_price) as avg_price,
    SUM(volume) as total_volume,
    MAX(price_date) as latest_date
FROM taiwan_stocks 
GROUP BY market_type;

-- 建立熱門股票視圖 (依成交量排序)
CREATE VIEW v_popular_stocks AS
SELECT 
    code,
    name,
    market_type,
    closing_price,
    volume,
    change_percent,
    price_date
FROM taiwan_stocks 
WHERE price_date = (SELECT MAX(price_date) FROM taiwan_stocks)
ORDER BY volume DESC
LIMIT 50;

-- 建立漲跌幅排行視圖
CREATE VIEW v_price_movers AS
SELECT 
    code,
    name,
    market_type,
    closing_price,
    price_change,
    change_percent,
    volume,
    price_date
FROM taiwan_stocks 
WHERE price_date = (SELECT MAX(price_date) FROM taiwan_stocks)
  AND change_percent IS NOT NULL
ORDER BY change_percent DESC;

-- 插入範例資料
INSERT INTO taiwan_stocks (code, name, market_type, closing_price, price_date) VALUES
('2330', '台積電', 'TSE', 967.00, '2025-05-29'),
('2317', '鴻海', 'TSE', 156.00, '2025-05-29'),
('2454', '聯發科', 'TSE', 1200.00, '2025-05-29'),
('0050', '元大台灣50', 'ETF', 179.75, '2025-05-29'),
('0056', '元大高股息', 'ETF', 34.08, '2025-05-29'),
('00878', '國泰永續高股息', 'ETF', 20.43, '2025-05-29'),
('3008', '大立光', 'TSE', 2800.00, '2025-05-29'),
('6488', '環球晶', 'OTC', 168.50, '2025-05-29'),
('4938', '和碩', 'TSE', 95.20, '2025-05-29'),
('6505', '台塑化', 'TSE', 98.70, '2025-05-29');

-- 建立統計查詢
COMMENT ON TABLE taiwan_stocks IS '台灣股票資料表 - 包含上市(TSE)、上櫃(OTC)、ETF';
COMMENT ON COLUMN taiwan_stocks.code IS '股票代號';
COMMENT ON COLUMN taiwan_stocks.name IS '股票名稱';
COMMENT ON COLUMN taiwan_stocks.market_type IS '市場類型: TSE(上市), OTC(上櫃), ETF(指數股票型基金)';
COMMENT ON COLUMN taiwan_stocks.closing_price IS '收盤價';
COMMENT ON COLUMN taiwan_stocks.volume IS '成交量(股)';
COMMENT ON COLUMN taiwan_stocks.change_percent IS '漲跌幅百分比';

-- 查詢驗證
SELECT 
    '資料表建立完成' as status,
    COUNT(*) as sample_count,
    COUNT(DISTINCT market_type) as market_types
FROM taiwan_stocks;
