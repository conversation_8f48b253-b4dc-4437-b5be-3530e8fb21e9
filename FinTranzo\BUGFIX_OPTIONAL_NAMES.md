# 🔧 功能改進：所有名稱欄位改為非必填

## 改進概述
將所有添加表單中的名稱欄位改為非必填，並在用戶未填寫時自動使用預設名稱，提升用戶體驗。

## 修改的表單

### 1. 📝 交易記錄表單 (AddTransactionModal)

#### 修改內容
- **描述欄位** 從必填改為可選
- **標題顯示**: "描述 (可選)"
- **預設名稱**: 收入類型使用"收入"，支出類型使用"支出"
- **提示文字**: 動態顯示預設值

#### 修改前
```typescript
if (!amount || !description) {
  Alert.alert('錯誤', '請填寫所有必填欄位');
  return;
}
```

#### 修改後
```typescript
if (!amount) {
  Alert.alert('錯誤', '請填寫金額');
  return;
}

// 使用預設描述如果沒有填寫
const defaultDescription = type === 'income' ? '收入' : '支出';
const finalDescription = description.trim() || defaultDescription;
```

### 2. 💎 資產表單 (AddAssetModal)

#### 修改內容
- **資產名稱** 從必填改為可選
- **標題顯示**: "資產名稱 (可選)"
- **預設名稱**: 使用選中的資產類型名稱（如"台股"、"美股"等）
- **提示文字**: 顯示預設值

#### 修改前
```typescript
if (!name || !quantity || !costBasis) {
  Alert.alert('錯誤', '請填寫所有必填欄位');
  return;
}
```

#### 修改後
```typescript
if (!quantity || !costBasis) {
  Alert.alert('錯誤', '請填寫數量和成本基礎');
  return;
}

// 使用預設名稱如果沒有填寫
const selectedAssetType = assetTypes.find(t => t.key === type);
const defaultName = selectedAssetType?.label || '資產';
const finalName = name.trim() || defaultName;
```

### 3. 💳 負債表單 (AddLiabilityModal)

#### 修改內容
- **負債名稱** 從必填改為可選
- **標題顯示**: "負債名稱 (可選)"
- **預設名稱**: 使用選中的負債類型名稱（如"信用卡"、"房屋貸款"等）
- **提示文字**: 顯示預設值

#### 修改前
```typescript
if (!name || !balance) {
  Alert.alert('錯誤', '請填寫所有必填欄位');
  return;
}
```

#### 修改後
```typescript
if (!balance) {
  Alert.alert('錯誤', '請填寫當前餘額');
  return;
}

// 使用預設名稱如果沒有填寫
const selectedLiabilityType = liabilityTypes.find(t => t.key === type);
const defaultName = selectedLiabilityType?.label || '負債';
const finalName = name.trim() || defaultName;
```

## 預設名稱對應表

### 交易記錄
| 類型 | 預設名稱 |
|------|----------|
| 收入 | "收入" |
| 支出 | "支出" |

### 資產類型
| 類型 | 預設名稱 |
|------|----------|
| tw_stock | "台股" |
| us_stock | "美股" |
| mutual_fund | "共同基金" |
| cryptocurrency | "加密貨幣" |
| real_estate | "不動產" |
| vehicle | "汽車" |
| other | "其他" |

### 負債類型
| 類型 | 預設名稱 |
|------|----------|
| credit_card | "信用卡" |
| personal_loan | "信用貸款" |
| mortgage | "房屋貸款" |
| car_loan | "汽車貸款" |
| other_loan | "其他貸款" |

## 用戶體驗改進

### 🎯 優點
1. **更快的操作** - 用戶可以跳過名稱輸入，直接填寫重要數據
2. **減少錯誤** - 不會因為忘記填寫名稱而無法保存
3. **智能預設** - 根據選擇的類型自動生成合理的名稱
4. **清晰提示** - 用戶知道會使用什麼預設名稱

### 📱 使用場景

#### 快速記帳
```
用戶操作：
1. 選擇"支出"
2. 輸入金額"500"
3. 選擇類別"餐飲"
4. 直接點擊保存

結果：
- 描述: "支出"
- 金額: 500
- 類別: 餐飲
```

#### 快速添加資產
```
用戶操作：
1. 選擇"台股"
2. 輸入數量"100"
3. 輸入成本"45000"
4. 直接點擊保存

結果：
- 名稱: "台股"
- 數量: 100
- 成本: 45000
```

## 技術實現細節

### 字串處理
```typescript
// 使用 trim() 去除空白字符
const finalName = name.trim() || defaultName;

// 確保即使輸入只有空格也會使用預設名稱
```

### 動態提示文字
```typescript
placeholder={`輸入${selectedAssetType?.label}名稱 (預設: ${selectedAssetType?.label})`}
```

### 類型安全
```typescript
const selectedAssetType = assetTypes.find(t => t.key === type);
const defaultName = selectedAssetType?.label || '資產';
```

## 測試場景

### ✅ 測試用例 1：完全不填寫名稱
- **操作**: 只填寫必要欄位，名稱留空
- **預期**: 使用預設名稱成功保存
- **結果**: ✅ 通過

### ✅ 測試用例 2：只輸入空格
- **操作**: 名稱欄位只輸入空格
- **預期**: 使用預設名稱成功保存
- **結果**: ✅ 通過

### ✅ 測試用例 3：正常填寫名稱
- **操作**: 正常填寫自定義名稱
- **預期**: 使用用戶輸入的名稱
- **結果**: ✅ 通過

### ✅ 測試用例 4：切換類型後的預設名稱
- **操作**: 切換不同的資產/負債類型
- **預期**: 提示文字和預設名稱相應更新
- **結果**: ✅ 通過

## 向後兼容性
- ✅ 現有數據不受影響
- ✅ 用戶仍可自定義名稱
- ✅ 表單驗證邏輯更寬鬆但仍安全

## 狀態
- ✅ **已實現**
- ✅ **已測試**
- ✅ **已更新文檔**
- ✅ **已更新 Demo**

這個改進讓 FinTranzo 的使用更加便捷，特別適合快速記帳和資產管理的場景。
