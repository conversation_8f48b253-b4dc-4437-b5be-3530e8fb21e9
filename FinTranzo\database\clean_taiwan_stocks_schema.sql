-- 台股資料庫架構 - 完全乾淨的重建版本
-- 先刪除所有舊的物件，再重新建立

-- 第一步：刪除所有舊的函數和表格
DROP FUNCTION IF EXISTS search_stocks(text);
DROP FUNCTION IF EXISTS get_stock_by_code(VARCHAR(10));
DROP FUNCTION IF EXISTS get_latest_stock_price(VARCHAR(10));
DROP FUNCTION IF EXISTS update_daily_stock_prices(JSONB);
DROP FUNCTION IF EXISTS cleanup_old_stock_data();
DROP FUNCTION IF EXISTS update_taiwan_stocks_timestamp();

-- 刪除舊表格和觸發器
DROP TABLE IF EXISTS taiwan_stocks CASCADE;

-- 第二步：建立新的台股資料表
CREATE TABLE taiwan_stocks (
  code VARCHAR(10) PRIMARY KEY,                    -- 股票代號（主鍵）
  name VARCHAR(100) NOT NULL,                      -- 股票名稱
  closing_price DECIMAL(10,2) NOT NULL,            -- 收盤價
  monthly_average_price DECIMAL(10,2),             -- 月平均價
  price_date DATE NOT NULL,                        -- 價格日期
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 建立索引以提升查詢效能
CREATE INDEX idx_taiwan_stocks_name ON taiwan_stocks(name);
CREATE INDEX idx_taiwan_stocks_price_date ON taiwan_stocks(price_date);
CREATE INDEX idx_taiwan_stocks_code_name ON taiwan_stocks(code, name);

-- 建立更新時間觸發器函數
CREATE OR REPLACE FUNCTION update_taiwan_stocks_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 建立觸發器
CREATE TRIGGER trigger_update_taiwan_stocks_timestamp
    BEFORE UPDATE ON taiwan_stocks
    FOR EACH ROW
    EXECUTE FUNCTION update_taiwan_stocks_timestamp();

-- 建立 RLS (Row Level Security) 政策
ALTER TABLE taiwan_stocks ENABLE ROW LEVEL SECURITY;

-- 允許所有人讀取台股資料
CREATE POLICY "公開讀取台股資料" ON taiwan_stocks
    FOR SELECT USING (true);

-- 只允許服務角色寫入資料（用於每日自動更新）
CREATE POLICY "服務角色寫入權限" ON taiwan_stocks
    FOR ALL USING (auth.role() = 'service_role');

-- 建立函數：批量更新股票資料（每日自動更新使用）
CREATE FUNCTION update_daily_stock_prices(stock_data JSONB)
RETURNS INTEGER AS $$
DECLARE
    stock_record JSONB;
    updated_count INTEGER := 0;
BEGIN
    -- 遍歷股票資料並更新
    FOR stock_record IN SELECT * FROM jsonb_array_elements(stock_data)
    LOOP
        INSERT INTO taiwan_stocks (code, name, closing_price, monthly_average_price, price_date)
        VALUES (
            stock_record->>'code',
            stock_record->>'name',
            (stock_record->>'closing_price')::DECIMAL(10,2),
            (stock_record->>'monthly_average_price')::DECIMAL(10,2),
            (stock_record->>'date')::DATE
        )
        ON CONFLICT (code) DO UPDATE SET
            name = EXCLUDED.name,
            closing_price = EXCLUDED.closing_price,
            monthly_average_price = EXCLUDED.monthly_average_price,
            price_date = EXCLUDED.price_date,
            updated_at = NOW();
        
        updated_count := updated_count + 1;
    END LOOP;
    
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 建立函數：搜尋股票（支援代號或名稱模糊搜尋）
CREATE FUNCTION search_stocks(search_term TEXT)
RETURNS TABLE(
    code VARCHAR(10),
    name VARCHAR(100),
    closing_price DECIMAL(10,2),
    price_date DATE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        ts.code,
        ts.name,
        ts.closing_price,
        ts.price_date
    FROM taiwan_stocks ts
    WHERE ts.code ILIKE search_term || '%'
       OR ts.name ILIKE '%' || search_term || '%'
    ORDER BY
        CASE
            WHEN ts.code ILIKE search_term || '%' THEN 1
            ELSE 2
        END,
        ts.code
    LIMIT 20;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 建立函數：獲取特定股票資料
CREATE FUNCTION get_stock_by_code(stock_code VARCHAR(10))
RETURNS TABLE(
    code VARCHAR(10),
    name VARCHAR(100),
    closing_price DECIMAL(10,2),
    price_date DATE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        ts.code,
        ts.name,
        ts.closing_price,
        ts.price_date
    FROM taiwan_stocks ts
    WHERE ts.code = stock_code;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 建立函數：清理舊資料（可選，用於維護）
CREATE FUNCTION cleanup_old_stock_data()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 刪除超過 7 天的資料（保留最新資料）
    DELETE FROM taiwan_stocks 
    WHERE price_date < CURRENT_DATE - INTERVAL '7 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 建立函數：獲取資料庫統計資訊
CREATE FUNCTION get_stock_database_stats()
RETURNS TABLE(
    total_stocks INTEGER,
    latest_date DATE,
    last_updated TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*)::INTEGER as total_stocks,
        MAX(ts.price_date) as latest_date,
        MAX(ts.updated_at) as last_updated
    FROM taiwan_stocks ts;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 注意：不插入測試資料，等待從台灣證交所 API 獲取實際資料
-- 執行以下命令來獲取實際股票資料：
-- node database/fetch_json_stock_data.js

-- 查詢範例：
-- 1. 搜尋股票：SELECT * FROM search_stocks('233');
-- 2. 獲取特定股票：SELECT * FROM get_stock_by_code('2330');
-- 3. 列出所有股票：SELECT * FROM taiwan_stocks ORDER BY code LIMIT 10;
-- 4. 獲取統計資訊：SELECT * FROM get_stock_database_stats();
-- 5. 批量更新範例：
--    SELECT update_daily_stock_prices('[
--      {"code":"2330","name":"台積電","closing_price":"1000.00","monthly_average_price":"980.50","date":"2024-12-30"}
--    ]'::jsonb);

-- 完成！現在可以執行 fetch_json_stock_data.js 來獲取真實的台股資料
