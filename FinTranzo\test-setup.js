// 簡單的設置測試
console.log('Testing FinTranzo setup...');

// 測試 TypeScript 編譯
const { execSync } = require('child_process');

try {
  console.log('1. Testing TypeScript compilation...');
  execSync('npx tsc --noEmit', { stdio: 'inherit' });
  console.log('✅ TypeScript compilation successful');
} catch (error) {
  console.log('❌ TypeScript compilation failed:', error.message);
}

// 測試依賴
try {
  console.log('2. Testing dependencies...');
  require('@supabase/supabase-js');
  console.log('✅ Supabase dependency loaded');
  
  require('zustand');
  console.log('✅ Zustand dependency loaded');
  
  require('@react-navigation/native');
  console.log('✅ React Navigation dependency loaded');
  
  console.log('✅ All dependencies loaded successfully');
} catch (error) {
  console.log('❌ Dependency loading failed:', error.message);
}

console.log('Setup test completed!');
