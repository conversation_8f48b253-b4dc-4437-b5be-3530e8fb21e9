#!/usr/bin/env python3
"""
使用Yahoo Finance API更新ETF股價到Supabase
"""

import os
import sys
import time
import yfinance as yf
from datetime import datetime, date
from supabase import create_client, Client
import warnings

# 忽略警告
warnings.filterwarnings('ignore')

# Supabase配置
SUPABASE_URL = "https://yrryyapzkgrsahranzvo.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlycnl5YXB6a2dyc2FocmFuenZvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODE3MzYzNSwiZXhwIjoyMDYzNzQ5NjM1fQ.xjiXVagzKgBBYXqtRjJiXwCgbRdEW0EpFzCIlJ26SSQ"

def get_etf_quote_yahoo(symbol):
    """從Yahoo Finance獲取ETF即時報價"""
    try:
        print(f"🔍 獲取 {symbol} 的Yahoo Finance報價...")
        
        # 創建ticker對象
        ticker = yf.Ticker(symbol)
        
        # 獲取基本信息
        info = ticker.info
        
        # 獲取歷史數據（最近2天）
        hist = ticker.history(period="2d")
        
        if hist.empty:
            print(f"⚠️ 沒有找到 {symbol} 的歷史數據")
            return None
        
        # 獲取最新數據
        latest = hist.iloc[-1]
        previous = hist.iloc[-2] if len(hist) > 1 else latest
        
        # 計算變化
        current_price = float(latest['Close'])
        previous_close = float(previous['Close'])
        change_amount = current_price - previous_close
        change_percent = (change_amount / previous_close * 100) if previous_close != 0 else 0
        
        # 準備價格數據
        price_data = {
            'symbol': symbol,
            'price': round(current_price, 2),
            'open_price': round(float(latest['Open']), 2),
            'high_price': round(float(latest['High']), 2),
            'low_price': round(float(latest['Low']), 2),
            'volume': int(latest['Volume']) if latest['Volume'] else 0,
            'change_amount': round(change_amount, 2),
            'change_percent': round(change_percent, 2),
            'previous_close': round(previous_close, 2),
            'price_date': latest.name.strftime('%Y-%m-%d'),
            'updated_at': datetime.now().isoformat(),
            'market_cap': info.get('marketCap', None)
        }
        
        print(f"✅ {symbol}: ${price_data['price']} ({price_data['change_percent']:+.2f}%)")
        return price_data
        
    except Exception as e:
        print(f"❌ 獲取 {symbol} 報價時出錯: {str(e)}")
        return None

def update_etf_price_in_supabase(supabase: Client, price_data):
    """更新ETF價格到Supabase"""
    try:
        symbol = price_data['symbol']
        
        # 準備更新數據
        update_data = {
            'price': price_data['price'],
            'open_price': price_data['open_price'],
            'high_price': price_data['high_price'],
            'low_price': price_data['low_price'],
            'volume': price_data['volume'],
            'change_amount': price_data['change_amount'],
            'change_percent': price_data['change_percent'],
            'previous_close': price_data['previous_close'],
            'price_date': price_data['price_date'],
            'updated_at': price_data['updated_at']
        }
        
        # 如果有市值數據，也更新市值
        if price_data['market_cap']:
            update_data['market_cap'] = price_data['market_cap']
        
        # 更新數據庫
        result = supabase.table('us_stocks').update(update_data).eq('symbol', symbol).eq('is_etf', True).execute()
        
        if result.data:
            print(f"✅ 數據庫更新成功: {symbol}")
            return True
        else:
            print(f"⚠️ 數據庫更新沒有返回數據: {symbol}")
            return False
            
    except Exception as e:
        print(f"❌ 數據庫更新失敗 {symbol}: {str(e)}")
        return False

def get_etf_list_from_supabase(supabase: Client, limit=50):
    """從Supabase獲取ETF列表"""
    try:
        print("📊 從數據庫獲取ETF列表...")
        
        result = supabase.table('us_stocks').select('symbol, name').eq('is_etf', True).order('symbol').limit(limit).execute()
        
        if result.data:
            etf_list = [etf['symbol'] for etf in result.data]
            print(f"✅ 獲取到 {len(etf_list)} 個ETF")
            return etf_list
        else:
            print("⚠️ 沒有找到ETF數據")
            return []
            
    except Exception as e:
        print(f"❌ 獲取ETF列表失敗: {str(e)}")
        return []

def batch_update_etfs_yahoo(symbols, supabase: Client):
    """批量更新ETF價格（Yahoo Finance支持批量查詢）"""
    try:
        print(f"📦 批量獲取 {len(symbols)} 個ETF的數據...")
        
        # 使用yfinance批量下載
        symbols_str = ' '.join(symbols)
        data = yf.download(symbols_str, period="2d", interval="1d", group_by='ticker', progress=False)
        
        success_count = 0
        error_count = 0
        
        for symbol in symbols:
            try:
                if len(symbols) == 1:
                    # 單個股票的情況
                    symbol_data = data
                else:
                    # 多個股票的情況
                    symbol_data = data[symbol] if symbol in data.columns.levels[0] else None
                
                if symbol_data is None or symbol_data.empty:
                    print(f"⚠️ 沒有找到 {symbol} 的數據")
                    error_count += 1
                    continue
                
                # 獲取最新數據
                latest = symbol_data.iloc[-1]
                previous = symbol_data.iloc[-2] if len(symbol_data) > 1 else latest
                
                # 計算變化
                current_price = float(latest['Close'])
                previous_close = float(previous['Close'])
                change_amount = current_price - previous_close
                change_percent = (change_amount / previous_close * 100) if previous_close != 0 else 0
                
                # 準備價格數據
                price_data = {
                    'symbol': symbol,
                    'price': round(current_price, 2),
                    'open_price': round(float(latest['Open']), 2),
                    'high_price': round(float(latest['High']), 2),
                    'low_price': round(float(latest['Low']), 2),
                    'volume': int(latest['Volume']) if latest['Volume'] else 0,
                    'change_amount': round(change_amount, 2),
                    'change_percent': round(change_percent, 2),
                    'previous_close': round(previous_close, 2),
                    'price_date': latest.name.strftime('%Y-%m-%d'),
                    'updated_at': datetime.now().isoformat()
                }
                
                print(f"✅ {symbol}: ${price_data['price']} ({price_data['change_percent']:+.2f}%)")
                
                # 更新到數據庫
                if update_etf_price_in_supabase(supabase, price_data):
                    success_count += 1
                else:
                    error_count += 1
                    
            except Exception as e:
                print(f"❌ 處理 {symbol} 時出錯: {str(e)}")
                error_count += 1
        
        return success_count, error_count
        
    except Exception as e:
        print(f"❌ 批量更新失敗: {str(e)}")
        return 0, len(symbols)

def update_etf_prices_yahoo(batch_size=10, max_etfs=50):
    """使用Yahoo Finance批量更新ETF價格"""
    print("🚀 開始使用Yahoo Finance更新ETF價格...")
    print("=" * 60)
    
    try:
        # 創建Supabase客戶端
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        print("✅ Supabase連接成功")
        
        # 獲取ETF列表
        etf_symbols = get_etf_list_from_supabase(supabase, max_etfs)
        
        if not etf_symbols:
            print("❌ 沒有ETF需要更新")
            return False
        
        print(f"\n🎯 準備更新 {len(etf_symbols)} 個ETF的價格")
        print(f"📦 批次大小: {batch_size} 個ETF")
        print(f"🌐 使用Yahoo Finance API (無速率限制)")
        
        total_success = 0
        total_errors = 0
        
        # 分批處理
        for i in range(0, len(etf_symbols), batch_size):
            batch = etf_symbols[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(etf_symbols) + batch_size - 1) // batch_size
            
            print(f"\n📦 處理批次 {batch_num}/{total_batches}: {', '.join(batch)}")
            
            success, errors = batch_update_etfs_yahoo(batch, supabase)
            total_success += success
            total_errors += errors
            
            # 批次間短暫暫停
            if i + batch_size < len(etf_symbols):
                print("⏳ 批次間暫停2秒...")
                time.sleep(2)
        
        # 顯示結果
        print("\n" + "=" * 60)
        print("🎉 ETF價格更新完成！")
        print(f"✅ 成功更新: {total_success} 個ETF")
        print(f"❌ 更新失敗: {total_errors} 個ETF")
        
        if total_success > 0:
            print("\n📊 驗證更新結果...")
            # 驗證更新結果
            verify_result = supabase.table('us_stocks').select('symbol, name, price, change_percent, updated_at').eq('is_etf', True).not_.is_('price', 'null').order('updated_at', desc=True).limit(10).execute()
            
            if verify_result.data:
                print("✅ 最近更新的ETF:")
                for etf in verify_result.data:
                    print(f"   {etf['symbol']}: ${etf['price']} ({etf['change_percent']:+.2f}%) - {etf['updated_at'][:19]}")
        
        return total_success > 0
        
    except Exception as e:
        print(f"❌ 更新過程中發生錯誤: {str(e)}")
        return False

def main():
    """主函數"""
    print("📈 ETF價格更新工具 (Yahoo Finance)")
    print("=" * 60)
    
    print(f"🗄️ Supabase URL: {SUPABASE_URL}")
    print(f"🌐 數據源: Yahoo Finance (yfinance)")
    
    # 詢問用戶更新數量
    try:
        max_etfs = input("\n請輸入要更新的ETF數量 (預設30，最多100): ").strip()
        if not max_etfs:
            max_etfs = 30
        else:
            max_etfs = min(int(max_etfs), 100)
    except ValueError:
        max_etfs = 30
    
    print(f"🎯 將更新 {max_etfs} 個ETF的價格")
    
    # 開始更新
    success = update_etf_prices_yahoo(batch_size=10, max_etfs=max_etfs)
    
    if success:
        print("\n✅ ETF價格更新成功！")
        print("🔍 您現在可以在前端搜索ETF並查看最新價格")
        print("📊 可以查看 us_etf_view 視圖獲取ETF數據")
        return True
    else:
        print("\n❌ ETF價格更新失敗")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 用戶中斷程序")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序執行失敗: {str(e)}")
        sys.exit(1)
