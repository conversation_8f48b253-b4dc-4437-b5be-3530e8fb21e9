-- =====================================================
-- 完整修正腳本：建立函數 + 更新匯率為 29.925
-- 即期買入：29.900，即期賣出：29.950，中間價：29.925
-- =====================================================

-- 1. 先建立即期中間價計算函數
CREATE OR REPLACE FUNCTION get_spot_mid_rate(
    target_currency VARCHAR(3) DEFAULT 'USD'
)
RETURNS DECIMAL(8,3) AS $$
DECLARE
    mid_rate DECIMAL(8,3);
BEGIN
    SELECT (spot_buy + spot_sell) / 2.0 INTO mid_rate
    FROM exchange_rates
    WHERE currency = target_currency
    ORDER BY date DESC
    LIMIT 1;
    
    -- 如果沒有資料，返回預設中間價
    IF mid_rate IS NULL THEN
        mid_rate := 29.925; -- 2025-06-01 即期中間價
    END IF;
    
    RETURN mid_rate;
END;
$$ LANGUAGE plpgsql;

-- 2. 更新 2025-06-01 的匯率資料
UPDATE exchange_rates 
SET 
    spot_buy = 29.900,
    spot_sell = 29.950,
    updated_at = NOW()
WHERE date = '2025-06-01' AND currency = 'USD';

-- 3. 如果沒有 2025-06-01 的資料，則插入
INSERT INTO exchange_rates (date, currency, cash_buy, cash_sell, spot_buy, spot_sell) 
VALUES ('2025-06-01', 'USD', 29.800, 30.050, 29.900, 29.950)
ON CONFLICT (date, currency) DO UPDATE SET
    cash_buy = EXCLUDED.cash_buy,
    cash_sell = EXCLUDED.cash_sell,
    spot_buy = EXCLUDED.spot_buy,
    spot_sell = EXCLUDED.spot_sell,
    updated_at = NOW();

-- 4. 驗證更新結果
SELECT 
    '✅ 匯率驗證' as check_type,
    date,
    spot_buy,
    spot_sell,
    (spot_buy + spot_sell) / 2.0 as calculated_mid_rate
FROM exchange_rates 
WHERE date = '2025-06-01' AND currency = 'USD';

-- 5. 測試即期中間價函數
SELECT 
    '✅ 中間價測試' as test_type,
    get_spot_mid_rate('USD') as spot_mid_rate;

-- 6. 確認結果應該是 29.925
SELECT 
    '🎯 預期結果' as info,
    '29.925' as expected_mid_rate,
    get_spot_mid_rate('USD') as actual_mid_rate,
    CASE 
        WHEN get_spot_mid_rate('USD') = 29.925 THEN '✅ 正確'
        ELSE '❌ 需要修正'
    END as status;

-- 7. 檢查所有匯率資料
SELECT 
    '📊 所有匯率資料' as info,
    date,
    spot_buy,
    spot_sell,
    (spot_buy + spot_sell) / 2.0 as mid_rate
FROM exchange_rates 
WHERE currency = 'USD'
ORDER BY date DESC;

-- 8. 測試轉換功能
SELECT 
    '💱 轉換測試' as test_type,
    100.00 as usd_amount,
    100.00 * get_spot_mid_rate('USD') as twd_amount,
    get_spot_mid_rate('USD') as exchange_rate_used;

-- =====================================================
-- 完成訊息
-- =====================================================

SELECT 
    '🎉 修正完成！' as status,
    '即期中間價應為 29.925' as note,
    NOW() as completion_time;
