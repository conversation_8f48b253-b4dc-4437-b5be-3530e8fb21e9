# 📅 月曆收支總額顯示功能

## 🎯 功能概述

在記帳區的月曆中，每個日期下方會顯示當日的收支總額：

- **有收入/支出的日期**：顯示 `+總額` 或 `-總額`
- **沒有交易的日期**：下方保持空白
- **收入大於支出**：顯示綠色 `+金額`
- **支出大於收入**：顯示紅色 `-金額`

## 📱 視覺效果

```
6 月 2025
週日  週一  週二  週三  週四  週五  週六

1     2     3     4     5     6     7
      -9,050

8     9     10    11    12    13    14

15    16    17    18    19    20    21
+6,314 -776

22    23    24    25    26    27    28
            +1,252      -786

29    30
      -679
```

## 🔧 技術實現

### 1. 自定義日期組件

使用 `react-native-calendars` 的 `dayComponent` 屬性來自定義每個日期的顯示：

```typescript
const renderDay = (day: any, item: any) => {
  const dateString = day.dateString;
  const dayNumber = day.day;
  const isSelected = dateString === selectedDate;
  const markData = markedDates[dateString];
  const hasTransactions = markData?.hasTransactions || false;
  const netAmount = markData?.netAmount || 0;
  
  return (
    <TouchableOpacity style={[styles.dayContainer, isSelected && styles.selectedDayContainer]}>
      <Text style={[styles.dayText, isSelected && styles.selectedDayText]}>
        {dayNumber}
      </Text>
      {hasTransactions && (
        <Text style={[
          styles.amountText,
          netAmount > 0 ? styles.positiveAmount : styles.negativeAmount,
        ]}>
          {formatNetAmount(netAmount)}
        </Text>
      )}
    </TouchableOpacity>
  );
};
```

### 2. 收支總額計算

```typescript
const getDayTransactionSummary = (date: string) => {
  const dayTransactions = getTransactionsForDate(date);
  const income = dayTransactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);
  const expense = dayTransactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);

  return { income, expense, count: dayTransactions.length };
};
```

### 3. 金額格式化

```typescript
const formatNetAmount = (amount: number) => {
  if (amount === 0) return '';
  const absAmount = Math.abs(amount);
  
  // 簡化顯示：超過萬元顯示萬
  let formattedAmount: string;
  if (absAmount >= 10000) {
    const wanAmount = Math.round(absAmount / 1000) / 10;
    formattedAmount = wanAmount % 1 === 0 ? `${Math.round(wanAmount)}萬` : `${wanAmount}萬`;
  } else {
    formattedAmount = absAmount.toLocaleString();
  }
  
  return amount > 0 ? `+${formattedAmount}` : `-${formattedAmount}`;
};
```

## 🧪 測試方法

### 1. 添加測試資料

```typescript
import { addTestTransactions } from '../utils/testCalendarAmounts';

// 添加測試交易
addTestTransactions();
```

### 2. 驗證顯示邏輯

```typescript
import { validateCalendarDisplay } from '../utils/testCalendarAmounts';

// 驗證月曆顯示邏輯
const isValid = validateCalendarDisplay();
```

### 3. 手動測試步驟

1. **啟動應用程式**
   ```bash
   cd FinTranzo
   npx expo start
   ```

2. **進入記帳頁面**
   - 點擊底部導航的「記帳」

3. **添加測試交易**
   - 點擊「+」按鈕添加交易
   - 或使用測試組件添加預設資料

4. **查看月曆效果**
   - 有交易的日期會顯示收支總額
   - 沒有交易的日期保持空白

## 📊 預期顯示效果

### 測試資料範例

| 日期 | 收入 | 支出 | 淨額 | 月曆顯示 | 顏色 |
|------|------|------|------|----------|------|
| 6月2日 | 0 | 9,050 | -9,050 | -9,050 | 紅色 |
| 6月15日 | 6,314 | 0 | +6,314 | +6,314 | 綠色 |
| 6月16日 | 0 | 776 | -776 | -776 | 紅色 |
| 6月25日 | 1,252 | 0 | +1,252 | +1,252 | 綠色 |
| 6月27日 | 0 | 786 | -786 | -786 | 紅色 |
| 6月30日 | 0 | 679 | -679 | -679 | 紅色 |
| 其他日期 | 0 | 0 | 0 | (空白) | 無 |

## 🎨 樣式配置

### 日期容器樣式

```typescript
dayContainer: {
  width: 40,
  height: 55,
  alignItems: 'center',
  justifyContent: 'flex-start',
  borderRadius: 16,
  paddingVertical: 6,
  paddingHorizontal: 2,
},
```

### 金額文字樣式

```typescript
amountText: {
  fontSize: 9,
  fontWeight: '600',
  textAlign: 'center',
  marginTop: 1,
  lineHeight: 11,
},
positiveAmount: {
  color: '#34C759', // 綠色
},
negativeAmount: {
  color: '#FF3B30', // 紅色
},
```

## 🔄 與現有功能的整合

### 1. 交易資料同步

- 當添加、編輯或刪除交易時，月曆會自動更新顯示
- 循環交易也會正確顯示在對應日期

### 2. 日期選擇

- 點擊有金額顯示的日期會選中該日期
- 下方會顯示該日期的詳細交易列表

### 3. 月份切換

- 切換月份時會重新計算並顯示該月的收支總額
- 支援滑動切換和點擊標題選擇月份

## ✅ 功能驗證清單

- [ ] 有交易的日期顯示收支總額
- [ ] 沒有交易的日期保持空白
- [ ] 收入大於支出顯示綠色 +金額
- [ ] 支出大於收入顯示紅色 -金額
- [ ] 金額格式化正確（萬元簡化顯示）
- [ ] 日期選擇功能正常
- [ ] 月份切換功能正常
- [ ] 交易增刪改後月曆自動更新

## 🚀 使用說明

1. **查看月曆**：進入記帳頁面即可看到月曆
2. **理解顏色**：綠色表示收入日，紅色表示支出日
3. **查看詳情**：點擊有金額的日期查看詳細交易
4. **切換月份**：滑動或點擊標題切換月份

這個功能讓用戶能夠快速瀏覽每月的收支情況，一目了然地看到哪些日期有收入或支出，以及大致的金額範圍。
