-- =====================================================
-- 美股資料庫完整設定 (修正版)
-- 解決函數衝突問題
-- 執行日期: 2025-06-01
-- =====================================================

-- =====================================================
-- 1. 清理現有函數 (解決衝突)
-- =====================================================

-- 刪除可能存在的舊函數
DROP FUNCTION IF EXISTS get_us_stock_stats();
DROP FUNCTION IF EXISTS search_us_stocks(TEXT, BOOLEAN, INTEGER);
DROP FUNCTION IF EXISTS get_us_stock_by_symbol(VARCHAR);
DROP FUNCTION IF EXISTS get_popular_us_stocks(INTEGER);
DROP FUNCTION IF EXISTS get_us_stocks_by_sector(VARCHAR, INTEGER);
DROP FUNCTION IF EXISTS update_sync_status(VARCHAR, VARCHAR, INTEGER, INTEGER, INTEGER, INTEGER, TEXT);
DROP FUNCTION IF EXISTS get_sync_status(VARCHAR);
DROP FUNCTION IF EXISTS upsert_us_stock(VARCHAR, VARCHAR, VARCHAR, VARCHAR, DECIMAL, DECIMAL, DECIMAL, DECIMAL, BIGINT, DECIMAL, DECIMAL, DECIMAL, BIGINT, BOOLEAN);
DROP FUNCTION IF EXISTS check_stock_data_quality();

-- =====================================================
-- 2. 美股資料表 (如果不存在則創建)
-- =====================================================

-- 建立美股資料表
CREATE TABLE IF NOT EXISTS us_stocks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL UNIQUE,
    name VARCHAR(200) NOT NULL,
    sector VARCHAR(100),
    industry VARCHAR(100),
    market_cap BIGINT,
    price DECIMAL(10,2),
    open_price DECIMAL(10,2),
    high_price DECIMAL(10,2),
    low_price DECIMAL(10,2),
    volume BIGINT,
    change_amount DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    previous_close DECIMAL(10,2),
    price_date DATE DEFAULT CURRENT_DATE,
    is_sp500 BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 美股索引 (如果不存在則創建)
CREATE INDEX IF NOT EXISTS idx_us_stocks_symbol ON us_stocks(symbol);
CREATE INDEX IF NOT EXISTS idx_us_stocks_name ON us_stocks(name);
CREATE INDEX IF NOT EXISTS idx_us_stocks_sector ON us_stocks(sector);
CREATE INDEX IF NOT EXISTS idx_us_stocks_price_date ON us_stocks(price_date DESC);
CREATE INDEX IF NOT EXISTS idx_us_stocks_symbol_search ON us_stocks(symbol varchar_pattern_ops);
CREATE INDEX IF NOT EXISTS idx_us_stocks_name_search ON us_stocks(name varchar_pattern_ops);
CREATE INDEX IF NOT EXISTS idx_us_stocks_sp500 ON us_stocks(is_sp500);
CREATE INDEX IF NOT EXISTS idx_us_stocks_market_cap ON us_stocks(market_cap DESC);
CREATE INDEX IF NOT EXISTS idx_us_stocks_price_not_null ON us_stocks(price) WHERE price IS NOT NULL;

-- =====================================================
-- 3. 同步狀態追蹤表 (如果不存在則創建)
-- =====================================================

-- 創建同步狀態表
CREATE TABLE IF NOT EXISTS sync_status (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    sync_type VARCHAR(50) NOT NULL UNIQUE,
    total_items INTEGER DEFAULT 0,
    completed_items INTEGER DEFAULT 0,
    failed_items INTEGER DEFAULT 0,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'pending', -- pending, running, completed, failed
    api_requests_used INTEGER DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 同步狀態索引
CREATE INDEX IF NOT EXISTS idx_sync_status_type ON sync_status(sync_type);
CREATE INDEX IF NOT EXISTS idx_sync_status_status ON sync_status(status);
CREATE INDEX IF NOT EXISTS idx_sync_status_last_sync ON sync_status(last_sync_at DESC);

-- 插入美股同步狀態記錄
INSERT INTO sync_status (sync_type, status) 
VALUES ('us_stocks', 'pending')
ON CONFLICT (sync_type) DO NOTHING;

-- =====================================================
-- 4. 更新時間觸發器
-- =====================================================

-- 美股更新時間觸發器
CREATE OR REPLACE FUNCTION update_us_stocks_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_us_stocks_updated_at ON us_stocks;
CREATE TRIGGER trigger_update_us_stocks_updated_at
    BEFORE UPDATE ON us_stocks
    FOR EACH ROW
    EXECUTE FUNCTION update_us_stocks_updated_at();

-- 同步狀態更新觸發器
CREATE OR REPLACE FUNCTION update_sync_status_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_sync_status_updated_at ON sync_status;
CREATE TRIGGER trigger_update_sync_status_updated_at
    BEFORE UPDATE ON sync_status
    FOR EACH ROW
    EXECUTE FUNCTION update_sync_status_updated_at();

-- =====================================================
-- 5. 核心查詢函數 (重新創建)
-- =====================================================

-- 搜尋美股函數
CREATE FUNCTION search_us_stocks(
    search_term TEXT,
    sp500_only BOOLEAN DEFAULT true,
    limit_count INTEGER DEFAULT 50
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    sector VARCHAR(100),
    price DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.sector, s.price, s.change_percent, s.market_cap
    FROM us_stocks s
    WHERE 
        (sp500_only = false OR s.is_sp500 = true)
        AND (s.symbol ILIKE '%' || search_term || '%' OR s.name ILIKE '%' || search_term || '%')
        AND s.price IS NOT NULL  -- 只返回有價格的股票
    ORDER BY 
        CASE 
            WHEN s.symbol = UPPER(search_term) THEN 1  -- 完全匹配代號優先
            WHEN s.symbol ILIKE search_term || '%' THEN 2  -- 代號開頭匹配次優先
            ELSE 3
        END,
        s.market_cap DESC NULLS LAST
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 獲取美股統計函數 (新版本)
CREATE FUNCTION get_us_stock_stats()
RETURNS TABLE (
    total_stocks BIGINT,
    sp500_count BIGINT,
    stocks_with_prices BIGINT,
    sectors_count BIGINT,
    last_updated DATE,
    avg_price DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_stocks,
        COUNT(*) FILTER (WHERE is_sp500 = true) as sp500_count,
        COUNT(*) FILTER (WHERE price IS NOT NULL) as stocks_with_prices,
        COUNT(DISTINCT sector) as sectors_count,
        MAX(price_date) as last_updated,
        AVG(price) as avg_price
    FROM us_stocks;
END;
$$ LANGUAGE plpgsql;

-- 獲取指定股票資訊
CREATE FUNCTION get_us_stock_by_symbol(
    stock_symbol VARCHAR(10)
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    sector VARCHAR(100),
    industry VARCHAR(100),
    price DECIMAL(10,2),
    open_price DECIMAL(10,2),
    high_price DECIMAL(10,2),
    low_price DECIMAL(10,2),
    volume BIGINT,
    change_amount DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT,
    price_date DATE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.sector, s.industry, s.price, s.open_price,
        s.high_price, s.low_price, s.volume, s.change_amount, 
        s.change_percent, s.market_cap, s.price_date, s.updated_at
    FROM us_stocks s
    WHERE s.symbol = stock_symbol;
END;
$$ LANGUAGE plpgsql;

-- 獲取熱門股票
CREATE FUNCTION get_popular_us_stocks(
    limit_count INTEGER DEFAULT 20
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    sector VARCHAR(100),
    price DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.sector, s.price, s.change_percent, s.market_cap
    FROM us_stocks s
    WHERE s.is_sp500 = true AND s.price IS NOT NULL
    ORDER BY s.market_cap DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 按行業分類獲取股票
CREATE FUNCTION get_us_stocks_by_sector(
    target_sector VARCHAR(100),
    limit_count INTEGER DEFAULT 20
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    price DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.price, s.change_percent, s.market_cap
    FROM us_stocks s
    WHERE s.sector = target_sector AND s.price IS NOT NULL
    ORDER BY s.market_cap DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;
