/**
 * 立即開始 S&P 500 股票同步
 * 使用方法 1：完整流程
 * 
 * 執行方式：
 * node scripts/startSP500Sync.js
 */

console.log('🚀 S&P 500 股票同步啟動器');
console.log('=====================================');
console.log('📊 目標：500 檔股票');
console.log('🔑 API Key: QJTK95T7SA1661WM');
console.log('⚠️ Alpha Vantage 限制：每分鐘 5 次，每日 500 次');
console.log('💡 只獲取收盤價，避免被封鎖');
console.log('=====================================\n');

// 動態導入模組
async function startSync() {
  try {
    console.log('📦 載入同步模組...');
    
    // 在 Node.js 環境中，需要使用動態導入
    const { startFullSyncProcess } = await import('../src/utils/executeSP500FullSync.js');
    
    console.log('✅ 模組載入成功');
    console.log('🚀 開始執行同步...\n');
    
    // 執行完整同步流程
    const success = await startFullSyncProcess();
    
    if (success) {
      console.log('\n🎉 同步流程執行完成！');
      console.log('💡 請檢查上方日誌確認同步狀態');
    } else {
      console.log('\n❌ 同步流程執行失敗');
      console.log('💡 請檢查錯誤訊息並重試');
    }
    
  } catch (error) {
    console.error('❌ 啟動同步失敗:', error);
    console.log('\n💡 請確保：');
    console.log('   1. 已安裝所有依賴 (npm install)');
    console.log('   2. Supabase 配置正確');
    console.log('   3. 網路連接正常');
    console.log('   4. Alpha Vantage API Key 有效');
  }
}

// 顯示使用說明
console.log('📋 使用說明：');
console.log('1. 此腳本將自動檢查現有資料');
console.log('2. 如果資料不完整，將開始同步');
console.log('3. 同步過程中會顯示即時進度');
console.log('4. 完成後會自動測試搜尋功能');
console.log('5. 可以隨時按 Ctrl+C 中斷\n');

console.log('⏱️ 預計時間：');
console.log('   - 檢查現有資料: 1 分鐘');
console.log('   - 完整同步: 100 分鐘 (500 檔股票)');
console.log('   - 測試驗證: 2 分鐘\n');

console.log('🔄 開始執行...\n');

// 執行同步
startSync().then(() => {
  console.log('\n📝 腳本執行完成');
  process.exit(0);
}).catch(error => {
  console.error('\n❌ 腳本執行失敗:', error);
  process.exit(1);
});
