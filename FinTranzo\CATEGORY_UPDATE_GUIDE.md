# 支出類別更新說明

## 🎯 更新內容

### 舊版本類別布局：
```
第一行：餐飲 交通 購物 娛樂
第二行：利息 販售 還款 其他
```

### 新版本類別布局：
```
第一行：餐飲 交通 購物 娛樂 禮品
第二行：學習 旅行 醫療 保險 還款  
第三行：家居 家庭 紅包 其他
```

## 📱 新增類別詳情

### 第一行新增：
- **禮品** 🎁 - `gift-outline` (紫色 #9966FF)
  - 用途：生日禮物、節日禮品、紀念品等

### 第二行新增：
- **學習** 📚 - `school-outline` (橙色 #FF9F40)
  - 用途：課程費用、書籍、培訓、教育支出
- **旅行** ✈️ - `airplane-outline` (青色 #1ABC9C)
  - 用途：旅遊、出差、機票、住宿
- **醫療** 🏥 - `medical-outline` (紅色 #E74C3C)
  - 用途：看病、藥品、健康檢查、醫療保險
- **保險** 🛡️ - `shield-outline` (藍色 #3498DB)
  - 用途：各種保險費用、保費支出

### 第三行新增：
- **家居** 🏠 - `home-outline` (橙色 #F39C12)
  - 用途：家具、裝修、家電、居家用品
- **家庭** 👨‍👩‍👧‍👦 - `people-outline` (紫色 #9B59B6)
  - 用途：家庭相關支出、親子活動
- **紅包** 💰 - `wallet-outline` (橙色 #E67E22)
  - 用途：婚禮紅包、節日紅包、禮金

## 🔧 技術實現

### 數據結構更新：
- 支出類別從 8 個增加到 14 個
- 收入類別 ID 調整為 15-21
- 保持數據完整性和向後兼容

### 動態類別加載：
- 修改 `AddTransactionModal.tsx` 從硬編碼改為動態獲取
- 從 `transactionDataService` 實時獲取類別列表
- 支援類別的實時更新和同步

### 本地存儲兼容：
- 新用戶自動獲得新的類別設定
- 現有用戶需要清除本地存儲以使用新類別
- 提供清除工具 `clear_storage.js`

## 🚀 使用方式

### 新用戶：
1. 直接使用應用程式
2. 自動獲得新的 14 個支出類別
3. 享受更豐富的分類選項

### 現有用戶：
1. 如果類別沒有更新，需要清除本地存儲
2. 運行清除腳本：`node clear_storage.js`
3. 重新啟動應用程式
4. 系統會自動初始化新的類別設定

## 📊 類別布局設計

### 分類邏輯：
- **第一行**：日常基本開支（餐飲、交通、購物、娛樂、禮品）
- **第二行**：特定用途支出（學習、旅行、醫療、保險、還款）
- **第三行**：家庭相關支出（家居、家庭、紅包、其他）

### UI 設計：
- **3行布局**：更好地組織類別
- **5-5-4分布**：第一行5個，第二行5個，第三行4個
- **色彩搭配**：每個類別都有獨特的顏色識別
- **圖標選擇**：使用直觀的 Ionicons 圖標

## 🎉 用戶體驗提升

### 更豐富的分類：
- ✅ **更細緻的分類** - 14個支出類別涵蓋更多場景
- ✅ **更直觀的圖標** - 每個類別都有對應的視覺標識
- ✅ **更好的組織** - 3行布局更清晰易用
- ✅ **更完整的覆蓋** - 涵蓋現代生活的各種支出需求

### 實用性增強：
- 🎁 **禮品** - 節日送禮、生日禮物
- 📚 **學習** - 自我提升、教育投資
- ✈️ **旅行** - 休閒娛樂、商務出差
- 🏥 **醫療** - 健康管理、醫療支出
- 🛡️ **保險** - 風險保障、保險費用
- 🏠 **家居** - 居家改善、生活品質
- 👨‍👩‍👧‍👦 **家庭** - 親子活動、家庭支出
- 💰 **紅包** - 社交禮儀、人情往來

## 🔍 故障排除

### 如果類別沒有更新：
1. 檢查應用程式是否重新啟動
2. 清除瀏覽器緩存（Web版）
3. 運行清除存儲腳本
4. 重新安裝應用程式（最後手段）

### 數據安全：
- 清除本地存儲會刪除所有交易記錄
- 建議在清除前手動備份重要數據
- 未來版本將支援數據導出功能

## 📞 技術支援

如果遇到類別更新相關問題：
1. 查看控制台日誌
2. 檢查本地存儲狀態
3. 嘗試清除數據重新初始化
4. 聯繫開發團隊

---

**更新完成時間：** 2024年12月
**版本：** v1.1.0
**狀態：** ✅ 已完成並測試
