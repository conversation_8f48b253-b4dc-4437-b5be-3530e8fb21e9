# ⚡ 快速部署指南 - 5分鐘完成設置

## 🎯 目標
在 5 分鐘內完成台股、美股、匯率每日自動更新的部署。

## 📋 準備清單
- ✅ GitHub 帳號
- ✅ Supabase 項目（您已有）
- ✅ 部署文件已準備完成

---

## 🚀 步驟1：註冊 Vercel（1分鐘）

1. **前往 Vercel**：
   - 打開 [vercel.com](https://vercel.com)
   - 點擊 **"Sign Up"**

2. **使用 GitHub 登入**：
   - 選擇 **"Continue with GitHub"**
   - 授權 Vercel 訪問您的 GitHub

---

## 🔗 步驟2：連接項目（1分鐘）

1. **推送代碼到 GitHub**：
   ```bash
   git add .
   git commit -m "Add auto-update deployment files"
   git push origin main
   ```

2. **在 Vercel 創建項目**：
   - 點擊 **"New Project"**
   - 選擇您的 **FinTranzo** 倉庫
   - 點擊 **"Import"**

---

## ⚙️ 步驟3：設定環境變數（2分鐘）

在 Vercel 項目設置中添加環境變數：

### 必需變數：
```
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here
```

### 可選變數（提升數據質量）：
```
ALPHA_VANTAGE_API_KEY=your-alpha-vantage-key
FINMIND_TOKEN=your-finmind-token
CRON_SECRET=your-secret-123
```

**如何添加**：
1. 進入項目 → **Settings** → **Environment Variables**
2. 點擊 **"Add"**
3. 輸入變數名稱和值
4. 點擊 **"Save"**

---

## 🚀 步驟4：部署（1分鐘）

1. **開始部署**：
   - 點擊 **"Deploy"**
   - 等待部署完成（約30秒）

2. **獲得 URL**：
   - 部署成功後會顯示項目 URL
   - 例如：`https://fintranzo.vercel.app`

---

## ✅ 步驟5：測試（30秒）

**手動測試 API**：
```bash
# 替換為您的實際 URL
curl https://your-app.vercel.app/api/update-exchange-rates
```

**預期結果**：
```json
{
  "message": "匯率更新成功",
  "timestamp": "2024-12-19T...",
  "result": {
    "success": true,
    "rate": 31.5,
    "source": "exchangerate-api"
  }
}
```

---

## 📅 自動排程（已配置）

部署完成後，以下更新會自動執行：

| 功能 | 時間 | 頻率 |
|------|------|------|
| 🇹🇼 **台股更新** | 下午 3:00 | 工作日 |
| 🇺🇸 **美股更新** | 晚上 10:00 | 工作日 |
| 💱 **匯率更新** | 早上 9:00 | 工作日 |

---

## 🔍 監控和檢查

### 查看執行日誌：
1. Vercel 控制台 → 您的項目
2. **Functions** 標籤
3. 點擊任一函數查看日誌

### 檢查資料庫：
1. 登入 Supabase
2. 查看相關表格的 `updated_at` 欄位
3. 確認數據有定期更新

---

## 🆓 免費額度

**Vercel 免費方案足夠使用**：
- ✅ 100 次函數執行/天
- ✅ 每日只需 3 次執行
- ✅ 完全免費

**API 免費額度**：
- ✅ 匯率 API：完全免費
- ✅ Alpha Vantage：500 次/天（免費）
- ✅ FinMind：有免費額度

---

## 🔧 故障排除

### 常見問題：

**1. 環境變數錯誤**
```
錯誤：Missing Supabase environment variables
解決：檢查 SUPABASE_URL 和 SUPABASE_ANON_KEY
```

**2. 函數超時**
```
錯誤：Function timeout
解決：檢查網路連接，API 可能暫時不可用
```

**3. 資料庫連接失敗**
```
錯誤：Database connection failed
解決：檢查 Supabase 項目狀態和金鑰
```

---

## 🎉 完成！

恭喜！您已成功部署每日自動更新系統：

- ✅ **台股價格**：每日自動更新
- ✅ **美股價格**：每日自動更新  
- ✅ **匯率數據**：每日自動更新
- ✅ **完全免費**：使用免費方案
- ✅ **自動執行**：無需手動干預

---

## 📞 需要幫助？

如果遇到問題：

1. **檢查 Vercel 日誌**：Functions → 選擇函數 → 查看日誌
2. **驗證環境變數**：Settings → Environment Variables
3. **測試 API**：手動調用 API 端點
4. **檢查 Supabase**：確認資料庫連接正常

---

## 🔄 下次更新

當您需要修改更新邏輯時：
1. 修改 `api/` 目錄下的文件
2. 推送到 GitHub
3. Vercel 會自動重新部署

**就是這麼簡單！** 🎉
