-- =====================================================
-- Supabase Edge Function 每日股票更新
-- 在伺服器端執行，不受客戶端限制
-- =====================================================

-- 1. 創建 Edge Function (需要在 Supabase Dashboard 中創建)
/*
創建 Edge Function: daily-stock-update

// supabase/functions/daily-stock-update/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    )

    // 多 API 配置
    const APIs = [
      {
        name: 'Alpha Vantage',
        key: Deno.env.get('ALPHA_VANTAGE_KEY'),
        getPrice: async (symbol: string) => {
          const response = await fetch(
            `https://www.alphavantage.co/query?function=GLOBAL_QUOTE&symbol=${symbol}&apikey=${APIs[0].key}`
          )
          const data = await response.json()
          return data['Global Quote']?['05. price'] ? parseFloat(data['Global Quote']['05. price']) : null
        }
      },
      {
        name: 'Yahoo Finance',
        getPrice: async (symbol: string) => {
          const response = await fetch(
            `https://query1.finance.yahoo.com/v8/finance/chart/${symbol}`
          )
          const data = await response.json()
          return data.chart?.result?.[0]?.meta?.regularMarketPrice || null
        }
      }
    ]

    // 重點股票清單
    const stocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'V', 'MA', 'JPM']
    
    const results = []
    
    for (const symbol of stocks) {
      let price = null
      let source = 'none'
      
      // 嘗試多個 API
      for (const api of APIs) {
        try {
          price = await api.getPrice(symbol)
          if (price && price > 0) {
            source = api.name
            break
          }
        } catch (error) {
          console.log(`${api.name} failed for ${symbol}:`, error)
        }
      }
      
      if (price) {
        // 更新到資料庫
        const { error } = await supabaseClient.rpc('upsert_us_stock', {
          stock_symbol: symbol,
          stock_name: getCompanyName(symbol),
          stock_sector: getSector(symbol),
          stock_price: price,
          is_sp500_stock: true
        })
        
        if (!error) {
          results.push({ symbol, price, source, status: 'success' })
        } else {
          results.push({ symbol, price, source, status: 'db_error', error })
        }
      } else {
        results.push({ symbol, status: 'no_price' })
      }
      
      // 避免過快請求
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        updated: new Date().toISOString(),
        results 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400
      }
    )
  }
})

function getCompanyName(symbol: string): string {
  const names: { [key: string]: string } = {
    'AAPL': 'Apple Inc.',
    'MSFT': 'Microsoft Corporation',
    'V': 'Visa Inc.',
    'GOOGL': 'Alphabet Inc. Class A',
    'AMZN': 'Amazon.com Inc.',
    'TSLA': 'Tesla Inc.',
    'META': 'Meta Platforms Inc.',
    'NVDA': 'NVIDIA Corporation',
    'MA': 'Mastercard Incorporated',
    'JPM': 'JPMorgan Chase & Co.'
  }
  return names[symbol] || `${symbol} Corporation`
}

function getSector(symbol: string): string {
  const sectors: { [key: string]: string } = {
    'AAPL': 'Technology',
    'MSFT': 'Technology',
    'V': 'Financials',
    'GOOGL': 'Communication Services',
    'AMZN': 'Consumer Discretionary',
    'TSLA': 'Consumer Discretionary',
    'META': 'Communication Services',
    'NVDA': 'Technology',
    'MA': 'Financials',
    'JPM': 'Financials'
  }
  return sectors[symbol] || 'Unknown'
}
*/

-- 2. 創建定時觸發器 (使用 pg_cron 擴展)
-- 需要在 Supabase Dashboard 中啟用 pg_cron 擴展

-- 啟用 pg_cron 擴展
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- 創建每日更新任務 (美東時間下午5點執行)
SELECT cron.schedule(
    'daily-stock-update',
    '0 17 * * 1-5', -- 週一到週五下午5點 (美東時間)
    $$
    SELECT net.http_post(
        url := 'https://your-project.supabase.co/functions/v1/daily-stock-update',
        headers := '{"Authorization": "Bearer YOUR_ANON_KEY", "Content-Type": "application/json"}'::jsonb,
        body := '{}'::jsonb
    );
    $$
);

-- 3. 創建更新日誌表
CREATE TABLE IF NOT EXISTS stock_update_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    update_date DATE DEFAULT CURRENT_DATE,
    total_stocks INTEGER,
    successful_updates INTEGER,
    failed_updates INTEGER,
    api_sources JSONB,
    execution_time_seconds INTEGER,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 創建手動觸發更新的函數
CREATE OR REPLACE FUNCTION trigger_manual_stock_update()
RETURNS JSONB AS $$
DECLARE
    result JSONB;
BEGIN
    -- 調用 Edge Function
    SELECT net.http_post(
        url := 'https://your-project.supabase.co/functions/v1/daily-stock-update',
        headers := '{"Authorization": "Bearer YOUR_ANON_KEY", "Content-Type": "application/json"}'::jsonb,
        body := '{"manual": true}'::jsonb
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 5. 創建檢查更新狀態的函數
CREATE OR REPLACE FUNCTION get_stock_update_status()
RETURNS TABLE (
    last_update_date DATE,
    total_stocks INTEGER,
    successful_updates INTEGER,
    failed_updates INTEGER,
    success_rate DECIMAL(5,2),
    hours_since_update INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        l.update_date,
        l.total_stocks,
        l.successful_updates,
        l.failed_updates,
        CASE 
            WHEN l.total_stocks > 0 THEN 
                ROUND((l.successful_updates::DECIMAL / l.total_stocks) * 100, 2)
            ELSE 0
        END as success_rate,
        EXTRACT(EPOCH FROM (NOW() - l.created_at))::INTEGER / 3600 as hours_since_update
    FROM stock_update_logs l
    ORDER BY l.created_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- 6. 創建 RLS 政策
ALTER TABLE stock_update_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow public read access" ON stock_update_logs
    FOR SELECT USING (true);

CREATE POLICY "Allow service role write access" ON stock_update_logs
    FOR ALL USING (auth.role() = 'service_role');

-- 7. 顯示設定完成訊息
SELECT 
    '🎉 每日股票更新系統設定完成！' as status,
    '⏰ 每日美東時間下午5點自動更新' as schedule,
    '🔧 可使用 trigger_manual_stock_update() 手動觸發' as manual_trigger,
    '📊 可使用 get_stock_update_status() 檢查狀態' as status_check,
    NOW() as setup_time;
