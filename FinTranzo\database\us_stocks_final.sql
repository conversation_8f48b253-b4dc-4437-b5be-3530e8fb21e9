-- =====================================================
-- 美股資料庫完整設定 (最終版)
-- API → Supabase → 用戶 架構
-- 不包含中文搜尋，專注核心功能
-- 執行日期: 2025-06-01
-- =====================================================

-- =====================================================
-- 1. 清理現有資料 (可選)
-- =====================================================

-- 如果需要重新開始，取消註解以下行
-- DROP TABLE IF EXISTS us_stocks CASCADE;
-- DROP TABLE IF EXISTS sync_status CASCADE;

-- =====================================================
-- 2. 美股資料表
-- =====================================================

-- 建立美股資料表
CREATE TABLE IF NOT EXISTS us_stocks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL UNIQUE,
    name VARCHAR(200) NOT NULL,
    sector VARCHAR(100),
    industry VARCHAR(100),
    market_cap BIGINT,
    price DECIMAL(10,2),
    open_price DECIMAL(10,2),
    high_price DECIMAL(10,2),
    low_price DECIMAL(10,2),
    volume BIGINT,
    change_amount DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    previous_close DECIMAL(10,2),
    price_date DATE DEFAULT CURRENT_DATE,
    is_sp500 BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 美股索引 (優化查詢效能)
CREATE INDEX IF NOT EXISTS idx_us_stocks_symbol ON us_stocks(symbol);
CREATE INDEX IF NOT EXISTS idx_us_stocks_name ON us_stocks(name);
CREATE INDEX IF NOT EXISTS idx_us_stocks_sector ON us_stocks(sector);
CREATE INDEX IF NOT EXISTS idx_us_stocks_price_date ON us_stocks(price_date DESC);
CREATE INDEX IF NOT EXISTS idx_us_stocks_symbol_search ON us_stocks(symbol varchar_pattern_ops);
CREATE INDEX IF NOT EXISTS idx_us_stocks_name_search ON us_stocks(name varchar_pattern_ops);
CREATE INDEX IF NOT EXISTS idx_us_stocks_sp500 ON us_stocks(is_sp500);
CREATE INDEX IF NOT EXISTS idx_us_stocks_market_cap ON us_stocks(market_cap DESC);
CREATE INDEX IF NOT EXISTS idx_us_stocks_price_not_null ON us_stocks(price) WHERE price IS NOT NULL;

-- =====================================================
-- 3. 同步狀態追蹤表
-- =====================================================

-- 創建同步狀態表
CREATE TABLE IF NOT EXISTS sync_status (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    sync_type VARCHAR(50) NOT NULL UNIQUE,
    total_items INTEGER DEFAULT 0,
    completed_items INTEGER DEFAULT 0,
    failed_items INTEGER DEFAULT 0,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'pending', -- pending, running, completed, failed
    api_requests_used INTEGER DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 同步狀態索引
CREATE INDEX IF NOT EXISTS idx_sync_status_type ON sync_status(sync_type);
CREATE INDEX IF NOT EXISTS idx_sync_status_status ON sync_status(status);
CREATE INDEX IF NOT EXISTS idx_sync_status_last_sync ON sync_status(last_sync_at DESC);

-- 插入美股同步狀態記錄
INSERT INTO sync_status (sync_type, status) 
VALUES ('us_stocks', 'pending')
ON CONFLICT (sync_type) DO NOTHING;

-- =====================================================
-- 4. 更新時間觸發器
-- =====================================================

-- 美股更新時間觸發器
CREATE OR REPLACE FUNCTION update_us_stocks_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_us_stocks_updated_at ON us_stocks;
CREATE TRIGGER trigger_update_us_stocks_updated_at
    BEFORE UPDATE ON us_stocks
    FOR EACH ROW
    EXECUTE FUNCTION update_us_stocks_updated_at();

-- 同步狀態更新觸發器
CREATE OR REPLACE FUNCTION update_sync_status_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_sync_status_updated_at ON sync_status;
CREATE TRIGGER trigger_update_sync_status_updated_at
    BEFORE UPDATE ON sync_status
    FOR EACH ROW
    EXECUTE FUNCTION update_sync_status_updated_at();

-- =====================================================
-- 5. 核心查詢函數
-- =====================================================

-- 搜尋美股函數 (優化版，只返回有價格的股票)
CREATE OR REPLACE FUNCTION search_us_stocks(
    search_term TEXT,
    sp500_only BOOLEAN DEFAULT true,
    limit_count INTEGER DEFAULT 50
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    sector VARCHAR(100),
    price DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.sector, s.price, s.change_percent, s.market_cap
    FROM us_stocks s
    WHERE 
        (sp500_only = false OR s.is_sp500 = true)
        AND (s.symbol ILIKE '%' || search_term || '%' OR s.name ILIKE '%' || search_term || '%')
        AND s.price IS NOT NULL  -- 只返回有價格的股票
    ORDER BY 
        CASE 
            WHEN s.symbol = UPPER(search_term) THEN 1  -- 完全匹配代號優先
            WHEN s.symbol ILIKE search_term || '%' THEN 2  -- 代號開頭匹配次優先
            ELSE 3
        END,
        s.market_cap DESC NULLS LAST
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 獲取美股統計函數
CREATE OR REPLACE FUNCTION get_us_stock_stats()
RETURNS TABLE (
    total_stocks BIGINT,
    sp500_count BIGINT,
    stocks_with_prices BIGINT,
    sectors_count BIGINT,
    last_updated DATE,
    avg_price DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_stocks,
        COUNT(*) FILTER (WHERE is_sp500 = true) as sp500_count,
        COUNT(*) FILTER (WHERE price IS NOT NULL) as stocks_with_prices,
        COUNT(DISTINCT sector) as sectors_count,
        MAX(price_date) as last_updated,
        AVG(price) as avg_price
    FROM us_stocks;
END;
$$ LANGUAGE plpgsql;

-- 獲取指定股票資訊
CREATE OR REPLACE FUNCTION get_us_stock_by_symbol(
    stock_symbol VARCHAR(10)
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    sector VARCHAR(100),
    industry VARCHAR(100),
    price DECIMAL(10,2),
    open_price DECIMAL(10,2),
    high_price DECIMAL(10,2),
    low_price DECIMAL(10,2),
    volume BIGINT,
    change_amount DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT,
    price_date DATE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.sector, s.industry, s.price, s.open_price,
        s.high_price, s.low_price, s.volume, s.change_amount, 
        s.change_percent, s.market_cap, s.price_date, s.updated_at
    FROM us_stocks s
    WHERE s.symbol = stock_symbol;
END;
$$ LANGUAGE plpgsql;

-- 獲取熱門股票 (按市值排序)
CREATE OR REPLACE FUNCTION get_popular_us_stocks(
    limit_count INTEGER DEFAULT 20
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    sector VARCHAR(100),
    price DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.sector, s.price, s.change_percent, s.market_cap
    FROM us_stocks s
    WHERE s.is_sp500 = true AND s.price IS NOT NULL
    ORDER BY s.market_cap DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 按行業分類獲取股票
CREATE OR REPLACE FUNCTION get_us_stocks_by_sector(
    target_sector VARCHAR(100),
    limit_count INTEGER DEFAULT 20
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    price DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.price, s.change_percent, s.market_cap
    FROM us_stocks s
    WHERE s.sector = target_sector AND s.price IS NOT NULL
    ORDER BY s.market_cap DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 6. 同步狀態管理函數
-- =====================================================

-- 更新同步狀態函數
CREATE OR REPLACE FUNCTION update_sync_status(
    p_sync_type VARCHAR(50),
    p_status VARCHAR(20),
    p_total_items INTEGER DEFAULT NULL,
    p_completed_items INTEGER DEFAULT NULL,
    p_failed_items INTEGER DEFAULT NULL,
    p_api_requests_used INTEGER DEFAULT NULL,
    p_error_message TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    UPDATE sync_status
    SET
        status = p_status,
        total_items = COALESCE(p_total_items, total_items),
        completed_items = COALESCE(p_completed_items, completed_items),
        failed_items = COALESCE(p_failed_items, failed_items),
        api_requests_used = COALESCE(p_api_requests_used, api_requests_used),
        error_message = p_error_message,
        last_sync_at = CASE WHEN p_status = 'completed' THEN NOW() ELSE last_sync_at END
    WHERE sync_type = p_sync_type;

    -- 如果記錄不存在，則插入
    IF NOT FOUND THEN
        INSERT INTO sync_status (
            sync_type, status, total_items, completed_items, failed_items,
            api_requests_used, error_message, last_sync_at
        ) VALUES (
            p_sync_type, p_status, p_total_items, p_completed_items, p_failed_items,
            p_api_requests_used, p_error_message,
            CASE WHEN p_status = 'completed' THEN NOW() ELSE NULL END
        );
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 獲取同步狀態函數
CREATE OR REPLACE FUNCTION get_sync_status(p_sync_type VARCHAR(50) DEFAULT 'us_stocks')
RETURNS TABLE (
    sync_type VARCHAR(50),
    status VARCHAR(20),
    total_items INTEGER,
    completed_items INTEGER,
    failed_items INTEGER,
    completion_rate DECIMAL(5,2),
    api_requests_used INTEGER,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        s.sync_type,
        s.status,
        s.total_items,
        s.completed_items,
        s.failed_items,
        CASE
            WHEN s.total_items > 0 THEN ROUND((s.completed_items::DECIMAL / s.total_items) * 100, 2)
            ELSE 0
        END as completion_rate,
        s.api_requests_used,
        s.last_sync_at,
        s.error_message
    FROM sync_status s
    WHERE s.sync_type = p_sync_type;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 7. 資料插入/更新函數
-- =====================================================

-- 插入或更新美股資料函數 (UPSERT)
CREATE OR REPLACE FUNCTION upsert_us_stock(
    stock_symbol VARCHAR(10),
    stock_name VARCHAR(200),
    stock_sector VARCHAR(100) DEFAULT NULL,
    stock_industry VARCHAR(100) DEFAULT NULL,
    stock_price DECIMAL(10,2) DEFAULT NULL,
    stock_open DECIMAL(10,2) DEFAULT NULL,
    stock_high DECIMAL(10,2) DEFAULT NULL,
    stock_low DECIMAL(10,2) DEFAULT NULL,
    stock_volume BIGINT DEFAULT NULL,
    stock_change DECIMAL(10,2) DEFAULT NULL,
    stock_change_percent DECIMAL(5,2) DEFAULT NULL,
    stock_previous_close DECIMAL(10,2) DEFAULT NULL,
    stock_market_cap BIGINT DEFAULT NULL,
    is_sp500_stock BOOLEAN DEFAULT true
)
RETURNS UUID AS $$
DECLARE
    stock_id UUID;
BEGIN
    INSERT INTO us_stocks (
        symbol, name, sector, industry, price, open_price, high_price, low_price,
        volume, change_amount, change_percent, previous_close, market_cap, is_sp500
    ) VALUES (
        stock_symbol, stock_name, stock_sector, stock_industry, stock_price,
        stock_open, stock_high, stock_low, stock_volume, stock_change,
        stock_change_percent, stock_previous_close, stock_market_cap, is_sp500_stock
    )
    ON CONFLICT (symbol)
    DO UPDATE SET
        name = EXCLUDED.name,
        sector = EXCLUDED.sector,
        industry = EXCLUDED.industry,
        price = EXCLUDED.price,
        open_price = EXCLUDED.open_price,
        high_price = EXCLUDED.high_price,
        low_price = EXCLUDED.low_price,
        volume = EXCLUDED.volume,
        change_amount = EXCLUDED.change_amount,
        change_percent = EXCLUDED.change_percent,
        previous_close = EXCLUDED.previous_close,
        market_cap = EXCLUDED.market_cap,
        price_date = CURRENT_DATE,
        updated_at = NOW()
    RETURNING id INTO stock_id;

    RETURN stock_id;
END;
$$ LANGUAGE plpgsql;

-- 資料品質檢查函數
CREATE OR REPLACE FUNCTION check_stock_data_quality()
RETURNS TABLE (
    metric VARCHAR(50),
    value BIGINT,
    description TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        'total_stocks'::VARCHAR(50) as metric,
        COUNT(*) as value,
        '總股票數量'::TEXT as description
    FROM us_stocks

    UNION ALL

    SELECT
        'stocks_with_prices'::VARCHAR(50),
        COUNT(*) as value,
        '有價格的股票數量'::TEXT
    FROM us_stocks
    WHERE price IS NOT NULL

    UNION ALL

    SELECT
        'stocks_without_prices'::VARCHAR(50),
        COUNT(*) as value,
        '沒有價格的股票數量'::TEXT
    FROM us_stocks
    WHERE price IS NULL

    UNION ALL

    SELECT
        'recent_updates'::VARCHAR(50),
        COUNT(*) as value,
        '最近24小時更新的股票'::TEXT
    FROM us_stocks
    WHERE updated_at > NOW() - INTERVAL '24 hours';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 8. 視圖建立
-- =====================================================

-- 最新美股價格視圖
CREATE OR REPLACE VIEW latest_us_stocks AS
SELECT
    symbol,
    name,
    sector,
    industry,
    price,
    change_amount,
    change_percent,
    volume,
    market_cap,
    price_date,
    updated_at
FROM us_stocks
WHERE price IS NOT NULL
AND price_date = (SELECT MAX(price_date) FROM us_stocks);

-- S&P 500 股票視圖
CREATE OR REPLACE VIEW sp500_stocks AS
SELECT
    symbol,
    name,
    sector,
    price,
    change_percent,
    market_cap,
    price_date
FROM us_stocks
WHERE is_sp500 = true AND price IS NOT NULL
ORDER BY market_cap DESC;

-- 同步狀態視圖
CREATE OR REPLACE VIEW sync_status_summary AS
SELECT
    sync_type,
    status,
    total_items,
    completed_items,
    failed_items,
    CASE
        WHEN total_items > 0 THEN ROUND((completed_items::DECIMAL / total_items) * 100, 2)
        ELSE 0
    END as completion_rate,
    api_requests_used,
    last_sync_at,
    CASE
        WHEN last_sync_at IS NULL THEN '從未同步'
        WHEN last_sync_at < NOW() - INTERVAL '24 hours' THEN '需要更新'
        ELSE '狀態良好'
    END as sync_health,
    updated_at
FROM sync_status;

-- =====================================================
-- 9. RLS (Row Level Security) 政策
-- =====================================================

-- 美股表 RLS
ALTER TABLE us_stocks ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Allow public read access" ON us_stocks;
CREATE POLICY "Allow public read access" ON us_stocks
    FOR SELECT USING (true);

DROP POLICY IF EXISTS "Allow service role write access" ON us_stocks;
CREATE POLICY "Allow service role write access" ON us_stocks
    FOR ALL USING (auth.role() = 'service_role');

-- 同步狀態表 RLS
ALTER TABLE sync_status ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Allow public read access" ON sync_status;
CREATE POLICY "Allow public read access" ON sync_status
    FOR SELECT USING (true);

DROP POLICY IF EXISTS "Allow service role write access" ON sync_status;
CREATE POLICY "Allow service role write access" ON sync_status
    FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- 10. 註解說明
-- =====================================================

COMMENT ON TABLE us_stocks IS '美股資料表，儲存 S&P 500 等美股資訊';
COMMENT ON COLUMN us_stocks.symbol IS '股票代號 (如: AAPL)';
COMMENT ON COLUMN us_stocks.name IS '公司名稱';
COMMENT ON COLUMN us_stocks.sector IS '行業分類';
COMMENT ON COLUMN us_stocks.industry IS '細分行業';
COMMENT ON COLUMN us_stocks.price IS '當前股價 (美元)';
COMMENT ON COLUMN us_stocks.market_cap IS '市值';
COMMENT ON COLUMN us_stocks.is_sp500 IS '是否為 S&P 500 成分股';

COMMENT ON TABLE sync_status IS '同步狀態追蹤表';
COMMENT ON COLUMN sync_status.sync_type IS '同步類型 (如: us_stocks)';
COMMENT ON COLUMN sync_status.status IS '同步狀態 (pending, running, completed, failed)';
COMMENT ON COLUMN sync_status.api_requests_used IS '已使用的 API 請求數量';

COMMENT ON FUNCTION search_us_stocks IS '搜尋美股函數，支援代號和名稱模糊搜尋';
COMMENT ON FUNCTION get_us_stock_by_symbol IS '根據股票代號獲取詳細資訊';
COMMENT ON FUNCTION upsert_us_stock IS '插入或更新美股資料';
COMMENT ON FUNCTION update_sync_status IS '更新同步狀態';
COMMENT ON FUNCTION get_sync_status IS '獲取同步狀態';
COMMENT ON FUNCTION check_stock_data_quality IS '檢查資料品質';

-- =====================================================
-- 11. 測試查詢範例
-- =====================================================

-- 測試搜尋功能
-- SELECT * FROM search_us_stocks('AAPL', true, 5);
-- SELECT * FROM search_us_stocks('Apple', true, 5);

-- 測試統計功能
-- SELECT * FROM get_us_stock_stats();

-- 測試同步狀態
-- SELECT * FROM get_sync_status('us_stocks');

-- 測試資料品質
-- SELECT * FROM check_stock_data_quality();

-- 查看視圖
-- SELECT * FROM sp500_stocks LIMIT 10;
-- SELECT * FROM sync_status_summary;

-- =====================================================
-- 設定完成
-- =====================================================

SELECT
    '🎉 美股資料庫完整設定完成！' as status,
    '支援 API → Supabase → 用戶 架構' as architecture,
    '包含同步狀態追蹤、資料品質檢查、RLS 安全政策' as features,
    '準備執行一次性股價同步' as next_step,
    NOW() as setup_time;
