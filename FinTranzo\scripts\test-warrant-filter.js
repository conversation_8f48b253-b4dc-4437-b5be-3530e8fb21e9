/**
 * 測試權證過濾功能
 * 模擬台股資料並測試權證過濾邏輯
 */

// 模擬台股資料（包含權證）
const mockTSEData = [
  { Code: '2330', Name: '台積電', ClosingPrice: '580.00' },
  { Code: '2317', Name: '鴻海', ClosingPrice: '105.50' },
  { Code: '2330P1', Name: '台積電購01', ClosingPrice: '12.50' }, // 權證
  { Code: '2317C2', Name: '鴻海購02', ClosingPrice: '8.30' },   // 權證
  { Code: '1301', Name: '台塑', ClosingPrice: '95.20' },
  { Code: '1301P3', Name: '台塑購03', ClosingPrice: '5.60' },   // 權證
  { Code: '0050', Name: '元大台灣50', ClosingPrice: '145.50' }, // ETF
  { Code: '0056', Name: '元大高股息', ClosingPrice: '35.80' },  // ETF
];

const mockOTCData = [
  { SecuritiesCompanyCode: '8446', CompanyName: '華研', Close: '156.0' },
  { SecuritiesCompanyCode: '3293', CompanyName: '鈊象', Close: '780.0' },
  { SecuritiesCompanyCode: '8446P1', CompanyName: '華研購01', Close: '15.5' }, // 權證
  { SecuritiesCompanyCode: '3293C2', CompanyName: '鈊象購02', Close: '25.8' }, // 權證
  { SecuritiesCompanyCode: '4736', CompanyName: '泰博', Close: '165.5' },
];

function testWarrantFilter() {
  console.log('🧪 開始測試權證過濾功能...\n');
  
  // 測試上市股票過濾
  console.log('📊 測試上市股票權證過濾:');
  console.log('原始資料:', mockTSEData.length, '檔');
  
  let tseStocks = [];
  let tseWarrants = 0;
  let tseETFs = 0;
  
  mockTSEData.forEach(stock => {
    // 過濾 ETF (代號以 00 開頭)
    if (stock.Code.startsWith('00')) {
      tseETFs++;
      return;
    }
    
    // 過濾權證 (名稱包含「購」字)
    if (stock.Name.includes('購')) {
      tseWarrants++;
      console.log(`  🚫 過濾權證: ${stock.Code} ${stock.Name}`);
      return;
    }
    
    tseStocks.push({
      code: stock.Code,
      name: stock.Name,
      market_type: 'TSE',
      closing_price: parseFloat(stock.ClosingPrice)
    });
    console.log(`  ✅ 保留股票: ${stock.Code} ${stock.Name}`);
  });
  
  console.log(`結果: 保留 ${tseStocks.length} 檔股票，過濾 ${tseWarrants} 檔權證，${tseETFs} 檔ETF\n`);
  
  // 測試上櫃股票過濾
  console.log('📊 測試上櫃股票權證過濾:');
  console.log('原始資料:', mockOTCData.length, '檔');
  
  let otcStocks = [];
  let otcWarrants = 0;
  
  mockOTCData.forEach(item => {
    const code = item.SecuritiesCompanyCode;
    const name = item.CompanyName;
    
    // 檢查代號格式（4位數字）
    if (!/^\d{4}$/.test(code)) {
      return;
    }
    
    // 過濾權證 (名稱包含「購」字)
    if (name.includes('購')) {
      otcWarrants++;
      console.log(`  🚫 過濾權證: ${code} ${name}`);
      return;
    }
    
    otcStocks.push({
      code: code,
      name: name,
      market_type: 'OTC',
      closing_price: parseFloat(item.Close)
    });
    console.log(`  ✅ 保留股票: ${code} ${name}`);
  });
  
  console.log(`結果: 保留 ${otcStocks.length} 檔股票，過濾 ${otcWarrants} 檔權證\n`);
  
  // 測試 ETF 過濾
  console.log('📊 測試 ETF 權證過濾:');
  let etfStocks = [];
  let etfWarrants = 0;
  
  mockTSEData.forEach(stock => {
    // 只選擇 ETF (代號以 00 開頭)
    if (!stock.Code.startsWith('00')) return;
    
    // 過濾權證 (名稱包含「購」字)
    if (stock.Name.includes('購')) {
      etfWarrants++;
      console.log(`  🚫 過濾ETF權證: ${stock.Code} ${stock.Name}`);
      return;
    }
    
    etfStocks.push({
      code: stock.Code,
      name: stock.Name,
      market_type: 'ETF',
      closing_price: parseFloat(stock.ClosingPrice)
    });
    console.log(`  ✅ 保留ETF: ${stock.Code} ${stock.Name}`);
  });
  
  console.log(`結果: 保留 ${etfStocks.length} 檔ETF，過濾 ${etfWarrants} 檔ETF權證\n`);
  
  // 總結
  const totalStocks = tseStocks.length + otcStocks.length + etfStocks.length;
  const totalWarrants = tseWarrants + otcWarrants + etfWarrants;
  
  console.log('🎯 測試總結:');
  console.log(`✅ 保留股票總數: ${totalStocks} 檔`);
  console.log(`   - 上市股票: ${tseStocks.length} 檔`);
  console.log(`   - 上櫃股票: ${otcStocks.length} 檔`);
  console.log(`   - ETF: ${etfStocks.length} 檔`);
  console.log(`🚫 過濾權證總數: ${totalWarrants} 檔`);
  console.log(`💾 節省資料庫空間: ${totalWarrants} 筆不必要的權證資料`);
  
  console.log('\n✅ 權證過濾功能測試完成！');
  console.log('🎉 所有包含「購」字的權證都已被正確過濾');
}

// 執行測試
testWarrantFilter();
