/**
 * 台股資料獲取系統 - 支援上市、上櫃、ETF
 * 使用 Node.js + TypeScript + Supabase
 * 資料來源：台灣證交所 + 櫃買中心 API
 */

import { createClient } from '@supabase/supabase-js';
import axios from 'axios';
import * as dotenv from 'dotenv';

// 載入環境變數
dotenv.config();

// 型別定義
interface StockData {
  code: string;
  name: string;
  market_type: 'TSE' | 'OTC' | 'ETF';
  closing_price: number;
  opening_price?: number;
  highest_price?: number;
  lowest_price?: number;
  volume?: number;
  transaction_count?: number;
  turnover?: number;
  price_change?: number;
  change_percent?: number;
  price_date: string;
}

interface TWSEResponse {
  stat: string;
  date: string;
  title: string;
  fields: string[];
  data: string[][];
}

interface OTCResponse {
  stkNo: string;
  stkName: string;
  showListPriceNote: string;
  showListPriceLink: string;
  reportDate: string;
  iTotalRecords: number;
  aaData: string[][];
}

// Supabase 客戶端
const supabase = createClient(
  process.env.EXPO_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

class TaiwanStockFetcher {
  private readonly TSE_API = 'https://openapi.twse.com.tw/v1/exchangeReport/STOCK_DAY_AVG_ALL';
  private readonly OTC_API = 'https://www.tpex.org.tw/web/stock/aftertrading/otc_quotes_no1430/stk_wn1430_result.php';
  private readonly ETF_API = 'https://openapi.twse.com.tw/v1/exchangeReport/TWT49U';

  /**
   * 獲取上市股票資料 (TSE)
   */
  async fetchTSEStocks(): Promise<StockData[]> {
    try {
      console.log('🏢 獲取上市股票資料...');
      const response = await axios.get<TWSEResponse>(this.TSE_API, {
        timeout: 30000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      console.log('📊 TSE API 回應狀態:', response.data.stat);
      console.log('📊 TSE API 資料筆數:', response.data.data?.length || 0);

      if (response.data.stat !== 'OK') {
        console.warn(`⚠️ TSE API 狀態異常: ${response.data.stat}`);
        // 如果有資料就繼續處理，否則返回空陣列
        if (!response.data.data || response.data.data.length === 0) {
          return [];
        }
      }

      const stocks: StockData[] = [];
      const today = new Date().toISOString().split('T')[0];

      response.data.data.forEach(row => {
        try {
          const [date, code, name, closingPrice, avgPrice] = row;
          
          // 過濾 ETF (代號以 00 開頭)
          if (code.startsWith('00')) return;
          
          const price = parseFloat(closingPrice);
          if (isNaN(price) || price <= 0) return;

          stocks.push({
            code: code.trim(),
            name: name.trim(),
            market_type: 'TSE',
            closing_price: price,
            price_date: today
          });
        } catch (error) {
          console.warn(`⚠️ TSE 資料解析錯誤: ${row}`);
        }
      });

      console.log(`✅ 上市股票: ${stocks.length} 檔`);
      return stocks;
    } catch (error) {
      console.error('❌ 獲取上市股票失敗:', error);
      return [];
    }
  }

  /**
   * 獲取上櫃股票資料 (OTC)
   */
  async fetchOTCStocks(): Promise<StockData[]> {
    try {
      console.log('🏪 獲取上櫃股票資料...');
      const today = new Date();
      const year = today.getFullYear() - 1911; // 轉換為民國年
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const day = String(today.getDate()).padStart(2, '0');
      const dateStr = `${year}/${month}/${day}`;

      const response = await axios.get<OTCResponse>(this.OTC_API, {
        params: {
          l: 'zh-tw',
          d: dateStr
        },
        timeout: 30000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      const stocks: StockData[] = [];
      const todayISO = today.toISOString().split('T')[0];

      if (response.data.aaData) {
        response.data.aaData.forEach(row => {
          try {
            const [code, name, closingPrice, change, openPrice, highPrice, lowPrice, volume] = row;
            
            const price = parseFloat(closingPrice);
            if (isNaN(price) || price <= 0) return;

            stocks.push({
              code: code.trim(),
              name: name.trim(),
              market_type: 'OTC',
              closing_price: price,
              opening_price: parseFloat(openPrice) || undefined,
              highest_price: parseFloat(highPrice) || undefined,
              lowest_price: parseFloat(lowPrice) || undefined,
              volume: parseInt(volume?.replace(/,/g, '')) || undefined,
              price_change: parseFloat(change) || undefined,
              price_date: todayISO
            });
          } catch (error) {
            console.warn(`⚠️ OTC 資料解析錯誤: ${row}`);
          }
        });
      }

      console.log(`✅ 上櫃股票: ${stocks.length} 檔`);
      return stocks;
    } catch (error) {
      console.error('❌ 獲取上櫃股票失敗:', error);
      return [];
    }
  }

  /**
   * 獲取 ETF 資料
   */
  async fetchETFStocks(): Promise<StockData[]> {
    try {
      console.log('📈 獲取 ETF 資料...');
      const response = await axios.get<TWSEResponse>(this.ETF_API, {
        timeout: 30000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      console.log('📊 ETF API 回應狀態:', response.data.stat);
      console.log('📊 ETF API 資料筆數:', response.data.data?.length || 0);

      if (response.data.stat !== 'OK') {
        console.warn(`⚠️ ETF API 狀態異常: ${response.data.stat}`);
        // 如果有資料就繼續處理，否則返回空陣列
        if (!response.data.data || response.data.data.length === 0) {
          return [];
        }
      }

      const stocks: StockData[] = [];
      const today = new Date().toISOString().split('T')[0];

      response.data.data.forEach(row => {
        try {
          const [code, name, closingPrice, change, openPrice, highPrice, lowPrice, volume] = row;
          
          const price = parseFloat(closingPrice);
          if (isNaN(price) || price <= 0) return;

          stocks.push({
            code: code.trim(),
            name: name.trim(),
            market_type: 'ETF',
            closing_price: price,
            opening_price: parseFloat(openPrice) || undefined,
            highest_price: parseFloat(highPrice) || undefined,
            lowest_price: parseFloat(lowPrice) || undefined,
            volume: parseInt(volume?.replace(/,/g, '')) || undefined,
            price_change: parseFloat(change) || undefined,
            price_date: today
          });
        } catch (error) {
          console.warn(`⚠️ ETF 資料解析錯誤: ${row}`);
        }
      });

      console.log(`✅ ETF: ${stocks.length} 檔`);
      return stocks;
    } catch (error) {
      console.error('❌ 獲取 ETF 失敗:', error);
      return [];
    }
  }

  /**
   * 批量更新資料庫
   */
  async updateDatabase(stocks: StockData[]): Promise<void> {
    try {
      console.log(`💾 開始更新資料庫: ${stocks.length} 檔股票...`);
      
      const batchSize = 100;
      let successCount = 0;
      let errorCount = 0;

      for (let i = 0; i < stocks.length; i += batchSize) {
        const batch = stocks.slice(i, i + batchSize);
        
        try {
          const { error } = await supabase
            .from('taiwan_stocks')
            .upsert(batch, { 
              onConflict: 'code',
              ignoreDuplicates: false 
            });

          if (error) {
            console.error(`❌ 批次 ${Math.floor(i/batchSize) + 1} 更新失敗:`, error);
            errorCount += batch.length;
          } else {
            successCount += batch.length;
            console.log(`✅ 批次 ${Math.floor(i/batchSize) + 1} 更新成功: ${batch.length} 檔`);
          }
        } catch (error) {
          console.error(`❌ 批次 ${Math.floor(i/batchSize) + 1} 執行錯誤:`, error);
          errorCount += batch.length;
        }

        // 避免 API 限制，稍作延遲
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      console.log(`🎉 資料庫更新完成!`);
      console.log(`   成功: ${successCount} 檔`);
      console.log(`   失敗: ${errorCount} 檔`);
    } catch (error) {
      console.error('❌ 資料庫更新失敗:', error);
      throw error;
    }
  }

  /**
   * 主要執行函數
   */
  async execute(): Promise<void> {
    try {
      console.log('🚀 開始獲取台股資料...');
      console.log(`⏰ 執行時間: ${new Date().toLocaleString('zh-TW')}`);

      // 並行獲取三種類型的股票資料
      const [tseStocks, otcStocks, etfStocks] = await Promise.all([
        this.fetchTSEStocks(),
        this.fetchOTCStocks(),
        this.fetchETFStocks()
      ]);

      // 合併所有資料
      const allStocks = [...tseStocks, ...otcStocks, ...etfStocks];
      
      console.log(`\n📊 資料統計:`);
      console.log(`   上市股票 (TSE): ${tseStocks.length} 檔`);
      console.log(`   上櫃股票 (OTC): ${otcStocks.length} 檔`);
      console.log(`   ETF: ${etfStocks.length} 檔`);
      console.log(`   總計: ${allStocks.length} 檔`);

      if (allStocks.length === 0) {
        console.warn('⚠️ 沒有獲取到任何股票資料');
        return;
      }

      // 更新資料庫
      await this.updateDatabase(allStocks);

      console.log('🎉 台股資料更新完成!');
    } catch (error) {
      console.error('❌ 執行失敗:', error);
      process.exit(1);
    }
  }
}

// 執行腳本
if (require.main === module) {
  const fetcher = new TaiwanStockFetcher();
  fetcher.execute();
}

export default TaiwanStockFetcher;
