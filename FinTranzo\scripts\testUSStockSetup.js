/**
 * 美股設定測試腳本
 * 測試 Alpha Vantage API 和 Supabase 連接
 */

// 載入環境變數
require('dotenv').config();

const ALPHA_VANTAGE_API_KEY = process.env.EXPO_PUBLIC_ALPHA_VANTAGE_API_KEY || 'QJTK95T7SA1661WM';
const ALPHA_VANTAGE_BASE_URL = 'https://www.alphavantage.co/query';

// Supabase 配置
const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

class USStockSetupTester {
  /**
   * 測試 Alpha Vantage API
   */
  async testAlphaVantageAPI() {
    console.log('🧪 測試 Alpha Vantage API...');
    
    try {
      const params = new URLSearchParams({
        function: 'GLOBAL_QUOTE',
        symbol: 'AAPL',
        apikey: ALPHA_VANTAGE_API_KEY,
      });

      const response = await fetch(`${ALPHA_VANTAGE_BASE_URL}?${params}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data['Global Quote'] && data['Global Quote']['01. symbol']) {
        const quote = data['Global Quote'];
        console.log('✅ Alpha Vantage API 測試成功:');
        console.log(`   股票: ${quote['01. symbol']}`);
        console.log(`   價格: $${quote['05. price']}`);
        console.log(`   漲跌: ${quote['10. change percent']}`);
        return true;
      } else {
        console.log('❌ API 回應格式錯誤:', data);
        return false;
      }
    } catch (error) {
      console.error('❌ Alpha Vantage API 測試失敗:', error.message);
      return false;
    }
  }

  /**
   * 測試 Supabase 連接
   */
  async testSupabaseConnection() {
    console.log('\n🧪 測試 Supabase 連接...');
    
    try {
      // 測試讀取
      const response = await fetch(`${SUPABASE_URL}/rest/v1/us_stocks?select=symbol&limit=1`, {
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Supabase 讀取測試成功');
        console.log(`   資料庫中有 ${data.length > 0 ? '資料' : '無資料'}`);
        return true;
      } else {
        console.log(`❌ Supabase 連接失敗: ${response.status}`);
        return false;
      }
    } catch (error) {
      console.error('❌ Supabase 連接測試失敗:', error.message);
      return false;
    }
  }

  /**
   * 測試資料庫寫入
   */
  async testDatabaseWrite() {
    console.log('\n🧪 測試資料庫寫入...');
    
    try {
      const testData = {
        stock_symbol: 'TEST',
        stock_name: 'Test Company',
        stock_price: 100.00,
        stock_open: 99.50,
        stock_high: 101.00,
        stock_low: 98.00,
        stock_volume: 1000000,
        stock_change: 0.50,
        stock_change_percent: 0.50,
        stock_previous_close: 99.50,
        is_sp500_stock: false
      };

      const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/upsert_us_stock`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify(testData),
      });

      if (response.ok) {
        console.log('✅ 資料庫寫入測試成功');
        
        // 清理測試資料
        await this.cleanupTestData();
        return true;
      } else {
        const errorText = await response.text();
        console.log(`❌ 資料庫寫入失敗: ${response.status} - ${errorText}`);
        return false;
      }
    } catch (error) {
      console.error('❌ 資料庫寫入測試失敗:', error.message);
      return false;
    }
  }

  /**
   * 清理測試資料
   */
  async cleanupTestData() {
    try {
      const response = await fetch(`${SUPABASE_URL}/rest/v1/us_stocks?symbol=eq.TEST`, {
        method: 'DELETE',
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        },
      });
      
      if (response.ok) {
        console.log('🧹 測試資料已清理');
      }
    } catch (error) {
      console.log('⚠️ 清理測試資料失敗:', error.message);
    }
  }

  /**
   * 測試搜尋功能
   */
  async testSearchFunction() {
    console.log('\n🧪 測試搜尋功能...');
    
    try {
      const payload = {
        search_term: 'AAPL',
        sp500_only: true,
        limit_count: 5
      };

      const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/search_us_stocks`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ 搜尋功能測試成功');
        console.log(`   找到 ${data.length} 個結果`);
        if (data.length > 0) {
          console.log(`   第一個結果: ${data[0].symbol} - ${data[0].name}`);
        }
        return true;
      } else {
        console.log(`❌ 搜尋功能測試失敗: ${response.status}`);
        return false;
      }
    } catch (error) {
      console.error('❌ 搜尋功能測試失敗:', error.message);
      return false;
    }
  }

  /**
   * 檢查環境變數
   */
  checkEnvironmentVariables() {
    console.log('🔍 檢查環境變數...');
    
    const requiredVars = [
      'EXPO_PUBLIC_SUPABASE_URL',
      'EXPO_PUBLIC_SUPABASE_ANON_KEY'
    ];
    
    let allPresent = true;
    
    for (const varName of requiredVars) {
      if (process.env[varName]) {
        console.log(`✅ ${varName}: 已設定`);
      } else {
        console.log(`❌ ${varName}: 未設定`);
        allPresent = false;
      }
    }
    
    if (!allPresent) {
      console.log('\n⚠️ 請確保在 .env 檔案中設定所有必要的環境變數');
      console.log('範例 .env 檔案內容:');
      console.log('EXPO_PUBLIC_SUPABASE_URL=https://your-project.supabase.co');
      console.log('EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key');
    }
    
    return allPresent;
  }

  /**
   * 執行完整測試
   */
  async runAllTests() {
    console.log('🚀 開始美股設定測試...\n');
    
    const results = {
      environment: this.checkEnvironmentVariables(),
      alphaVantage: await this.testAlphaVantageAPI(),
      supabaseRead: await this.testSupabaseConnection(),
      supabaseWrite: await this.testDatabaseWrite(),
      searchFunction: await this.testSearchFunction(),
    };
    
    console.log('\n📊 測試結果摘要:');
    console.log('==================');
    
    for (const [test, result] of Object.entries(results)) {
      const status = result ? '✅ 通過' : '❌ 失敗';
      const testName = {
        environment: '環境變數',
        alphaVantage: 'Alpha Vantage API',
        supabaseRead: 'Supabase 讀取',
        supabaseWrite: 'Supabase 寫入',
        searchFunction: '搜尋功能'
      }[test];
      
      console.log(`${testName}: ${status}`);
    }
    
    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
      console.log('\n🎉 所有測試通過！美股功能已準備就緒');
      console.log('\n下一步:');
      console.log('1. 執行: node scripts/batchSyncSP500.js --batch 1');
      console.log('2. 開始同步 SP500 股票資料');
    } else {
      console.log('\n⚠️ 部分測試失敗，請檢查設定');
    }
    
    return allPassed;
  }
}

// 主程序
async function main() {
  const tester = new USStockSetupTester();
  
  const args = process.argv.slice(2);
  
  if (args.includes('--help')) {
    console.log(`
美股設定測試工具

使用方式:
  node scripts/testUSStockSetup.js           # 執行完整測試
  node scripts/testUSStockSetup.js --api     # 只測試 API
  node scripts/testUSStockSetup.js --db      # 只測試資料庫
  node scripts/testUSStockSetup.js --help    # 顯示幫助
    `);
    return;
  }
  
  if (args.includes('--api')) {
    await tester.testAlphaVantageAPI();
    return;
  }
  
  if (args.includes('--db')) {
    await tester.testSupabaseConnection();
    await tester.testDatabaseWrite();
    await tester.testSearchFunction();
    return;
  }
  
  // 執行完整測試
  await tester.runAllTests();
}

// 執行主程序
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 測試執行失敗:', error);
    process.exit(1);
  });
}

module.exports = { USStockSetupTester };