/**
 * 手動執行股票價格同步腳本
 * 管理員執行此腳本將股價同步到 Supabase
 * 
 * 使用方法:
 * node scripts/syncStockPrices.js
 */

const { runOneTimeSyncProcess, checkSyncStatus } = require('../src/utils/runOneTimeSync');

async function main() {
  console.log('🎯 股票價格同步腳本');
  console.log('💡 目標：一次性將股價存入 Supabase，用戶直接讀取');
  console.log('=====================================\n');

  try {
    // 檢查當前狀態
    console.log('📊 檢查當前同步狀態...');
    const currentStatus = await checkSyncStatus();
    
    if (currentStatus && currentStatus.completionRate >= 80) {
      console.log('✅ 資料已同步，狀態良好');
      console.log('💡 如需重新同步，請先清空資料庫');
      return;
    }
    
    // 確認執行
    console.log('\n⚠️ 即將開始同步，這將：');
    console.log('   - 消耗約 50 次 Alpha Vantage API 請求');
    console.log('   - 需要 10-15 分鐘完成');
    console.log('   - 將股價存入 Supabase 供用戶查詢');
    
    // 在實際使用時可以添加確認機制
    const shouldProceed = true;
    
    if (!shouldProceed) {
      console.log('❌ 同步已取消');
      return;
    }
    
    // 執行同步
    console.log('\n🚀 開始執行同步...');
    const success = await runOneTimeSyncProcess();
    
    if (success) {
      console.log('\n🎉 同步完成！');
      console.log('💡 現在用戶可以直接從 Supabase 搜尋股票');
      console.log('🚫 不會再消耗 API 額度');
    } else {
      console.log('\n❌ 同步失敗');
      console.log('💡 請檢查網路連接和 API 配置');
    }
    
  } catch (error) {
    console.error('❌ 執行失敗:', error);
    process.exit(1);
  }
}

// 執行主函數
main().then(() => {
  console.log('\n📝 腳本執行完成');
  process.exit(0);
}).catch(error => {
  console.error('❌ 腳本執行失敗:', error);
  process.exit(1);
});
