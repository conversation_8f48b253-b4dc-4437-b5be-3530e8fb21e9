# 美股功能設定指南

本指南將協助您完成美股功能的設定，包括 SP500 股票資料同步和搜尋功能。

## 📋 功能概述

1. **SP500 股票清單**: 從 CSV 檔案載入 500 個 SP500 成分股
2. **Alpha Vantage API**: 使用免費 API 獲取即時股價資料
3. **Supabase 資料庫**: 儲存股票資料並提供快速搜尋
4. **用戶介面**: 在資產管理中支援美股搜尋和自動填入

## 🚀 快速開始

### 步驟 1: 測試設定

```bash
# 測試 API 和資料庫連接
node scripts/testUSStockSetup.js

# 只測試 Alpha Vantage API
node scripts/testUSStockSetup.js --api

# 只測試 Supabase 資料庫
node scripts/testUSStockSetup.js --db
```

### 步驟 2: 設定資料庫

確保您的 Supabase 專案已經執行了美股資料表設定：

```sql
-- 在 Supabase SQL Editor 中執行
-- 檔案位置: database/us_stocks_setup.sql
```

### 步驟 3: 修正權限 (如果需要)

```sql
-- 在 Supabase SQL Editor 中執行
-- 檔案位置: database/fix_permissions.sql
```

### 步驟 4: 同步 SP500 資料

#### 選項 A: 批量同步 (推薦)

```bash
# 查看進度狀態
node scripts/batchSyncSP500.js --status

# 處理第1批 (1-50 個股票)
node scripts/batchSyncSP500.js --batch 1

# 處理第2批 (51-100 個股票)
node scripts/batchSyncSP500.js --batch 2

# 自動處理所有批次 (需要約 2-3 小時)
node scripts/batchSyncSP500.js --auto

# 重試失敗的股票
node scripts/batchSyncSP500.js --retry
```

#### 選項 B: 完整同步

```bash
# 測試單個股票
node scripts/syncSP500WithAlphaVantage.js --test AAPL

# 完整同步 (需要約 2-3 小時)
node scripts/syncSP500WithAlphaVantage.js
```

## 📊 API 限制說明

### Alpha Vantage 免費版限制
- **每分鐘**: 5 次 API 調用
- **每天**: 500 次 API 調用
- **建議策略**: 分批處理，每批 50 個股票

### 同步時間估算
- **單批 (50 股票)**: 約 10-12 分鐘
- **完整 SP500**: 約 2-3 小時 (分 10 批)
- **建議**: 每天處理 1-2 批，避免超過限制

## 🔧 設定檔案說明

### 環境變數 (.env)
```env
EXPO_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

### 資料庫檔案
- `database/20250601135735.csv`: SP500 股票清單
- `database/us_stocks_setup.sql`: 資料庫結構設定
- `database/fix_permissions.sql`: 權限修正

### 同步腳本
- `scripts/testUSStockSetup.js`: 測試工具
- `scripts/batchSyncSP500.js`: 批量同步 (推薦)
- `scripts/syncSP500WithAlphaVantage.js`: 完整同步

## 📱 用戶介面功能

### 在資產管理中使用美股

1. **新增資產** → 選擇 **美股** 類型
2. **股票搜尋**: 輸入股票代號或公司名稱
3. **自動填入**: 系統自動填入公司名稱和最新股價
4. **匯率處理**: 自動獲取美元兌台幣匯率

### 支援的功能
- ✅ SP500 股票搜尋
- ✅ 即時股價顯示
- ✅ 自動匯率轉換
- ✅ 成本基礎計算
- ✅ 損益計算

## 🛠️ 故障排除

### 常見問題

#### 1. API 限制錯誤
```
Error: API_LIMIT_EXCEEDED
```
**解決方案**: 等待 1 分鐘後重試，或使用批量模式

#### 2. 資料庫權限錯誤
```
Error: permission denied for table us_stocks
```
**解決方案**: 執行 `database/fix_permissions.sql`

#### 3. 搜尋無結果
**可能原因**:
- 資料庫中沒有股票資料
- 搜尋關鍵字不正確

**解決方案**:
```bash
# 檢查資料庫狀態
node scripts/testUSStockSetup.js --db

# 同步部分資料進行測試
node scripts/batchSyncSP500.js --batch 1
```

### 檢查清單

- [ ] 環境變數已設定
- [ ] Supabase 資料庫已建立
- [ ] 權限已修正
- [ ] 至少同步了一批股票資料
- [ ] 測試腳本通過

## 📈 進階功能

### 自動更新股價

建議設定定期任務更新股價：

```bash
# 每日更新熱門股票
node scripts/batchSyncSP500.js --batch 1

# 重試失敗的股票
node scripts/batchSyncSP500.js --retry
```

### 監控同步狀態

```bash
# 查看詳細進度
node scripts/batchSyncSP500.js --status

# 查看日誌檔案
cat scripts/sp500_sync.log
```

## 🎯 最佳實踐

1. **分批同步**: 使用批量模式避免 API 限制
2. **定期更新**: 每週更新 1-2 批股票資料
3. **錯誤處理**: 定期重試失敗的股票
4. **監控使用**: 追蹤 API 調用次數

## 📞 支援

如果遇到問題，請檢查：

1. **日誌檔案**: `scripts/sp500_sync.log`
2. **進度檔案**: `scripts/sp500_sync_progress.json`
3. **測試結果**: 執行 `node scripts/testUSStockSetup.js`

---

## 🎉 完成！

設定完成後，您就可以在 FinTranzo 應用中：

1. 新增美股資產
2. 搜尋 SP500 股票
3. 自動獲取股價和匯率
4. 追蹤投資損益

享受您的美股投資管理體驗！