/**
 * Vercel Serverless Function - 台股每日更新
 * 每個工作日下午3點執行（台股收盤後）
 */

import { createClient } from '@supabase/supabase-js';

// Supabase 配置
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Missing Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseKey);

// 台股 API 配置
const GRS_API_BASE = 'https://api.finmindtrade.com/api/v4/data';

/**
 * 獲取台股即時報價
 */
async function fetchTaiwanStockPrice(stockCode) {
  try {
    const today = new Date().toISOString().split('T')[0];
    
    const response = await fetch(`${GRS_API_BASE}?dataset=TaiwanStockPrice&data_id=${stockCode}&start_date=${today}&token=${process.env.FINMIND_TOKEN}`);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (data.data && data.data.length > 0) {
      const latestData = data.data[data.data.length - 1];
      return {
        code: stockCode,
        price: parseFloat(latestData.close),
        change_percent: parseFloat(latestData.change_percent || 0),
        volume: parseInt(latestData.Trading_Volume || 0),
        updated_at: new Date().toISOString()
      };
    }
    
    return null;
  } catch (error) {
    console.error(`Error fetching ${stockCode}:`, error);
    return null;
  }
}

/**
 * 批量更新台股價格
 */
async function updateTaiwanStocks() {
  try {
    console.log('🚀 開始更新台股價格...');
    
    // 獲取需要更新的股票列表
    const { data: stocks, error: fetchError } = await supabase
      .from('taiwan_stocks')
      .select('code')
      .limit(100); // 限制每次更新數量
    
    if (fetchError) {
      throw fetchError;
    }
    
    console.log(`📊 找到 ${stocks.length} 支股票需要更新`);
    
    let successCount = 0;
    let failedCount = 0;
    
    // 批量處理，每次處理10支股票
    for (let i = 0; i < stocks.length; i += 10) {
      const batch = stocks.slice(i, i + 10);
      
      const promises = batch.map(stock => fetchTaiwanStockPrice(stock.code));
      const results = await Promise.all(promises);
      
      // 更新成功獲取的股票價格
      for (const result of results) {
        if (result) {
          const { error: updateError } = await supabase
            .from('taiwan_stocks')
            .update({
              closing_price: result.price,
              change_percent: result.change_percent,
              volume: result.volume,
              updated_at: result.updated_at
            })
            .eq('code', result.code);
          
          if (updateError) {
            console.error(`❌ 更新 ${result.code} 失敗:`, updateError);
            failedCount++;
          } else {
            console.log(`✅ 更新 ${result.code}: $${result.price}`);
            successCount++;
          }
        } else {
          failedCount++;
        }
      }
      
      // 避免 API 限制，每批次間隔1秒
      if (i + 10 < stocks.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    console.log(`✅ 台股更新完成: 成功 ${successCount}, 失敗 ${failedCount}`);
    
    return {
      success: true,
      updated: successCount,
      failed: failedCount,
      total: stocks.length
    };
    
  } catch (error) {
    console.error('❌ 台股更新失敗:', error);
    throw error;
  }
}

/**
 * Vercel Serverless Function 入口
 */
export default async function handler(req, res) {
  // 只允許 POST 請求和 Cron Jobs
  if (req.method !== 'POST' && req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  // 驗證 Cron Job 請求（可選）
  const authHeader = req.headers.authorization;
  if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
    console.log('⚠️ 未授權的請求，但繼續執行更新');
  }
  
  try {
    const result = await updateTaiwanStocks();
    
    res.status(200).json({
      message: '台股價格更新成功',
      timestamp: new Date().toISOString(),
      result
    });
    
  } catch (error) {
    console.error('❌ 處理請求失敗:', error);
    
    res.status(500).json({
      error: '台股價格更新失敗',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
}
