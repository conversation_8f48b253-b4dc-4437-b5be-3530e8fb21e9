#!/usr/bin/env python3
"""
使用Alpha Vantage API更新ETF股價到Supabase
"""

import os
import sys
import time
import requests
from datetime import datetime
from supabase import create_client, Client

# API配置
ALPHA_VANTAGE_API_KEY = "QJTK95T7SA1661WM"
ALPHA_VANTAGE_BASE_URL = "https://www.alphavantage.co/query"

# Supabase配置
SUPABASE_URL = "https://yrryyapzkgrsahranzvo.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlycnl5YXB6a2dyc2FocmFuenZvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODE3MzYzNSwiZXhwIjoyMDYzNzQ5NjM1fQ.xjiXVagzKgBBYXqtRjJiXwCgbRdEW0EpFzCIlJ26SSQ"

def get_etf_quote(symbol):
    """從Alpha Vantage獲取ETF即時報價"""
    try:
        params = {
            "function": "GLOBAL_QUOTE",
            "symbol": symbol,
            "apikey": ALPHA_VANTAGE_API_KEY
        }
        
        print(f"🔍 獲取 {symbol} 的報價...")
        response = requests.get(ALPHA_VANTAGE_BASE_URL, params=params, timeout=30)
        response.raise_for_status()
        data = response.json()
        
        # 檢查API限制
        if 'Note' in data:
            print(f"⚠️ API限制: {data['Note']}")
            return None
            
        if 'Error Message' in data:
            print(f"❌ API錯誤: {data['Error Message']}")
            return None
        
        if 'Global Quote' in data and data['Global Quote']:
            quote = data['Global Quote']
            
            # 解析數據
            price_data = {
                'symbol': quote.get('01. symbol', symbol),
                'price': float(quote.get('05. price', 0)),
                'open_price': float(quote.get('02. open', 0)),
                'high_price': float(quote.get('03. high', 0)),
                'low_price': float(quote.get('04. low', 0)),
                'volume': int(quote.get('06. volume', 0)),
                'change_amount': float(quote.get('09. change', 0)),
                'change_percent': float(quote.get('10. change percent', '0%').replace('%', '')),
                'previous_close': float(quote.get('08. previous close', 0)),
                'price_date': quote.get('07. latest trading day', ''),
                'updated_at': datetime.now().isoformat()
            }
            
            print(f"✅ {symbol}: ${price_data['price']} ({price_data['change_percent']:+.2f}%)")
            return price_data
        else:
            print(f"⚠️ 沒有找到 {symbol} 的報價數據")
            return None
            
    except requests.exceptions.Timeout:
        print(f"⏰ {symbol} 請求超時")
        return None
    except requests.exceptions.RequestException as e:
        print(f"❌ 網絡錯誤 {symbol}: {str(e)}")
        return None
    except Exception as e:
        print(f"❌ 獲取 {symbol} 報價時出錯: {str(e)}")
        return None

def update_etf_price_in_supabase(supabase: Client, price_data):
    """更新ETF價格到Supabase"""
    try:
        symbol = price_data['symbol']
        
        # 更新價格數據
        result = supabase.table('us_stocks').update({
            'price': price_data['price'],
            'open_price': price_data['open_price'],
            'high_price': price_data['high_price'],
            'low_price': price_data['low_price'],
            'volume': price_data['volume'],
            'change_amount': price_data['change_amount'],
            'change_percent': price_data['change_percent'],
            'previous_close': price_data['previous_close'],
            'price_date': price_data['price_date'],
            'updated_at': price_data['updated_at']
        }).eq('symbol', symbol).eq('is_etf', True).execute()
        
        if result.data:
            print(f"✅ 數據庫更新成功: {symbol}")
            return True
        else:
            print(f"⚠️ 數據庫更新沒有返回數據: {symbol}")
            return False
            
    except Exception as e:
        print(f"❌ 數據庫更新失敗 {symbol}: {str(e)}")
        return False

def get_etf_list_from_supabase(supabase: Client, limit=50):
    """從Supabase獲取ETF列表"""
    try:
        print("📊 從數據庫獲取ETF列表...")
        
        result = supabase.table('us_stocks').select('symbol, name').eq('is_etf', True).order('symbol').limit(limit).execute()
        
        if result.data:
            etf_list = [etf['symbol'] for etf in result.data]
            print(f"✅ 獲取到 {len(etf_list)} 個ETF")
            return etf_list
        else:
            print("⚠️ 沒有找到ETF數據")
            return []
            
    except Exception as e:
        print(f"❌ 獲取ETF列表失敗: {str(e)}")
        return []

def update_etf_prices(batch_size=5, max_etfs=50):
    """批量更新ETF價格"""
    print("🚀 開始更新ETF價格...")
    print("=" * 60)
    
    try:
        # 創建Supabase客戶端
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        print("✅ Supabase連接成功")
        
        # 獲取ETF列表
        etf_symbols = get_etf_list_from_supabase(supabase, max_etfs)
        
        if not etf_symbols:
            print("❌ 沒有ETF需要更新")
            return False
        
        print(f"\n🎯 準備更新 {len(etf_symbols)} 個ETF的價格")
        print(f"📦 批次大小: {batch_size} 個ETF")
        print(f"⏱️ API限制: 每分鐘5次請求")
        
        success_count = 0
        error_count = 0
        api_requests = 0
        
        for i, symbol in enumerate(etf_symbols):
            try:
                print(f"\n📈 處理 {symbol} ({i+1}/{len(etf_symbols)})")
                
                # 獲取股價數據
                price_data = get_etf_quote(symbol)
                api_requests += 1
                
                if price_data:
                    # 更新到數據庫
                    if update_etf_price_in_supabase(supabase, price_data):
                        success_count += 1
                    else:
                        error_count += 1
                else:
                    error_count += 1
                
                # API速率限制控制
                if api_requests % 5 == 0:
                    print("⏳ 等待60秒以遵守API速率限制...")
                    time.sleep(60)
                else:
                    print("⏳ 等待12秒...")
                    time.sleep(12)
                
            except KeyboardInterrupt:
                print("\n⚠️ 用戶中斷更新")
                break
            except Exception as e:
                print(f"❌ 處理 {symbol} 時發生錯誤: {str(e)}")
                error_count += 1
        
        # 顯示結果
        print("\n" + "=" * 60)
        print("🎉 ETF價格更新完成！")
        print(f"✅ 成功更新: {success_count} 個ETF")
        print(f"❌ 更新失敗: {error_count} 個ETF")
        print(f"📡 API請求總數: {api_requests} 次")
        
        if success_count > 0:
            print("\n📊 驗證更新結果...")
            # 驗證更新結果
            verify_result = supabase.table('us_stocks').select('symbol, name, price, change_percent, updated_at').eq('is_etf', True).not_.is_('price', 'null').order('updated_at', desc=True).limit(5).execute()
            
            if verify_result.data:
                print("✅ 最近更新的ETF:")
                for etf in verify_result.data:
                    print(f"   {etf['symbol']}: ${etf['price']} ({etf['change_percent']:+.2f}%) - {etf['updated_at'][:19]}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 更新過程中發生錯誤: {str(e)}")
        return False

def main():
    """主函數"""
    print("📈 ETF價格更新工具")
    print("=" * 60)
    
    # 檢查API密鑰
    if not ALPHA_VANTAGE_API_KEY or ALPHA_VANTAGE_API_KEY == "YOUR_API_KEY":
        print("❌ 請設置正確的Alpha Vantage API密鑰")
        return False
    
    print(f"🔑 API密鑰: {ALPHA_VANTAGE_API_KEY[:10]}...")
    print(f"🗄️ Supabase URL: {SUPABASE_URL}")
    
    # 詢問用戶更新數量
    try:
        max_etfs = input("\n請輸入要更新的ETF數量 (預設20，最多100): ").strip()
        if not max_etfs:
            max_etfs = 20
        else:
            max_etfs = min(int(max_etfs), 100)
    except ValueError:
        max_etfs = 20
    
    print(f"🎯 將更新 {max_etfs} 個ETF的價格")
    
    # 開始更新
    success = update_etf_prices(batch_size=5, max_etfs=max_etfs)
    
    if success:
        print("\n✅ ETF價格更新成功！")
        print("🔍 您現在可以在前端搜索ETF並查看最新價格")
        return True
    else:
        print("\n❌ ETF價格更新失敗")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 用戶中斷程序")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序執行失敗: {str(e)}")
        sys.exit(1)
