<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FinTranzo - 個人財務管理專家 (Demo)</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #F8F9FA;
            color: #333;
        }

        .app-container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: white;
            padding: 60px 24px 20px;
            border-bottom: 1px solid #E5E5E5;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .greeting {
            font-size: 16px;
            color: #666;
        }

        .user-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .sign-out {
            padding: 8px;
            color: #666;
            cursor: pointer;
        }

        .content {
            padding: 24px;
        }

        .chart-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .net-worth-amount {
            font-size: 32px;
            font-weight: bold;
            color: #007AFF;
            margin-bottom: 16px;
        }

        .chart-placeholder {
            height: 200px;
            background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .bento-grid {
            margin-bottom: 24px;
        }

        .summary-row {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
        }

        .summary-card {
            flex: 1;
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.1);
        }

        .assets-card {
            border-left: 4px solid #34C759;
        }

        .liabilities-card {
            border-left: 4px solid #FF3B30;
        }

        .income-card {
            border-left: 4px solid #007AFF;
        }

        .expense-card {
            border-left: 4px solid #FF9500;
        }

        .summary-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 4px;
        }

        .summary-amount {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .top-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.1);
        }

        .top-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #F0F0F0;
        }

        .top-item:last-child {
            border-bottom: none;
        }

        .top-item-name {
            font-size: 14px;
            color: #333;
            flex: 1;
        }

        .gain-amount {
            font-size: 14px;
            font-weight: 600;
            color: #34C759;
        }

        .gain-percent {
            font-size: 12px;
            color: #34C759;
        }

        .loss-amount {
            font-size: 14px;
            font-weight: 600;
            color: #FF3B30;
        }

        .loss-percent {
            font-size: 12px;
            color: #FF3B30;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 414px;
            background: white;
            border-top: 1px solid #E5E5E5;
            padding: 5px 0;
            height: 60px;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            cursor: pointer;
            transition: color 0.2s;
        }

        .nav-item.active {
            color: #007AFF;
        }

        .nav-item:not(.active) {
            color: gray;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 2px;
        }

        .nav-label {
            font-size: 12px;
            font-weight: 500;
        }

        .demo-badge {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #FF3B30;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            z-index: 1000;
        }

        .add-button {
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 20px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            transition: background-color 0.2s;
        }

        .add-button:hover {
            background: #0056CC;
        }

        .functional-badge {
            position: fixed;
            top: 60px;
            right: 20px;
            background: #34C759;
            color: white;
            padding: 6px 10px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="demo-badge">DEMO</div>
    <div class="functional-badge">✅ 名稱非必填</div>

    <div class="app-container">
        <!-- Header -->
        <div class="header">
            <div>
                <div class="greeting">您好，</div>
                <div class="user-name">demo</div>
            </div>
            <div class="sign-out">⚙️</div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Net Worth Chart -->
            <div class="chart-card">
                <div class="card-title">近一年淨資產變化</div>
                <div class="net-worth-amount">NT$1,250,000</div>
                <div class="chart-placeholder">📈 淨資產趨勢圖</div>
            </div>

            <!-- Bento Grid Layout -->
            <div class="bento-grid">
                <!-- Financial Summary Cards -->
                <div class="summary-row">
                    <div class="summary-card assets-card">
                        <div class="summary-label">總資產</div>
                        <div class="summary-amount">NT$1,500,000</div>
                    </div>
                    <div class="summary-card liabilities-card">
                        <div class="summary-label">總負債</div>
                        <div class="summary-amount">NT$250,000</div>
                    </div>
                </div>

                <div class="summary-row">
                    <div class="summary-card income-card">
                        <div class="summary-label">月收入</div>
                        <div class="summary-amount">NT$80,000</div>
                    </div>
                    <div class="summary-card expense-card">
                        <div class="summary-label">月支出</div>
                        <div class="summary-amount">NT$45,000</div>
                    </div>
                </div>

                <!-- Top Gains -->
                <div class="top-card">
                    <div class="card-title">
                        資產增長 TOP 5
                        <button class="add-button" onclick="showAddForm('asset')" title="新增資產">+</button>
                    </div>
                    <div class="top-item">
                        <div class="top-item-name">台積電</div>
                        <div>
                            <div class="gain-amount">+NT$25,000</div>
                            <div class="gain-percent">+12.5%</div>
                        </div>
                    </div>
                    <div class="top-item">
                        <div class="top-item-name">蘋果股票</div>
                        <div>
                            <div class="gain-amount">+NT$18,000</div>
                            <div class="gain-percent">+8.2%</div>
                        </div>
                    </div>
                    <div class="top-item">
                        <div class="top-item-name">0050 ETF</div>
                        <div>
                            <div class="gain-amount">+NT$15,000</div>
                            <div class="gain-percent">+6.8%</div>
                        </div>
                    </div>
                </div>

                <!-- Top Losses -->
                <div class="top-card">
                    <div class="card-title">
                        負債管理
                        <button class="add-button" onclick="showAddForm('liability')" title="新增負債">+</button>
                    </div>
                    <div class="top-item">
                        <div class="top-item-name">加密貨幣</div>
                        <div>
                            <div class="loss-amount">-NT$12,000</div>
                            <div class="loss-percent">-15.2%</div>
                        </div>
                    </div>
                    <div class="top-item">
                        <div class="top-item-name">個股投資</div>
                        <div>
                            <div class="loss-amount">-NT$8,000</div>
                            <div class="loss-percent">-10.5%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <div class="nav-item active">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">儀表板</div>
            </div>
            <div class="nav-item" onclick="showAddForm('transaction')">
                <div class="nav-icon">📅</div>
                <div class="nav-label">記帳</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon">💰</div>
                <div class="nav-label">資產負債</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon">📋</div>
                <div class="nav-label">收支分析</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon">📊</div>
                <div class="nav-label">圖表分析</div>
            </div>
        </div>
    </div>

    <script>
        // Add form functionality
        function showAddForm(type) {
            let message = '';
            let details = '';

            switch(type) {
                case 'transaction':
                    message = '📝 新增交易記錄';
                    details = '✅ 支援收入/支出分類\n✅ 多種類別選擇\n✅ 帳戶管理\n✅ 即時更新月曆視圖\n✅ 選擇日期後變藍色顯示\n✅ 描述非必填，預設為"收入"或"支出"';
                    break;
                case 'asset':
                    message = '💎 新增資產';
                    details = '✅ 支援台股/美股/基金\n✅ 自動價格更新\n✅ 損益計算\n✅ 資產配置分析\n✅ 名稱非必填，預設為資產類型名稱';
                    break;
                case 'liability':
                    message = '💳 新增負債';
                    details = '✅ 信用卡/貸款管理\n✅ 利率計算\n✅ 還款提醒\n✅ 淨資產自動計算\n✅ 名稱非必填，預設為負債類型名稱';
                    break;
                default:
                    message = '🚀 功能開發中';
                    details = '更多功能即將推出...';
            }

            alert(`${message}\n\n${details}\n\n💡 這些功能在 React Native 版本中完全可用！`);
        }

        // Simple navigation simulation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                // 避免重複綁定點擊事件
                if (this.onclick) return;

                document.querySelectorAll('.nav-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');

                const label = this.querySelector('.nav-label').textContent;
                console.log(`Navigated to: ${label}`);

                // You could add more sophisticated page switching here
                if (label !== '儀表板' && label !== '記帳') {
                    alert(`${label} 頁面開發中...`);
                }
            });
        });

        // Add button hover effects
        document.querySelectorAll('.add-button').forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.1)';
            });

            button.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });

        console.log('FinTranzo Demo Loaded');
        console.log('✅ React Native + Expo + TypeScript 架構已建立');
        console.log('✅ Supabase 整合已配置');
        console.log('✅ 五大核心頁面已實現');
        console.log('✅ Bento Grid UI 風格已應用');
        console.log('✅ 所有 + 按鈕功能已連接');
        console.log('🎉 點擊任何 + 按鈕試試看！');
    </script>
</body>
</html>
