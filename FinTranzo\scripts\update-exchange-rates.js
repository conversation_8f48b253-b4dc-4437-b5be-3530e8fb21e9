/**
 * GitHub Actions - 匯率每日更新腳本
 */

const { createClient } = require('@supabase/supabase-js');

// Supabase 配置
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ 缺少 Supabase 環境變數');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * 使用 ExchangeRate-API 獲取匯率
 */
async function fetchExchangeRates() {
  try {
    console.log('📡 使用 ExchangeRate-API 獲取匯率...');
    const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (data.rates && data.rates.TWD) {
      return {
        usd_to_twd: data.rates.TWD,
        updated_at: new Date().toISOString(),
        source: 'exchangerate-api'
      };
    }
    
    return null;
  } catch (error) {
    console.error('❌ ExchangeRate-API 失敗:', error.message);
    return null;
  }
}

/**
 * 使用台灣銀行 API 獲取匯率（備用方案）
 */
async function fetchTaiwanBankRates() {
  try {
    console.log('📡 使用台灣銀行 API 獲取匯率...');
    const response = await fetch('https://rate.bot.com.tw/xrt/flcsv/0/day');
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const csvText = await response.text();
    const lines = csvText.split('\n');
    
    // 尋找美元匯率
    for (const line of lines) {
      if (line.includes('USD') || line.includes('美金')) {
        const columns = line.split(',');
        if (columns.length >= 4) {
          const buyRate = parseFloat(columns[2]); // 現金買入
          const sellRate = parseFloat(columns[3]); // 現金賣出
          
          if (!isNaN(buyRate) && !isNaN(sellRate)) {
            const midRate = (buyRate + sellRate) / 2;
            
            return {
              usd_to_twd: midRate,
              buy_rate: buyRate,
              sell_rate: sellRate,
              updated_at: new Date().toISOString(),
              source: 'taiwan-bank'
            };
          }
        }
      }
    }
    
    return null;
  } catch (error) {
    console.error('❌ 台灣銀行 API 失敗:', error.message);
    return null;
  }
}

/**
 * 使用 Fixer.io API 獲取匯率（第三備用方案）
 */
async function fetchFixerRates() {
  try {
    console.log('📡 使用 Fixer.io API 獲取匯率...');
    // 免費版 Fixer.io
    const response = await fetch('http://data.fixer.io/api/latest?access_key=free&base=USD&symbols=TWD');
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (data.success && data.rates && data.rates.TWD) {
      return {
        usd_to_twd: data.rates.TWD,
        updated_at: new Date().toISOString(),
        source: 'fixer-io'
      };
    }
    
    return null;
  } catch (error) {
    console.error('❌ Fixer.io API 失敗:', error.message);
    return null;
  }
}

/**
 * 手動設定匯率（最後備用方案）
 */
function getDefaultExchangeRate() {
  console.log('⚠️ 使用預設匯率');
  return {
    usd_to_twd: 31.5, // 預設匯率
    updated_at: new Date().toISOString(),
    source: 'default'
  };
}

/**
 * 更新匯率到資料庫
 */
async function updateExchangeRates() {
  try {
    console.log('🚀 開始更新匯率...');
    
    let rateData = null;
    
    // 嘗試多個數據源
    rateData = await fetchExchangeRates();
    
    if (!rateData) {
      console.log('⚠️ 主要 API 失敗，嘗試台灣銀行 API');
      rateData = await fetchTaiwanBankRates();
    }
    
    if (!rateData) {
      console.log('⚠️ 台灣銀行 API 失敗，嘗試 Fixer.io API');
      rateData = await fetchFixerRates();
    }
    
    if (!rateData) {
      console.log('⚠️ 所有 API 失敗，使用預設匯率');
      rateData = getDefaultExchangeRate();
    }
    
    console.log(`📊 獲取匯率: USD/TWD = ${rateData.usd_to_twd} (來源: ${rateData.source})`);
    
    // 更新到資料庫
    const { error: updateError } = await supabase
      .from('exchange_rates')
      .upsert({
        from_currency: 'USD',
        to_currency: 'TWD',
        rate: rateData.usd_to_twd,
        buy_rate: rateData.buy_rate || null,
        sell_rate: rateData.sell_rate || null,
        source: rateData.source,
        updated_at: rateData.updated_at
      }, {
        onConflict: 'from_currency,to_currency'
      });
    
    if (updateError) {
      throw updateError;
    }
    
    console.log('✅ 匯率更新成功');
    
    // 記錄更新日誌
    const { error: logError } = await supabase
      .from('update_logs')
      .insert({
        type: 'exchange_rates',
        success_count: 1,
        failed_count: 0,
        total_count: 1,
        updated_at: new Date().toISOString(),
        notes: `USD/TWD: ${rateData.usd_to_twd} (${rateData.source})`
      });
    
    if (logError) {
      console.warn('⚠️ 記錄日誌失敗:', logError);
    }
    
    return {
      success: true,
      rate: rateData.usd_to_twd,
      source: rateData.source,
      updated_at: rateData.updated_at
    };
    
  } catch (error) {
    console.error('❌ 匯率更新失敗:', error);
    throw error;
  }
}

/**
 * 主函數
 */
async function main() {
  try {
    console.log('💱 GitHub Actions - 匯率更新開始');
    console.log('⏰ 執行時間:', new Date().toLocaleString('zh-TW'));
    
    const result = await updateExchangeRates();
    
    console.log('📊 更新結果:', result);
    console.log('🎉 匯率更新完成！');
    
    process.exit(0);
    
  } catch (error) {
    console.error('💥 執行失敗:', error);
    process.exit(1);
  }
}

// 執行主函數
if (require.main === module) {
  main();
}

module.exports = { updateExchangeRates };
