-- 修復ETF搜索函數，確保返回正確的ETF標籤
-- 執行此腳本來修復ETF搜索函數

-- 1. 刪除現有的ETF搜索函數
DROP FUNCTION IF EXISTS search_us_etf(TEXT, INTEGER);
DROP FUNCTION IF EXISTS search_us_etf(TEXT);

-- 2. 重新創建ETF搜索函數，包含所有必要字段
CREATE OR REPLACE FUNCTION search_us_etf(
    search_term TEXT,
    limit_count INTEGER DEFAULT 50
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    sector VARCHAR(100),
    price DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT,
    is_etf BOOLEAN,
    asset_type VARCHAR(20)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, 
        s.name, 
        s.sector, 
        s.price, 
        s.change_percent, 
        s.market_cap,
        s.is_etf,
        s.asset_type
    FROM us_stocks s
    WHERE 
        s.is_etf = true
        AND (s.symbol ILIKE '%' || search_term || '%' OR s.name ILIKE '%' || search_term || '%')
    ORDER BY s.market_cap DESC NULLS LAST
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 3. 同時修復普通股票搜索函數，確保返回ETF標籤
DROP FUNCTION IF EXISTS search_us_stocks(TEXT, BOOLEAN, INTEGER);

CREATE OR REPLACE FUNCTION search_us_stocks(
    search_term TEXT,
    sp500_only BOOLEAN DEFAULT false,
    limit_count INTEGER DEFAULT 50
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(200),
    sector VARCHAR(100),
    price DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    market_cap BIGINT,
    is_etf BOOLEAN,
    asset_type VARCHAR(20)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, 
        s.name, 
        s.sector, 
        s.price, 
        s.change_percent, 
        s.market_cap,
        s.is_etf,
        s.asset_type
    FROM us_stocks s
    WHERE 
        (sp500_only = false OR s.is_sp500 = true)
        AND (s.symbol ILIKE '%' || search_term || '%' OR s.name ILIKE '%' || search_term || '%')
    ORDER BY s.market_cap DESC NULLS LAST
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 4. 設置權限
GRANT EXECUTE ON FUNCTION search_us_etf(TEXT, INTEGER) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION search_us_stocks(TEXT, BOOLEAN, INTEGER) TO anon, authenticated;

-- 5. 測試函數
DO $$
DECLARE
    test_result RECORD;
    etf_count INTEGER := 0;
BEGIN
    RAISE NOTICE '🧪 測試ETF搜索函數...';
    
    -- 測試搜索QQQ
    FOR test_result IN 
        SELECT * FROM search_us_etf('qqq', 5)
    LOOP
        etf_count := etf_count + 1;
        RAISE NOTICE '   %: % - is_etf: % - asset_type: %', 
            test_result.symbol, 
            test_result.name, 
            test_result.is_etf, 
            test_result.asset_type;
    END LOOP;
    
    RAISE NOTICE '✅ 找到 % 個ETF結果', etf_count;
    
    IF etf_count > 0 THEN
        RAISE NOTICE '✅ ETF搜索函數修復成功！';
    ELSE
        RAISE NOTICE '⚠️ 沒有找到ETF結果，請檢查數據';
    END IF;
END $$;
