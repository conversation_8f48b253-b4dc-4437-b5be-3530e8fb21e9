// 測試月末日期調整邏輯

// 模擬 calculateNextDate 函數
function calculateNextDate(currentDate, frequency, originalTargetDay) {
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  const day = currentDate.getDate();
  const hours = currentDate.getHours();
  const minutes = currentDate.getMinutes();
  const seconds = currentDate.getSeconds();
  const milliseconds = currentDate.getMilliseconds();

  switch (frequency) {
    case 'monthly':
      // 使用原始目標日期或當前日期
      const targetDay = originalTargetDay || day;

      // 計算下個月
      const nextMonth = month + 1;
      const nextYear = nextMonth > 11 ? year + 1 : year;
      const adjustedMonth = nextMonth > 11 ? 0 : nextMonth;

      // 獲取下個月的最後一天
      const lastDayOfNextMonth = new Date(nextYear, adjustedMonth + 1, 0).getDate();

      // 根據正確的月末調整邏輯
      let adjustedDay;
      if (targetDay > lastDayOfNextMonth) {
        // 如果原始目標日期超過該月的最大天數，使用該月的最後一天
        adjustedDay = lastDayOfNextMonth;
        console.log(`📅 月末日期調整: 原定${targetDay}號，${nextYear}年${adjustedMonth + 1}月只有${lastDayOfNextMonth}天，調整為${lastDayOfNextMonth}號`);
      } else {
        adjustedDay = targetDay;
      }

      return new Date(nextYear, adjustedMonth, adjustedDay, hours, minutes, seconds, milliseconds);

    default:
      throw new Error(`不支援的循環頻率: ${frequency}`);
  }
}

// 測試函數
function testMonthEndLogic() {
  console.log('🧪 測試月末日期調整邏輯\n');

  // 測試案例1: 31號開始
  console.log('📅 測試案例1: 每月31號');
  console.log('預期結果: 1,3,5,7,8,10,12月為31號，4,6,9,11月為30號，2月為28號\n');

  let currentDate = new Date(2024, 0, 31); // 2024年1月31日
  const originalTargetDay = 31;

  for (let i = 0; i < 12; i++) {
    const monthName = currentDate.toLocaleDateString('zh-TW', { year: 'numeric', month: 'long' });
    const actualDay = currentDate.getDate();
    const monthNumber = currentDate.getMonth() + 1;

    // 檢查是否符合預期
    let expected;
    if ([1, 3, 5, 7, 8, 10, 12].includes(monthNumber)) {
      expected = 31; // 有31天的月份
    } else if (monthNumber === 2) {
      // 2024年是閏年，2月有29天
      expected = 29;
    } else {
      expected = 30; // 4,6,9,11月有30天
    }

    const status = actualDay === expected ? '✅' : '❌';
    console.log(`  ${status} ${monthName}: ${actualDay}號 (預期: ${expected}號)`);

    // 移動到下個月
    if (i < 11) {
      currentDate = calculateNextDate(currentDate, 'monthly', originalTargetDay);
    }
  }

  console.log('\n📅 測試案例2: 每月30號');
  console.log('預期結果: 除了2月為28號外，其他月份都應該是30號\n');

  currentDate = new Date(2024, 0, 30); // 2024年1月30日
  const originalTargetDay30 = 30;

  for (let i = 0; i < 12; i++) {
    const monthName = currentDate.toLocaleDateString('zh-TW', { year: 'numeric', month: 'long' });
    const actualDay = currentDate.getDate();
    const monthNumber = currentDate.getMonth() + 1;

    // 檢查是否符合預期
    let expected = monthNumber === 2 ? 29 : 30; // 2024年是閏年

    const status = actualDay === expected ? '✅' : '❌';
    console.log(`  ${status} ${monthName}: ${actualDay}號 (預期: ${expected}號)`);

    // 移動到下個月
    if (i < 11) {
      currentDate = calculateNextDate(currentDate, 'monthly', originalTargetDay30);
    }
  }

  console.log('\n📅 測試案例3: 每月29號');
  console.log('預期結果: 除了2月為28號外，其他月份都應該是29號\n');

  currentDate = new Date(2024, 0, 29); // 2024年1月29日
  const originalTargetDay29 = 29;

  for (let i = 0; i < 12; i++) {
    const monthName = currentDate.toLocaleDateString('zh-TW', { year: 'numeric', month: 'long' });
    const actualDay = currentDate.getDate();
    const monthNumber = currentDate.getMonth() + 1;

    // 檢查是否符合預期
    let expected = monthNumber === 2 ? 29 : 29; // 2024年是閏年

    const status = actualDay === expected ? '✅' : '❌';
    console.log(`  ${status} ${monthName}: ${actualDay}號 (預期: ${expected}號)`);

    // 移動到下個月
    if (i < 11) {
      currentDate = calculateNextDate(currentDate, 'monthly', originalTargetDay29);
    }
  }
}

// 執行測試
testMonthEndLogic();
