name: 更新台股資料

on:
  # 每個交易日下午 3:30 執行 (台北時間)
  schedule:
    - cron: '30 7 * * 1-5'  # UTC 7:30 = 台北時間 15:30
  
  # 手動觸發
  workflow_dispatch:
    inputs:
      force_update:
        description: '強制更新所有資料'
        required: false
        default: 'false'
        type: boolean

jobs:
  update-taiwan-stocks:
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout 程式碼
        uses: actions/checkout@v4

      - name: 🟢 設置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: 📦 安裝依賴
        run: npm ci

      - name: 🔧 設置環境變數
        env:
          EXPO_PUBLIC_SUPABASE_URL: ${{ secrets.EXPO_PUBLIC_SUPABASE_URL }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
        run: |
          echo "EXPO_PUBLIC_SUPABASE_URL=$EXPO_PUBLIC_SUPABASE_URL" >> .env
          echo "SUPABASE_SERVICE_ROLE_KEY=$SUPABASE_SERVICE_ROLE_KEY" >> .env

      - name: 🚀 執行股票資料更新
        run: npm run fetch-stocks
        env:
          EXPO_PUBLIC_SUPABASE_URL: ${{ secrets.EXPO_PUBLIC_SUPABASE_URL }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}

      - name: 📊 驗證更新結果
        run: |
          echo "✅ 台股資料更新完成"
          echo "⏰ 更新時間: $(date '+%Y-%m-%d %H:%M:%S')"

      - name: 🔔 通知更新結果 (成功)
        if: success()
        run: |
          echo "🎉 台股資料更新成功!"
          echo "📈 包含: 上市股票 (TSE) + 上櫃股票 (OTC) + ETF"

      - name: ❌ 通知更新結果 (失敗)
        if: failure()
        run: |
          echo "💥 台股資料更新失敗!"
          echo "請檢查 GitHub Actions 日誌"
