name: 📈 每日股票和匯率更新

on:
  schedule:
    # 匯率更新：每個工作日早上9點 (UTC+8 = UTC 1點)
    - cron: '0 1 * * 1-5'
    # 台股更新：每個工作日下午3點 (UTC+8 = UTC 7點) - 分批執行
    - cron: '0 7 * * 1-5'   # 第1批：上市股票
    - cron: '10 7 * * 1-5'  # 第2批：上櫃股票
    - cron: '20 7 * * 1-5'  # 第3批：ETF
    # 美股更新：每個工作日晚上10點 (UTC+8 = UTC 14點) - 分批執行
    - cron: '0 14 * * 1-5'  # 第1批：大型股
    - cron: '15 14 * * 1-5' # 第2批：中型股
    - cron: '30 14 * * 1-5' # 第3批：ETF

  # 手動觸發
  workflow_dispatch:
    inputs:
      update_type:
        description: '選擇更新類型'
        required: true
        default: 'all'
        type: choice
        options:
        - all
        - taiwan-tse
        - taiwan-otc
        - taiwan-etf
        - us-large
        - us-mid
        - us-etf
        - exchange-rates

jobs:
  # 匯率更新 Job
  update-exchange-rates:
    name: 💱 更新匯率
    runs-on: ubuntu-latest
    if: github.event.schedule == '0 1 * * 1-5' || github.event.inputs.update_type == 'all' || github.event.inputs.update_type == 'exchange-rates'

    steps:
    - name: 📥 檢出代碼
      uses: actions/checkout@v4

    - name: 🟢 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 📦 安裝依賴
      run: npm ci

    - name: 💱 執行匯率更新
      env:
        SUPABASE_URL: ${{ secrets.EXPO_PUBLIC_SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
      run: node scripts/update-exchange-rates.js

  # 台股上市股票更新
  update-taiwan-tse:
    name: 🇹🇼 更新台股上市股票
    runs-on: ubuntu-latest
    if: github.event.schedule == '0 7 * * 1-5' || github.event.inputs.update_type == 'all' || github.event.inputs.update_type == 'taiwan-tse'

    steps:
    - name: 📥 檢出代碼
      uses: actions/checkout@v4

    - name: 🟢 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 📦 安裝依賴
      run: npm ci

    - name: 🏢 執行台股上市股票更新
      env:
        SUPABASE_URL: ${{ secrets.EXPO_PUBLIC_SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
        FINMIND_TOKEN: ${{ secrets.FINMIND_TOKEN }}
        BATCH_TYPE: 'TSE'
      run: node scripts/update-taiwan-stocks.js

  # 台股上櫃股票更新
  update-taiwan-otc:
    name: 🇹🇼 更新台股上櫃股票
    runs-on: ubuntu-latest
    if: github.event.schedule == '10 7 * * 1-5' || github.event.inputs.update_type == 'all' || github.event.inputs.update_type == 'taiwan-otc'

    steps:
    - name: 📥 檢出代碼
      uses: actions/checkout@v4

    - name: 🟢 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 📦 安裝依賴
      run: npm ci

    - name: 🏪 執行台股上櫃股票更新
      env:
        SUPABASE_URL: ${{ secrets.EXPO_PUBLIC_SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
        FINMIND_TOKEN: ${{ secrets.FINMIND_TOKEN }}
        BATCH_TYPE: 'OTC'
      run: node scripts/update-taiwan-stocks.js

  # 台股ETF更新
  update-taiwan-etf:
    name: 🇹🇼 更新台股ETF
    runs-on: ubuntu-latest
    if: github.event.schedule == '20 7 * * 1-5' || github.event.inputs.update_type == 'all' || github.event.inputs.update_type == 'taiwan-etf'

    steps:
    - name: 📥 檢出代碼
      uses: actions/checkout@v4

    - name: 🟢 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 📦 安裝依賴
      run: npm ci

    - name: 📊 執行台股ETF更新
      env:
        SUPABASE_URL: ${{ secrets.EXPO_PUBLIC_SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
        FINMIND_TOKEN: ${{ secrets.FINMIND_TOKEN }}
        BATCH_TYPE: 'ETF'
      run: node scripts/update-taiwan-stocks.js

  # 美股大型股更新
  update-us-large:
    name: 🇺🇸 更新美股大型股
    runs-on: ubuntu-latest
    if: github.event.schedule == '0 14 * * 1-5' || github.event.inputs.update_type == 'all' || github.event.inputs.update_type == 'us-large'

    steps:
    - name: 📥 檢出代碼
      uses: actions/checkout@v4

    - name: 🟢 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 📦 安裝依賴
      run: npm ci

    - name: 🏢 執行美股大型股更新
      env:
        SUPABASE_URL: ${{ secrets.EXPO_PUBLIC_SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
        ALPHA_VANTAGE_API_KEY: ${{ secrets.ALPHA_VANTAGE_API_KEY }}
        BATCH_TYPE: 'LARGE'
      run: node scripts/update-us-stocks.js

  # 美股中型股更新
  update-us-mid:
    name: 🇺🇸 更新美股中型股
    runs-on: ubuntu-latest
    if: github.event.schedule == '15 14 * * 1-5' || github.event.inputs.update_type == 'all' || github.event.inputs.update_type == 'us-mid'

    steps:
    - name: 📥 檢出代碼
      uses: actions/checkout@v4

    - name: 🟢 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 📦 安裝依賴
      run: npm ci

    - name: 🏪 執行美股中型股更新
      env:
        SUPABASE_URL: ${{ secrets.EXPO_PUBLIC_SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
        ALPHA_VANTAGE_API_KEY: ${{ secrets.ALPHA_VANTAGE_API_KEY }}
        BATCH_TYPE: 'MID'
      run: node scripts/update-us-stocks.js

  # 美股ETF更新
  update-us-etf:
    name: 🇺🇸 更新美股ETF
    runs-on: ubuntu-latest
    if: github.event.schedule == '30 14 * * 1-5' || github.event.inputs.update_type == 'all' || github.event.inputs.update_type == 'us-etf'

    steps:
    - name: 📥 檢出代碼
      uses: actions/checkout@v4

    - name: 🟢 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 📦 安裝依賴
      run: npm ci

    - name: 📊 執行美股ETF更新
      env:
        SUPABASE_URL: ${{ secrets.EXPO_PUBLIC_SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
        ALPHA_VANTAGE_API_KEY: ${{ secrets.ALPHA_VANTAGE_API_KEY }}
        BATCH_TYPE: 'ETF'
      run: node scripts/update-us-stocks.js
