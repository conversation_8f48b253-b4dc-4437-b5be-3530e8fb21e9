name: 📈 每日股票和匯率更新

on:
  schedule:
    # 匯率更新：每個工作日早上9點 (UTC+8 = UTC 1點)
    - cron: '0 1 * * 1-5'
    # 台股更新：每個工作日下午3點 (UTC+8 = UTC 7點)
    - cron: '0 7 * * 1-5'
    # 美股更新：每個工作日晚上10點 (UTC+8 = UTC 14點)
    - cron: '0 14 * * 1-5'

  # 手動觸發
  workflow_dispatch:
    inputs:
      update_type:
        description: '選擇更新類型'
        required: true
        default: 'all'
        type: choice
        options:
        - all
        - taiwan-stocks
        - us-stocks
        - exchange-rates

jobs:
  # 匯率更新 Job
  update-exchange-rates:
    name: 💱 更新匯率
    runs-on: ubuntu-latest
    if: github.event.schedule == '0 1 * * 1-5' || github.event.inputs.update_type == 'all' || github.event.inputs.update_type == 'exchange-rates'

    steps:
    - name: 📥 檢出代碼
      uses: actions/checkout@v4

    - name: 🟢 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 📦 安裝依賴
      run: npm ci

    - name: 💱 執行匯率更新
      env:
        SUPABASE_URL: ${{ secrets.EXPO_PUBLIC_SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
      run: node scripts/update-exchange-rates.js

  # 台股更新 Job
  update-taiwan-stocks:
    name: 🇹🇼 更新台股價格
    runs-on: ubuntu-latest
    if: github.event.schedule == '0 7 * * 1-5' || github.event.inputs.update_type == 'all' || github.event.inputs.update_type == 'taiwan-stocks'
    
    steps:
    - name: 📥 檢出代碼
      uses: actions/checkout@v4

    - name: 🟢 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 📦 安裝依賴
      run: npm ci

    - name: 🇹🇼 執行台股更新
      env:
        SUPABASE_URL: ${{ secrets.EXPO_PUBLIC_SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
        FINMIND_TOKEN: ${{ secrets.FINMIND_TOKEN }}
      run: node scripts/update-taiwan-stocks.js

  # 美股更新 Job
  update-us-stocks:
    name: 🇺🇸 更新美股價格
    runs-on: ubuntu-latest
    if: github.event.schedule == '0 14 * * 1-5' || github.event.inputs.update_type == 'all' || github.event.inputs.update_type == 'us-stocks'

    steps:
    - name: 📥 檢出代碼
      uses: actions/checkout@v4

    - name: 🟢 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 📦 安裝依賴
      run: npm ci

    - name: 🇺🇸 執行美股更新
      env:
        SUPABASE_URL: ${{ secrets.EXPO_PUBLIC_SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
        ALPHA_VANTAGE_API_KEY: ${{ secrets.ALPHA_VANTAGE_API_KEY }}
      run: node scripts/update-us-stocks.js
