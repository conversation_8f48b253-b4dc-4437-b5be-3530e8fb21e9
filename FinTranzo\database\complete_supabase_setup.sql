-- =====================================================
-- FinTranzo 完整 Supabase 資料庫設定
-- 包含台股資料表和匯率功能
-- 執行日期: 2025-06-01
-- =====================================================

-- =====================================================
-- 1. 台股資料表設定 (如果尚未建立)
-- =====================================================

-- 建立市場類型枚舉
DO $$ BEGIN
    CREATE TYPE market_type AS ENUM ('TSE', 'OTC', 'ETF');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 建立台股資料表
CREATE TABLE IF NOT EXISTS taiwan_stocks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    code VARCHAR(10) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    market_type market_type NOT NULL,
    closing_price DECIMAL(10,2),
    volume BIGINT,
    change_amount DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    price_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 台股索引
CREATE INDEX IF NOT EXISTS idx_taiwan_stocks_code ON taiwan_stocks(code);
CREATE INDEX IF NOT EXISTS idx_taiwan_stocks_name ON taiwan_stocks(name);
CREATE INDEX IF NOT EXISTS idx_taiwan_stocks_market_type ON taiwan_stocks(market_type);
CREATE INDEX IF NOT EXISTS idx_taiwan_stocks_price_date ON taiwan_stocks(price_date DESC);
CREATE INDEX IF NOT EXISTS idx_taiwan_stocks_code_search ON taiwan_stocks(code varchar_pattern_ops);
CREATE INDEX IF NOT EXISTS idx_taiwan_stocks_name_search ON taiwan_stocks(name varchar_pattern_ops);

-- =====================================================
-- 2. 匯率資料表設定
-- =====================================================

-- 建立匯率資料表
CREATE TABLE IF NOT EXISTS exchange_rates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    date DATE NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    cash_buy DECIMAL(8,3) NOT NULL,
    cash_sell DECIMAL(8,3) NOT NULL,
    spot_buy DECIMAL(8,3) NOT NULL,
    spot_sell DECIMAL(8,3) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 匯率索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_exchange_rates_date_currency 
ON exchange_rates(date, currency);

CREATE INDEX IF NOT EXISTS idx_exchange_rates_date 
ON exchange_rates(date DESC);

CREATE INDEX IF NOT EXISTS idx_exchange_rates_currency 
ON exchange_rates(currency);

-- =====================================================
-- 3. 更新時間觸發器
-- =====================================================

-- 台股更新時間觸發器
CREATE OR REPLACE FUNCTION update_taiwan_stocks_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_taiwan_stocks_updated_at ON taiwan_stocks;
CREATE TRIGGER trigger_update_taiwan_stocks_updated_at
    BEFORE UPDATE ON taiwan_stocks
    FOR EACH ROW
    EXECUTE FUNCTION update_taiwan_stocks_updated_at();

-- 匯率更新時間觸發器
CREATE OR REPLACE FUNCTION update_exchange_rates_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_exchange_rates_updated_at ON exchange_rates;
CREATE TRIGGER trigger_update_exchange_rates_updated_at
    BEFORE UPDATE ON exchange_rates
    FOR EACH ROW
    EXECUTE FUNCTION update_exchange_rates_updated_at();

-- =====================================================
-- 4. 台股查詢函數
-- =====================================================

-- 先刪除現有函數 (如果存在)
DROP FUNCTION IF EXISTS search_stocks(text, market_type, integer);
DROP FUNCTION IF EXISTS get_stock_stats();
DROP FUNCTION IF EXISTS get_latest_exchange_rate(varchar);
DROP FUNCTION IF EXISTS get_exchange_rate_by_date(date, varchar);
DROP FUNCTION IF EXISTS convert_usd_to_twd(decimal, decimal);
DROP FUNCTION IF EXISTS convert_twd_to_usd(decimal, decimal);
DROP FUNCTION IF EXISTS upsert_exchange_rate(date, varchar, decimal, decimal, decimal, decimal);

-- 搜尋股票函數
CREATE OR REPLACE FUNCTION search_stocks(
    search_term TEXT,
    market_filter market_type DEFAULT NULL,
    limit_count INTEGER DEFAULT 50
)
RETURNS TABLE (
    code VARCHAR(10),
    name VARCHAR(100),
    market_type market_type,
    closing_price DECIMAL(10,2),
    change_percent DECIMAL(5,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.code, s.name, s.market_type, s.closing_price, s.change_percent
    FROM taiwan_stocks s
    WHERE 
        (market_filter IS NULL OR s.market_type = market_filter)
        AND (s.code ILIKE '%' || search_term || '%' OR s.name ILIKE '%' || search_term || '%')
    ORDER BY s.volume DESC NULLS LAST
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 獲取股票統計函數
CREATE OR REPLACE FUNCTION get_stock_stats()
RETURNS TABLE (
    total_stocks BIGINT,
    tse_count BIGINT,
    otc_count BIGINT,
    etf_count BIGINT,
    last_updated DATE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_stocks,
        COUNT(*) FILTER (WHERE market_type = 'TSE') as tse_count,
        COUNT(*) FILTER (WHERE market_type = 'OTC') as otc_count,
        COUNT(*) FILTER (WHERE market_type = 'ETF') as etf_count,
        MAX(price_date) as last_updated
    FROM taiwan_stocks;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. 匯率查詢函數
-- =====================================================

-- 獲取最新匯率函數
CREATE OR REPLACE FUNCTION get_latest_exchange_rate(
    target_currency VARCHAR(3) DEFAULT 'USD'
)
RETURNS TABLE (
    date DATE,
    currency VARCHAR(3),
    cash_buy DECIMAL(8,3),
    cash_sell DECIMAL(8,3),
    spot_buy DECIMAL(8,3),
    spot_sell DECIMAL(8,3)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        er.date,
        er.currency,
        er.cash_buy,
        er.cash_sell,
        er.spot_buy,
        er.spot_sell
    FROM exchange_rates er
    WHERE er.currency = target_currency
    ORDER BY er.date DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- 獲取指定日期匯率函數
CREATE OR REPLACE FUNCTION get_exchange_rate_by_date(
    target_date DATE,
    target_currency VARCHAR(3) DEFAULT 'USD'
)
RETURNS TABLE (
    date DATE,
    currency VARCHAR(3),
    cash_buy DECIMAL(8,3),
    cash_sell DECIMAL(8,3),
    spot_buy DECIMAL(8,3),
    spot_sell DECIMAL(8,3)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        er.date,
        er.currency,
        er.cash_buy,
        er.cash_sell,
        er.spot_buy,
        er.spot_sell
    FROM exchange_rates er
    WHERE er.currency = target_currency
        AND er.date <= target_date
    ORDER BY er.date DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 6. 匯率轉換函數
-- =====================================================

-- 美元轉台幣函數
CREATE OR REPLACE FUNCTION convert_usd_to_twd(
    usd_amount DECIMAL(15,2),
    exchange_rate DECIMAL(8,3) DEFAULT NULL
)
RETURNS DECIMAL(15,2) AS $$
DECLARE
    rate DECIMAL(8,3);
BEGIN
    IF exchange_rate IS NULL THEN
        SELECT spot_sell INTO rate
        FROM get_latest_exchange_rate('USD')
        LIMIT 1;
        
        IF rate IS NULL THEN
            rate := 31.3;
        END IF;
    ELSE
        rate := exchange_rate;
    END IF;
    
    RETURN usd_amount * rate;
END;
$$ LANGUAGE plpgsql;

-- 台幣轉美元函數
CREATE OR REPLACE FUNCTION convert_twd_to_usd(
    twd_amount DECIMAL(15,2),
    exchange_rate DECIMAL(8,3) DEFAULT NULL
)
RETURNS DECIMAL(15,2) AS $$
DECLARE
    rate DECIMAL(8,3);
BEGIN
    IF exchange_rate IS NULL THEN
        SELECT spot_buy INTO rate
        FROM get_latest_exchange_rate('USD')
        LIMIT 1;
        
        IF rate IS NULL THEN
            rate := 31.2;
        END IF;
    ELSE
        rate := exchange_rate;
    END IF;
    
    RETURN twd_amount / rate;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 7. 資料插入/更新函數
-- =====================================================

-- 插入或更新匯率資料函數
CREATE OR REPLACE FUNCTION upsert_exchange_rate(
    target_date DATE,
    target_currency VARCHAR(3),
    cash_buy_rate DECIMAL(8,3),
    cash_sell_rate DECIMAL(8,3),
    spot_buy_rate DECIMAL(8,3),
    spot_sell_rate DECIMAL(8,3)
)
RETURNS UUID AS $$
DECLARE
    rate_id UUID;
BEGIN
    INSERT INTO exchange_rates (
        date, currency, cash_buy, cash_sell, spot_buy, spot_sell
    ) VALUES (
        target_date, target_currency, cash_buy_rate, cash_sell_rate, spot_buy_rate, spot_sell_rate
    )
    ON CONFLICT (date, currency) 
    DO UPDATE SET
        cash_buy = EXCLUDED.cash_buy,
        cash_sell = EXCLUDED.cash_sell,
        spot_buy = EXCLUDED.spot_buy,
        spot_sell = EXCLUDED.spot_sell,
        updated_at = NOW()
    RETURNING id INTO rate_id;
    
    RETURN rate_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 8. 視圖建立
-- =====================================================

-- 最新台股價格視圖
CREATE OR REPLACE VIEW latest_taiwan_stocks AS
SELECT
    code,
    name,
    market_type,
    closing_price,
    volume,
    change_amount,
    change_percent,
    price_date,
    updated_at
FROM taiwan_stocks
WHERE price_date = (SELECT MAX(price_date) FROM taiwan_stocks);

-- 最新匯率視圖
CREATE OR REPLACE VIEW latest_exchange_rates AS
SELECT DISTINCT ON (currency)
    currency,
    date,
    cash_buy,
    cash_sell,
    spot_buy,
    spot_sell,
    updated_at
FROM exchange_rates
ORDER BY currency, date DESC;

-- 匯率統計視圖
CREATE OR REPLACE VIEW exchange_rate_stats AS
SELECT
    currency,
    COUNT(*) as total_records,
    MIN(date) as earliest_date,
    MAX(date) as latest_date,
    AVG(spot_buy) as avg_spot_buy,
    AVG(spot_sell) as avg_spot_sell,
    MIN(spot_buy) as min_spot_buy,
    MAX(spot_buy) as max_spot_buy
FROM exchange_rates
GROUP BY currency;

-- =====================================================
-- 9. RLS (Row Level Security) 政策
-- =====================================================

-- 台股表 RLS
ALTER TABLE taiwan_stocks ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Allow public read access" ON taiwan_stocks;
CREATE POLICY "Allow public read access" ON taiwan_stocks
    FOR SELECT USING (true);

DROP POLICY IF EXISTS "Allow service role write access" ON taiwan_stocks;
CREATE POLICY "Allow service role write access" ON taiwan_stocks
    FOR ALL USING (auth.role() = 'service_role');

-- 匯率表 RLS
ALTER TABLE exchange_rates ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Allow public read access" ON exchange_rates;
CREATE POLICY "Allow public read access" ON exchange_rates
    FOR SELECT USING (true);

DROP POLICY IF EXISTS "Allow service role write access" ON exchange_rates;
CREATE POLICY "Allow service role write access" ON exchange_rates
    FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- 10. 初始測試資料
-- =====================================================

-- 插入台股測試資料 (如果不存在)
INSERT INTO taiwan_stocks (code, name, market_type, closing_price, volume, price_date) VALUES
('2330', '台積電', 'TSE', 967.00, 50000000, '2025-06-01'),
('2317', '鴻海', 'TSE', 156.00, 30000000, '2025-06-01'),
('2454', '聯發科', 'TSE', 1200.00, 15000000, '2025-06-01'),
('0050', '元大台灣50', 'ETF', 179.75, 8000000, '2025-06-01'),
('0056', '元大高股息', 'ETF', 34.08, 25000000, '2025-06-01'),
('8446', '華研', 'OTC', 156.00, 2000000, '2025-06-01'),
('3293', '鈊象', 'OTC', 780.00, 1500000, '2025-06-01')
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    market_type = EXCLUDED.market_type,
    closing_price = EXCLUDED.closing_price,
    volume = EXCLUDED.volume,
    price_date = EXCLUDED.price_date,
    updated_at = NOW();

-- 插入匯率測試資料 (2025-06-01 及前幾天)
INSERT INTO exchange_rates (date, currency, cash_buy, cash_sell, spot_buy, spot_sell) VALUES
('2025-06-01', 'USD', 31.000, 31.500, 31.200, 31.300),
('2025-05-31', 'USD', 30.950, 31.450, 31.150, 31.250),
('2025-05-30', 'USD', 31.100, 31.600, 31.300, 31.400),
('2025-05-29', 'USD', 31.050, 31.550, 31.250, 31.350),
('2025-05-28', 'USD', 30.900, 31.400, 31.100, 31.200)
ON CONFLICT (date, currency) DO UPDATE SET
    cash_buy = EXCLUDED.cash_buy,
    cash_sell = EXCLUDED.cash_sell,
    spot_buy = EXCLUDED.spot_buy,
    spot_sell = EXCLUDED.spot_sell,
    updated_at = NOW();

-- =====================================================
-- 11. 註解說明
-- =====================================================

COMMENT ON TABLE taiwan_stocks IS '台股資料表，儲存上市、上櫃、ETF股票資訊';
COMMENT ON COLUMN taiwan_stocks.code IS '股票代號';
COMMENT ON COLUMN taiwan_stocks.name IS '股票名稱';
COMMENT ON COLUMN taiwan_stocks.market_type IS '市場類型: TSE(上市), OTC(上櫃), ETF';
COMMENT ON COLUMN taiwan_stocks.closing_price IS '收盤價';
COMMENT ON COLUMN taiwan_stocks.volume IS '成交量';

COMMENT ON TABLE exchange_rates IS '匯率資料表，儲存美元兌台幣等匯率資訊';
COMMENT ON COLUMN exchange_rates.date IS '匯率日期';
COMMENT ON COLUMN exchange_rates.currency IS '幣別代碼 (如: USD)';
COMMENT ON COLUMN exchange_rates.cash_buy IS '現金買入價';
COMMENT ON COLUMN exchange_rates.cash_sell IS '現金賣出價';
COMMENT ON COLUMN exchange_rates.spot_buy IS '即期買入價';
COMMENT ON COLUMN exchange_rates.spot_sell IS '即期賣出價';

COMMENT ON FUNCTION search_stocks IS '搜尋股票函數，支援代號和名稱模糊搜尋';
COMMENT ON FUNCTION get_latest_exchange_rate IS '獲取指定幣別的最新匯率';
COMMENT ON FUNCTION convert_usd_to_twd IS '美元轉台幣轉換函數';
COMMENT ON FUNCTION convert_twd_to_usd IS '台幣轉美元轉換函數';

-- =====================================================
-- 12. 驗證查詢
-- =====================================================

-- 驗證台股資料
SELECT
    '台股資料驗證' as check_type,
    COUNT(*) as total_stocks,
    COUNT(*) FILTER (WHERE market_type = 'TSE') as tse_count,
    COUNT(*) FILTER (WHERE market_type = 'OTC') as otc_count,
    COUNT(*) FILTER (WHERE market_type = 'ETF') as etf_count
FROM taiwan_stocks;

-- 驗證匯率資料
SELECT
    '匯率資料驗證' as check_type,
    COUNT(*) as total_rates,
    MIN(date) as earliest_date,
    MAX(date) as latest_date
FROM exchange_rates;

-- 測試搜尋功能
SELECT '搜尋測試' as test_type, * FROM search_stocks('2330', NULL, 5);

-- 測試匯率功能
SELECT '匯率測試' as test_type, * FROM get_latest_exchange_rate('USD');

-- 測試轉換功能
SELECT
    '轉換測試' as test_type,
    convert_usd_to_twd(100.00) as usd_100_to_twd,
    convert_twd_to_usd(3120.00) as twd_3120_to_usd;

-- =====================================================
-- 設定完成
-- =====================================================

SELECT
    '🎉 FinTranzo 資料庫設定完成！' as status,
    NOW() as setup_time,
    '2025-06-01' as target_date;
