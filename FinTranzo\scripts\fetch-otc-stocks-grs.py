#!/usr/bin/env python3
"""
使用 grs 套件獲取完整上櫃股票資料
參考：https://github.com/toomore/grs
"""

import os
import sys
import json
import time
from datetime import datetime
from typing import List, Dict, Any

try:
    from grs import RealtimeOTC
    import requests
except ImportError as e:
    print(f"❌ 缺少必要套件: {e}")
    print("請執行: pip install grs requests")
    sys.exit(1)

# Supabase 配置
SUPABASE_URL = os.getenv('EXPO_PUBLIC_SUPABASE_URL')
SUPABASE_SERVICE_KEY = os.getenv('SUPABASE_SERVICE_ROLE_KEY')

if not SUPABASE_URL or not SUPABASE_SERVICE_KEY:
    print("❌ 請設置 SUPABASE_URL 和 SUPABASE_SERVICE_ROLE_KEY 環境變數")
    sys.exit(1)

class OTCStockFetcher:
    def __init__(self):
        self.supabase_url = SUPABASE_URL
        self.supabase_key = SUPABASE_SERVICE_KEY
        self.session = requests.Session()
        self.session.headers.update({
            'apikey': self.supabase_key,
            'Authorization': f'Bearer {self.supabase_key}',
            'Content-Type': 'application/json',
        })

    def get_otc_stock_list(self) -> List[str]:
        """
        獲取上櫃股票代號清單
        這裡使用一些已知的上櫃股票代號，實際應用中可以從其他來源獲取完整清單
        """
        # 常見上櫃股票代號（這只是部分，實際上櫃股票有數百家）
        otc_stocks = [
            # 電子類
            '3293', '3707', '4966', '6415', '6446', '6472', '6547', '8044', '8069', '8299',
            # 生技醫療
            '4736', '6547', '6446', '6472',
            # 金融
            '5820', '5880',
            # 傳產
            '1240', '1259', '1264', '1336', '1565',
            # 其他
            '4994', '6488', '6591',
            # 更多上櫃股票代號
            '1777', '2230', '2633', '2719', '3081', '3152', '3163', '3167', '3178',
            '3188', '3202', '3205', '3211', '3217', '3224', '3228', '3230', '3234',
            '3236', '3252', '3259', '3264', '3265', '3268', '3272', '3276', '3284',
            '3285', '3287', '3288', '3289', '3290', '3291', '3294', '3297', '3298',
            '3303', '3306', '3310', '3313', '3317', '3322', '3324', '3325', '3332',
            '3339', '3346', '3354', '3357', '3362', '3363', '3372', '3374', '3379',
            '3388', '3390', '3392', '3394', '3402', '3426', '3428', '3431', '3434',
            '3437', '3441', '3444', '3450', '3455', '3465', '3466', '3479', '3484',
            '3489', '3490', '3491', '3492', '3498', '3499', '3508', '3511', '3512',
            '3516', '3520', '3521', '3522', '3523', '3526', '3527', '3529', '3530',
            '3531', '3532', '3533', '3535', '3537', '3540', '3541', '3545', '3548',
            '3550', '3551', '3552', '3555', '3556', '3557', '3558', '3559', '3560',
            '3561', '3562', '3563', '3564', '3565', '3566', '3567', '3568', '3570',
            '3571', '3572', '3573', '3574', '3575', '3576', '3577', '3578', '3579',
            '3580', '3581', '3583', '3584', '3585', '3587', '3588', '3591', '3592',
            '3593', '3594', '3596', '3597', '3598', '3599', '4102', '4103', '4104',
            '4105', '4106', '4107', '4108', '4109', '4111', '4112', '4113', '4114',
            '4115', '4116', '4117', '4119', '4120', '4121', '4123', '4124', '4125',
            '4126', '4127', '4128', '4129', '4130', '4131', '4132', '4133', '4134',
            '4135', '4137', '4138', '4139', '4141', '4142', '4144', '4145', '4147',
            '4148', '4150', '4152', '4153', '4154', '4155', '4156', '4157', '4158',
            '4160', '4161', '4162', '4163', '4164', '4165', '4167', '4168', '4169',
            '4171', '4173', '4174', '4175', '4176', '4177', '4178', '4180', '4183',
            '4184', '4185', '4188', '4190', '4192', '4194', '4195', '4198', '4205',
            '4207', '4208', '4712', '4714', '4716', '4717', '4718', '4720', '4721',
            '4722', '4725', '4726', '4728', '4729', '4730', '4731', '4732', '4733',
            '4735', '4737', '4739', '4741', '4742', '4743', '4744', '4745', '4747',
            '4748', '4754', '4755', '4760', '4762', '4763', '4764', '4766', '4767',
            '4768', '4770', '4771', '4772', '4773', '4774', '4775', '4776', '4777',
            '4780', '4781', '4782', '4783', '4784', '4785', '4786', '4787', '4788',
            '4789', '4790', '4791', '4794', '4795', '4796', '4803', '4804', '4806',
            '4807', '4903', '4904', '4905', '4906', '4907', '4908', '4909', '4911',
            '4912', '4913', '4915', '4916', '4919', '4920', '4921', '4922', '4923',
            '4924', '4925', '4927', '4928', '4930', '4931', '4932', '4933', '4934',
            '4935', '4939', '4940', '4942', '4943', '4944', '4945', '4946', '4947',
            '4950', '4952', '4953', '4956', '4958', '4960', '4961', '4962', '4967',
            '4968', '4969', '4971', '4972', '4973', '4974', '4975', '4976', '4977',
            '4979', '4980', '4981', '4984', '4985', '4987', '4989', '4991', '4995',
            '4996', '4997', '4999', '5007', '5203', '5206', '5209', '5210', '5211',
            '5212', '5213', '5214', '5215', '5216', '5217', '5220', '5221', '5222',
            '5223', '5224', '5225', '5226', '5227', '5228', '5229', '5230', '5231',
            '5232', '5234', '5236', '5237', '5243', '5244', '5245', '5246', '5247',
            '5248', '5249', '5250', '5251', '5252', '5253', '5254', '5255', '5258',
            '5259', '5264', '5265', '5269', '5274', '5276', '5278', '5283', '5284',
            '5285', '5287', '5288', '5289', '5291', '5292', '5294', '5296', '5297',
            '5299', '5302', '5304', '5306', '5309', '5310', '5312', '5314', '5315',
            '5316', '5317', '5318', '5321', '5324', '5328', '5340', '5344', '5345',
            '5346', '5347', '5348', '5349', '5351', '5353', '5355', '5356', '5357',
            '5358', '5359', '5364', '5371', '5381', '5383', '5388', '5392', '5398',
            '5403', '5410', '5425', '5426', '5432', '5438', '5439', '5443', '5450',
            '5452', '5460', '5464', '5465', '5468', '5469', '5471', '5474', '5475',
            '5481', '5483', '5484', '5487', '5488', '5489', '5490', '5491', '5493',
            '5498', '5508', '5511', '5512', '5514', '5515', '5516', '5519', '5521',
            '5522', '5525', '5529', '5530', '5531', '5533', '5534', '5536', '5538',
            '5541', '5543', '5546', '5547', '5548', '5550', '5551', '5552', '5553',
            '5554', '5555', '5556', '5557', '5558', '5559', '5560', '5561', '5562',
            '5563', '5564', '5566', '5567', '5568', '5569', '5570', '5571', '5572',
            '5574', '5575', '5576', '5577', '5578', '5579', '5580', '5581', '5582',
            '5583', '5584', '5586', '5587', '5588', '5589', '5590', '5591', '5592',
            '5593', '5594', '5596', '5597', '5598', '5599'
        ]
        
        print(f"📋 準備獲取 {len(otc_stocks)} 檔上櫃股票資料...")
        return otc_stocks

    def fetch_otc_stock_data(self, stock_code: str) -> Dict[str, Any]:
        """
        使用 grs 套件獲取單一上櫃股票資料
        """
        try:
            print(f"🔍 獲取 {stock_code} 資料...")
            
            # 使用 grs RealtimeOTC 獲取即時資料
            realtime_stock = RealtimeOTC(stock_code)
            
            if not realtime_stock.data:
                print(f"⚠️ {stock_code} 無資料")
                return None
                
            data = realtime_stock.data
            
            # 提取需要的資料
            stock_data = {
                'code': stock_code,
                'name': data.get('name', ''),
                'market_type': 'OTC',
                'closing_price': float(data.get('price', 0)),
                'price_date': datetime.now().strftime('%Y-%m-%d')
            }
            
            print(f"✅ {stock_code} {stock_data['name']}: NT${stock_data['closing_price']}")
            return stock_data
            
        except Exception as e:
            print(f"❌ 獲取 {stock_code} 失敗: {e}")
            return None

    def batch_fetch_otc_stocks(self) -> List[Dict[str, Any]]:
        """
        批量獲取上櫃股票資料
        """
        stock_codes = self.get_otc_stock_list()
        otc_stocks = []
        
        print(f"🚀 開始批量獲取上櫃股票資料...")
        
        for i, stock_code in enumerate(stock_codes, 1):
            try:
                stock_data = self.fetch_otc_stock_data(stock_code)
                if stock_data:
                    otc_stocks.append(stock_data)
                
                # 避免 API 限制，每 10 筆暫停 1 秒
                if i % 10 == 0:
                    print(f"📊 已處理 {i}/{len(stock_codes)} 檔，暫停 1 秒...")
                    time.sleep(1)
                    
            except Exception as e:
                print(f"❌ 處理 {stock_code} 時發生錯誤: {e}")
                continue
        
        print(f"✅ 成功獲取 {len(otc_stocks)} 檔上櫃股票資料")
        return otc_stocks

    def update_supabase(self, stocks: List[Dict[str, Any]]) -> bool:
        """
        更新 Supabase 資料庫
        """
        try:
            print(f"💾 開始更新 Supabase 資料庫: {len(stocks)} 檔股票...")
            
            # 分批更新，每次 50 筆
            batch_size = 50
            success_count = 0
            
            for i in range(0, len(stocks), batch_size):
                batch = stocks[i:i + batch_size]
                batch_num = i // batch_size + 1
                total_batches = (len(stocks) + batch_size - 1) // batch_size
                
                print(f"📦 更新第 {batch_num}/{total_batches} 批，共 {len(batch)} 筆...")
                
                response = self.session.post(
                    f"{self.supabase_url}/rest/v1/taiwan_stocks",
                    json=batch,
                    params={'on_conflict': 'code'}
                )
                
                if response.status_code in [200, 201]:
                    success_count += len(batch)
                    print(f"✅ 第 {batch_num} 批更新成功")
                else:
                    print(f"❌ 第 {batch_num} 批更新失敗: {response.status_code} {response.text}")
                
                # 避免 API 限制
                time.sleep(0.5)
            
            print(f"🎉 資料庫更新完成！成功: {success_count} 檔")
            return True
            
        except Exception as e:
            print(f"❌ 更新資料庫失敗: {e}")
            return False

def main():
    """
    主要執行函數
    """
    print("🚀 開始使用 grs 套件獲取上櫃股票資料...")
    print(f"⏰ 開始時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    fetcher = OTCStockFetcher()
    
    try:
        # 獲取上櫃股票資料
        otc_stocks = fetcher.batch_fetch_otc_stocks()
        
        if not otc_stocks:
            print("⚠️ 沒有獲取到任何上櫃股票資料")
            return
        
        # 更新資料庫
        success = fetcher.update_supabase(otc_stocks)
        
        if success:
            print(f"🎉 上櫃股票資料更新完成！")
            print(f"📊 總計: {len(otc_stocks)} 檔上櫃股票")
        else:
            print("❌ 資料庫更新失敗")
            
    except Exception as e:
        print(f"❌ 執行失敗: {e}")
    
    print(f"⏰ 完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
