# 📱 FinTranzo 本地存儲清理指南

## 🎯 概述

FinTranzo 使用 AsyncStorage 在手機本地保存所有數據。有時您可能需要清理這些數據來重置應用或解決問題。

## 📊 存儲的數據類型

### 🔄 交易相關數據
- **交易記錄** (`@FinTranzo:transactions`) - 所有收入和支出記錄
- **類別設定** (`@FinTranzo:categories`) - 收入/支出類別
- **帳戶設定** (`@FinTranzo:accounts`) - 現金、銀行等帳戶
- **初始化標記** (`@FinTranzo:initialized`) - 應用初始化狀態

### 💰 資產負債數據
- **資產數據** (`@FinTranzo:assets`) - 資產負債表中的資產項目
- **負債數據** (`@FinTranzo:liabilities`) - 負債項目和還款記錄

### 👤 用戶數據
- **用戶檔案** (`@FinTranzo:userProfile`) - 用戶姓名等個人資料

### 🔄 其他數據
- **循環交易** (`@FinTranzo:recurringTransactions`) - 定期重複的交易設定
- **設定** (`@FinTranzo:settings`) - 應用設定
- **快取** (`@FinTranzo:cache`) - 臨時快取數據

## 🛠️ 清理方法

### 方法1：使用開發者工具（推薦）

在應用中使用內建的存儲管理工具：

```typescript
import { 
  clearAllStorage, 
  clearSpecificData, 
  checkStorageStatus 
} from './src/utils/storageManager';

// 檢查當前存儲狀態
await checkStorageStatus();

// 清除所有數據
await clearAllStorage();

// 清除特定類型的數據
await clearSpecificData('transactions'); // 清除交易數據
await clearSpecificData('assets');       // 清除資產負債數據
await clearSpecificData('user');         // 清除用戶資料
await clearSpecificData('recurring');    // 清除循環交易
```

### 方法2：使用清理腳本

運行專用的清理腳本：

```bash
# 在項目根目錄執行
node clear_storage.js
```

### 方法3：手動清理（高級用戶）

如果您熟悉 React Native 開發，可以手動清理：

```typescript
import AsyncStorage from '@react-native-async-storage/async-storage';

// 清除所有 FinTranzo 數據
const allKeys = await AsyncStorage.getAllKeys();
const finTranzoKeys = allKeys.filter(key => key.startsWith('@FinTranzo:'));
await AsyncStorage.multiRemove(finTranzoKeys);
```

## 🚨 注意事項

### ⚠️ 清理前的重要提醒
1. **數據無法恢復** - 清理後的數據無法恢復，請謹慎操作
2. **建議備份** - 清理前可以使用備份功能保存重要數據
3. **重啟應用** - 清理後需要完全重啟應用才能生效

### 💾 備份數據
在清理前，您可以備份數據：

```typescript
import { backupAllData, restoreFromBackup } from './src/utils/storageManager';

// 備份數據
const backupString = await backupAllData();
// 將 backupString 保存到文件或雲端

// 恢復數據
await restoreFromBackup(backupString);
```

## 🔧 常見使用場景

### 場景1：重置應用到初始狀態
```typescript
// 清除所有數據
await clearAllStorage();
// 重啟應用，將會重新初始化
```

### 場景2：清除測試數據，保留設定
```typescript
// 只清除交易和資產數據，保留用戶設定
await clearSpecificData('transactions');
await clearSpecificData('assets');
```

### 場景3：更換用戶
```typescript
// 清除用戶相關數據
await clearSpecificData('user');
await clearSpecificData('transactions');
await clearSpecificData('assets');
```

### 場景4：解決數據問題
```typescript
// 檢查存儲狀態
const status = await checkStorageStatus();
console.log('存儲狀態:', status);

// 如果發現問題，清除相關數據
if (status?.TRANSACTIONS && !status?.CATEGORIES) {
  await clearSpecificData('transactions');
}
```

## 📱 在應用中使用

您可以在應用的設定頁面添加清理功能：

```typescript
import { Alert } from 'react-native';
import { clearAllStorage, checkStorageStatus } from '../utils/storageManager';

const handleClearAllData = () => {
  Alert.alert(
    '清除所有數據',
    '此操作將刪除所有交易記錄、資產數據和設定。此操作無法撤銷。',
    [
      { text: '取消', style: 'cancel' },
      { 
        text: '確認清除', 
        style: 'destructive',
        onPress: async () => {
          const success = await clearAllStorage();
          if (success) {
            Alert.alert('成功', '所有數據已清除，請重啟應用。');
          } else {
            Alert.alert('錯誤', '清除數據失敗，請稍後再試。');
          }
        }
      }
    ]
  );
};
```

## 🆘 故障排除

### 問題1：清理後應用仍顯示舊數據
**解決方案**：完全關閉應用並重新啟動

### 問題2：清理失敗
**解決方案**：
1. 檢查應用權限
2. 確保應用沒有在後台運行
3. 重啟設備後再試

### 問題3：部分數據沒有清除
**解決方案**：使用 `getAllFinTranzoKeys()` 檢查所有存儲鍵名，手動清除

## 📞 技術支援

如果您在清理本地存儲時遇到問題，請：

1. 檢查控制台日誌中的錯誤信息
2. 使用 `checkStorageStatus()` 確認當前狀態
3. 嘗試重啟應用和設備
4. 如果問題持續，請聯繫開發團隊

---

**⚠️ 重要提醒：清理本地存儲會永久刪除所有數據，請在操作前確保您已經備份了重要信息！**
