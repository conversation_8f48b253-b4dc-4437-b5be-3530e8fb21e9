/**
 * 強制清除緩存並重新啟動應用程序
 */

const { spawn, exec } = require('child_process');
const path = require('path');

console.log('🔄 ===== 強制清除緩存並重新啟動應用程序 =====\n');

// 確保在正確的目錄中
process.chdir(__dirname);
console.log('📁 當前工作目錄:', __dirname);

async function clearCacheAndRestart() {
  try {
    console.log('1️⃣ 停止現有的 Expo 進程...');
    
    // 在 Windows 上停止 Expo 進程
    await new Promise((resolve) => {
      exec('taskkill /F /IM node.exe /T', (error) => {
        // 忽略錯誤，因為可能沒有運行的進程
        console.log('✅ 已嘗試停止現有進程');
        resolve();
      });
    });

    // 等待一下確保進程完全停止
    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log('\n2️⃣ 清除 Metro bundler 緩存...');
    
    // 清除 Metro 緩存
    await new Promise((resolve, reject) => {
      const clearCache = spawn('npx', ['expo', 'start', '--clear'], {
        stdio: 'pipe',
        shell: true,
        cwd: __dirname
      });

      let output = '';
      
      clearCache.stdout.on('data', (data) => {
        output += data.toString();
        console.log(data.toString().trim());
        
        // 當看到服務器啟動的標誌時，停止進程
        if (output.includes('Metro waiting on') || output.includes('Expo DevTools')) {
          console.log('✅ Metro bundler 緩存已清除，服務器已啟動');
          clearCache.kill();
          resolve();
        }
      });

      clearCache.stderr.on('data', (data) => {
        console.error('stderr:', data.toString());
      });

      clearCache.on('close', (code) => {
        if (code === 0 || code === null) {
          resolve();
        } else {
          reject(new Error(`清除緩存失敗，退出碼: ${code}`));
        }
      });

      // 10秒後強制結束
      setTimeout(() => {
        clearCache.kill();
        resolve();
      }, 10000);
    });

    console.log('\n✅ 緩存清除完成！');
    console.log('\n📋 接下來請手動執行以下步驟:');
    console.log('1. 在瀏覽器中按 Ctrl+Shift+R 強制刷新頁面');
    console.log('2. 打開開發者工具 (F12)');
    console.log('3. 查看 Console 標籤中的日誌');
    console.log('4. 尋找以下關鍵字:');
    console.log('   - "🔥 修復1" 或 "🔥 修復2"');
    console.log('   - "月末日期調整"');
    console.log('   - "無需調整"');
    console.log('5. 創建一個新的負債來測試修復');

  } catch (error) {
    console.error('❌ 清除緩存時發生錯誤:', error);
    console.log('\n🛠️ 手動清除緩存步驟:');
    console.log('1. 停止當前的 Expo 進程 (Ctrl+C)');
    console.log('2. 運行: npx expo start --clear');
    console.log('3. 在瀏覽器中強制刷新 (Ctrl+Shift+R)');
  }
}

clearCacheAndRestart();
