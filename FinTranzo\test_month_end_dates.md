# 月末日期調整測試案例

## 測試場景

### 場景1：每月31號循環交易
**設定**：
- 開始日期：2024年1月31日
- 頻率：每月
- 描述：房租

**預期結果**：
- 1月31日：正常執行
- 2月29日：調整為2月最後一天（2024年是閏年）
- 3月31日：正常執行
- 4月30日：調整為4月最後一天
- 5月31日：正常執行
- 6月30日：調整為6月最後一天

### 場景2：每月30號循環交易
**設定**：
- 開始日期：2024年1月30日
- 頻率：每月
- 描述：信用卡還款

**預期結果**：
- 1月30日：正常執行
- 2月29日：調整為2月最後一天
- 3月30日：正常執行
- 4月30日：正常執行

### 場景3：每月29號循環交易
**設定**：
- 開始日期：2024年1月29日
- 頻率：每月
- 描述：薪水

**預期結果**：
- 1月29日：正常執行
- 2月29日：正常執行（2024年是閏年）
- 3月29日：正常執行
- 如果是2025年2月：調整為2月28日

### 場景4：每年2月29日（閏年處理）
**設定**：
- 開始日期：2024年2月29日
- 頻率：每年
- 描述：年度保險

**預期結果**：
- 2024年2月29日：正常執行
- 2025年2月28日：調整為非閏年的2月最後一天
- 2026年2月28日：調整為非閏年的2月最後一天
- 2028年2月29日：正常執行（下一個閏年）

## 實際測試步驟

1. **創建31號循環交易**：
   - 在月曆選擇1月31日
   - 創建每月循環交易
   - 檢查未來月份的日期標記

2. **檢查2月調整**：
   - 確認2月顯示在29日（或28日）
   - 確認沒有2月31日的標記

3. **檢查4月調整**：
   - 確認4月顯示在30日
   - 確認沒有4月31日的標記

4. **檢查控制台日誌**：
   - 應該看到日期調整的提示信息
   - 例如："日期調整: 原定31號，該月只有30天，調整為30號"

## 邊界情況

### 月份天數對照表
- 31天：1月、3月、5月、7月、8月、10月、12月
- 30天：4月、6月、9月、11月
- 28/29天：2月（平年28天，閏年29天）

### 閏年規則
- 能被4整除且不能被100整除
- 或者能被400整除
- 例如：2024年是閏年，2025年不是

## 用戶提示

當用戶選擇29、30、31號作為循環交易日期時，系統會顯示：
```
⚠️ 注意：當月份沒有此日期時，將自動調整為該月最後一天
```

這確保用戶了解可能的日期調整行為。
