# 台股資料系統使用指南

## 🎯 **系統特色**

✅ **完整資料覆蓋**: 上市 (TSE) + 上櫃 (OTC) + ETF  
✅ **即時更新**: 每個交易日自動更新  
✅ **高效能**: 批量處理 + 資料庫索引優化  
✅ **TypeScript**: 完整型別安全  
✅ **React Native 整合**: 專用 Hooks  

## 🚀 **快速開始**

### **1. 安裝依賴**
```bash
cd FinTranzo
npm install
```

### **2. 設置環境變數**
```bash
# .env
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### **3. 建立資料庫**
```bash
# 在 Supabase SQL Editor 執行
taiwan_stocks_enhanced_schema.sql
```

### **4. 首次資料獲取**
```bash
npm run fetch-stocks
```

## 📊 **資料結構**

### **taiwan_stocks 資料表**
```sql
code VARCHAR(10) PRIMARY KEY     -- 股票代號 (2330, 00878)
name VARCHAR(100)                -- 股票名稱
market_type market_type          -- TSE/OTC/ETF
closing_price DECIMAL(10,2)      -- 收盤價
opening_price DECIMAL(10,2)      -- 開盤價
highest_price DECIMAL(10,2)      -- 最高價
lowest_price DECIMAL(10,2)       -- 最低價
volume BIGINT                    -- 成交量
price_change DECIMAL(10,2)       -- 漲跌價差
change_percent DECIMAL(5,2)      -- 漲跌幅 (%)
price_date DATE                  -- 價格日期
```

### **市場類型**
- **TSE**: 台灣證券交易所 (上市)
- **OTC**: 櫃買中心 (上櫃)  
- **ETF**: 指數股票型基金

## 🔄 **自動更新機制**

### **GitHub Actions (推薦)**
- ⏰ **執行時間**: 每個交易日下午 3:30
- 🔄 **自動觸發**: 無需人工介入
- 📧 **錯誤通知**: 失敗時自動通知

### **手動執行**
```bash
# 立即更新所有股票資料
npm run fetch-stocks

# 使用 TypeScript 直接執行
npx tsx scripts/fetch-taiwan-stocks.ts
```

## 📱 **React Native 使用**

### **基本用法**
```typescript
import { useStocks } from '../hooks/useStocks';

function StockList() {
  const { stocks, loading, error } = useStocks({
    market_type: 'TSE',
    limit: 50
  });

  if (loading) return <Text>載入中...</Text>;
  if (error) return <Text>錯誤: {error}</Text>;

  return (
    <FlatList
      data={stocks}
      renderItem={({ item }) => (
        <View>
          <Text>{item.code} {item.name}</Text>
          <Text>NT$ {item.closing_price}</Text>
        </View>
      )}
    />
  );
}
```

### **進階篩選**
```typescript
// 搜尋股票
const { stocks } = useStockSearch('台積電');

// 熱門股票 (依成交量)
const { stocks } = usePopularStocks(20);

// 漲跌幅排行
const { stocks: gainers } = usePriceMovers('gainers', 10);
const { stocks: losers } = usePriceMovers('losers', 10);

// 特定市場
const { stocks: etfStocks } = useMarketStocks('ETF');
```

### **統計資料**
```typescript
const { stats, loading } = useStockStats();

// stats.total        - 總股票數
// stats.tse_count    - 上市股票數
// stats.otc_count    - 上櫃股票數  
// stats.etf_count    - ETF 數量
// stats.avg_price    - 平均價格
// stats.total_volume - 總成交量
```

## 🔍 **查詢範例**

### **SQL 查詢**
```sql
-- 獲取所有 ETF
SELECT * FROM taiwan_stocks WHERE market_type = 'ETF';

-- 價格區間篩選
SELECT * FROM taiwan_stocks 
WHERE closing_price BETWEEN 10 AND 100;

-- 漲跌幅排行
SELECT code, name, closing_price, change_percent 
FROM taiwan_stocks 
ORDER BY change_percent DESC 
LIMIT 10;

-- 成交量排行
SELECT code, name, volume 
FROM taiwan_stocks 
WHERE volume > 0 
ORDER BY volume DESC 
LIMIT 20;
```

### **使用視圖**
```sql
-- 市場統計
SELECT * FROM v_stock_summary;

-- 熱門股票
SELECT * FROM v_popular_stocks;

-- 漲跌幅排行
SELECT * FROM v_price_movers;
```

## ⚡ **效能優化**

### **資料庫索引**
- ✅ `market_type` 索引
- ✅ `price_date` 索引  
- ✅ `code + price_date` 複合索引
- ✅ 全文搜尋索引

### **批量處理**
- 📦 每批 100 檔股票
- ⏱️ 批次間延遲 100ms
- 🔄 自動重試機制

### **快取策略**
```typescript
// React Native 中使用 SWR 或 React Query
import useSWR from 'swr';

const { data: stocks } = useSWR(
  'taiwan-stocks',
  () => fetchStocks(),
  { refreshInterval: 300000 } // 5分鐘更新
);
```

## 🛠️ **故障排除**

### **常見問題**

**1. API 連線失敗**
```bash
# 檢查網路連線
curl -I https://openapi.twse.com.tw/v1/exchangeReport/STOCK_DAY_AVG_ALL

# 檢查環境變數
echo $EXPO_PUBLIC_SUPABASE_URL
```

**2. 資料庫連線錯誤**
```typescript
// 測試 Supabase 連線
const { data, error } = await supabase
  .from('taiwan_stocks')
  .select('count(*)')
  .single();
```

**3. 資料不完整**
```sql
-- 檢查各市場資料量
SELECT market_type, COUNT(*) 
FROM taiwan_stocks 
GROUP BY market_type;
```

### **日誌檢查**
```bash
# GitHub Actions 日誌
# 前往 GitHub > Actions > 最新執行記錄

# 本地執行日誌
npm run fetch-stocks 2>&1 | tee stock-update.log
```

## 📈 **監控指標**

### **成功指標**
- ✅ 上市股票 > 900 檔
- ✅ 上櫃股票 > 700 檔  
- ✅ ETF > 200 檔
- ✅ 更新時間 < 5 分鐘
- ✅ 錯誤率 < 1%

### **警告指標**
- ⚠️ 總股票數 < 1500 檔
- ⚠️ 更新時間 > 10 分鐘
- ⚠️ 連續失敗 > 2 次

## 🎯 **下一步計劃**

1. **✅ 完成**: 基礎架構建立
2. **🔄 進行中**: 自動化部署
3. **📋 待辦**: 
   - 歷史價格追蹤
   - 技術指標計算
   - 即時推播通知
   - 自選股功能

---

🎉 **恭喜！您現在擁有完整的台股資料系統！**

包含 **上市 + 上櫃 + ETF** 的完整資料，每日自動更新，完美整合到您的 FinTranzo 應用程式中！🚀
