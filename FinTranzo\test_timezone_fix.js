/**
 * 測試時區修復
 * 驗證日期創建是否正確避免了時區問題
 */

console.log('🕐 ===== 測試時區修復 =====\n');

// 測試不同的日期創建方法
function testDateCreationMethods() {
  console.log('📅 測試不同的日期創建方法:');
  
  const year = 2025;
  const month = 4; // 5月 (0-based)
  const day = 31;
  
  // 方法1：原始方法（可能有時區問題）
  const date1 = new Date(year, month, day);
  console.log('\n方法1 - 原始方法:');
  console.log(`new Date(${year}, ${month}, ${day})`);
  console.log(`本地時間: ${date1.toLocaleDateString('zh-TW')} ${date1.toLocaleTimeString('zh-TW')}`);
  console.log(`ISO字符串: ${date1.toISOString()}`);
  console.log(`split('T')[0]: ${date1.toISOString().split('T')[0]}`);
  console.log(`getDate(): ${date1.getDate()}號`);
  
  // 方法2：修復後的方法（設定中午12點）
  const date2 = new Date(year, month, day, 12, 0, 0, 0);
  console.log('\n方法2 - 修復後方法:');
  console.log(`new Date(${year}, ${month}, ${day}, 12, 0, 0, 0)`);
  console.log(`本地時間: ${date2.toLocaleDateString('zh-TW')} ${date2.toLocaleTimeString('zh-TW')}`);
  console.log(`ISO字符串: ${date2.toISOString()}`);
  console.log(`split('T')[0]: ${date2.toISOString().split('T')[0]}`);
  console.log(`getDate(): ${date2.getDate()}號`);
  
  // 比較結果
  console.log('\n🔍 比較結果:');
  const iso1 = date1.toISOString().split('T')[0];
  const iso2 = date2.toISOString().split('T')[0];
  
  console.log(`原始方法 ISO 日期: ${iso1}`);
  console.log(`修復方法 ISO 日期: ${iso2}`);
  console.log(`是否相同: ${iso1 === iso2 ? '是' : '否'}`);
  
  // 檢查日期是否正確
  const expectedIsoDate = '2025-05-31';
  console.log(`期望的 ISO 日期: ${expectedIsoDate}`);
  console.log(`原始方法正確: ${iso1 === expectedIsoDate ? '✅' : '❌'}`);
  console.log(`修復方法正確: ${iso2 === expectedIsoDate ? '✅' : '❌'}`);
  
  return {
    original: { date: date1, iso: iso1, correct: iso1 === expectedIsoDate },
    fixed: { date: date2, iso: iso2, correct: iso2 === expectedIsoDate }
  };
}

const dateTestResult = testDateCreationMethods();

// 測試日曆標記邏輯
function testCalendarMarkingWithFix() {
  console.log('\n🗓️ ===== 測試修復後的日曆標記邏輯 =====');
  
  // 使用修復後的方法創建交易
  const paymentDate = new Date(2025, 4, 31, 12, 0, 0, 0); // 5月31日中午12點
  
  const transaction = {
    id: 'test_payment',
    amount: 10000,
    type: 'expense',
    description: '測試信用卡',
    category: '還款',
    account: '銀行',
    date: paymentDate.toISOString()
  };
  
  console.log('💰 創建的交易:');
  console.log(`描述: ${transaction.description}`);
  console.log(`金額: ${transaction.amount}`);
  console.log(`日期 (ISO): ${transaction.date}`);
  console.log(`日期 (本地): ${new Date(transaction.date).toLocaleDateString('zh-TW')}`);
  
  // 模擬日曆標記邏輯
  const dateKey = transaction.date.split('T')[0];
  const actualDay = new Date(transaction.date).getDate();
  
  console.log('\n📅 日曆標記分析:');
  console.log(`日期鍵 (用於標記): ${dateKey}`);
  console.log(`實際日期: ${actualDay}號`);
  console.log(`期望日期鍵: 2025-05-31`);
  console.log(`標記正確: ${dateKey === '2025-05-31' ? '✅' : '❌'}`);
  
  return {
    transaction,
    dateKey,
    actualDay,
    isCorrect: dateKey === '2025-05-31' && actualDay === 31
  };
}

const calendarTestResult = testCalendarMarkingWithFix();

// 測試不同時區的影響
function testTimezoneImpact() {
  console.log('\n🌍 ===== 測試時區影響 =====');
  
  console.log('當前時區信息:');
  console.log(`時區偏移: ${new Date().getTimezoneOffset()} 分鐘`);
  console.log(`時區名稱: ${Intl.DateTimeFormat().resolvedOptions().timeZone}`);
  
  // 測試邊界情況
  const testCases = [
    { hour: 0, minute: 0, description: '午夜' },
    { hour: 12, minute: 0, description: '中午' },
    { hour: 23, minute: 59, description: '深夜' }
  ];
  
  console.log('\n⏰ 不同時間點的測試:');
  testCases.forEach(testCase => {
    const date = new Date(2025, 4, 31, testCase.hour, testCase.minute, 0, 0);
    const iso = date.toISOString().split('T')[0];
    const day = date.getDate();
    
    console.log(`${testCase.description} (${testCase.hour}:${testCase.minute.toString().padStart(2, '0')})`);
    console.log(`  ISO 日期: ${iso}`);
    console.log(`  實際日期: ${day}號`);
    console.log(`  正確性: ${iso === '2025-05-31' && day === 31 ? '✅' : '❌'}`);
  });
}

testTimezoneImpact();

// 總結測試結果
console.log('\n📊 ===== 測試結果總結 =====');

const originalCorrect = dateTestResult.original.correct;
const fixedCorrect = dateTestResult.fixed.correct;
const calendarCorrect = calendarTestResult.isCorrect;

console.log('1. 日期創建測試:');
console.log(`   原始方法: ${originalCorrect ? '✅ 正確' : '❌ 錯誤'}`);
console.log(`   修復方法: ${fixedCorrect ? '✅ 正確' : '❌ 錯誤'}`);

console.log('\n2. 日曆標記測試:');
console.log(`   標記正確性: ${calendarCorrect ? '✅ 正確' : '❌ 錯誤'}`);

console.log('\n3. 修復效果:');
if (fixedCorrect && calendarCorrect) {
  console.log('🎉 時區修復成功！');
  console.log('✅ 5月31號現在會正確顯示為31號');
  console.log('✅ 日曆標記會正確標記在31號');
} else {
  console.log('⚠️ 修復可能不完全成功');
  if (!fixedCorrect) {
    console.log('❌ 日期創建仍有問題');
  }
  if (!calendarCorrect) {
    console.log('❌ 日曆標記仍有問題');
  }
}

console.log('\n🛠️ 下一步操作:');
console.log('1. 清除瀏覽器緩存');
console.log('2. 重新啟動應用程序');
console.log('3. 創建新的負債測試');
console.log('4. 檢查日曆上的31號是否有正確標記');

console.log('\n✨ 時區修復測試完成！');
