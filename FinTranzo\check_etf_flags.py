#!/usr/bin/env python3
"""
檢查ETF標籤設置
"""

import requests

# Supabase配置
SUPABASE_URL = "https://yrryyapzkgrsahranzvo.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlycnl5YXB6a2dyc2FocmFuenZvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODE3MzYzNSwiZXhwIjoyMDYzNzQ5NjM1fQ.xjiXVagzKgBBYXqtRjJiXwCgbRdEW0EpFzCIlJ26SSQ"

def make_supabase_request(endpoint, method='GET', data=None):
    """發送Supabase請求"""
    url = f"{SUPABASE_URL}/rest/v1/{endpoint}"
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'application/json',
        'Prefer': 'return=representation'
    }
    
    try:
        if method == 'GET':
            response = requests.get(url, headers=headers)
        elif method == 'POST':
            response = requests.post(url, headers=headers, json=data)
        elif method == 'PATCH':
            response = requests.patch(url, headers=headers, json=data)
        
        response.raise_for_status()
        return response.json() if response.content else []
    except Exception as e:
        print(f"❌ 請求失敗: {str(e)}")
        return None

def check_specific_etfs():
    """檢查特定ETF的標籤設置"""
    etf_symbols = ['QQQ', 'QQQM', 'QQQI', 'SQQQ']
    
    print("🔍 檢查特定ETF的標籤設置:")
    print("=" * 60)
    
    for symbol in etf_symbols:
        stocks = make_supabase_request(f'us_stocks?symbol=eq.{symbol}&select=symbol,name,is_etf,asset_type,sector')
        
        if stocks:
            for stock in stocks:
                is_etf = stock.get('is_etf', False)
                asset_type = stock.get('asset_type', 'N/A')
                sector = stock.get('sector', 'N/A')
                
                print(f"\n📈 {symbol}:")
                print(f"   名稱: {stock['name']}")
                print(f"   is_etf: {is_etf} {'✅' if is_etf else '❌'}")
                print(f"   asset_type: {asset_type}")
                print(f"   sector: {sector}")
        else:
            print(f"\n📈 {symbol}: 沒有找到記錄")

def search_etfs_with_qqq():
    """搜索包含QQQ的ETF"""
    print("\n🔍 搜索包含'qqq'的股票:")
    print("=" * 60)
    
    # 使用搜索函數
    search_data = {
        'search_term': 'qqq',
        'limit_count': 10
    }
    
    # 搜索普通股票
    stocks = make_supabase_request('rpc/search_us_stocks', method='POST', data=search_data)
    print(f"\n📊 普通股票搜索結果 ({len(stocks) if stocks else 0} 個):")
    if stocks:
        for stock in stocks:
            is_etf = stock.get('is_etf', False)
            asset_type = stock.get('asset_type', 'N/A')
            print(f"   {stock['symbol']}: {stock['name']} - is_etf: {is_etf} - asset_type: {asset_type}")
    
    # 搜索ETF
    etfs = make_supabase_request('rpc/search_us_etf', method='POST', data=search_data)
    print(f"\n📊 ETF搜索結果 ({len(etfs) if etfs else 0} 個):")
    if etfs:
        for etf in etfs:
            is_etf = etf.get('is_etf', False)
            asset_type = etf.get('asset_type', 'N/A')
            print(f"   {etf['symbol']}: {etf['name']} - is_etf: {is_etf} - asset_type: {asset_type}")

def fix_etf_flags():
    """修復ETF標籤"""
    print("\n🔧 修復ETF標籤:")
    print("=" * 60)
    
    # 獲取所有應該是ETF但標籤不正確的記錄
    etf_symbols = ['QQQ', 'QQQM', 'QQQI', 'SQQQ', 'SPY', 'VOO', 'VTI', 'IWM']
    
    for symbol in etf_symbols:
        print(f"\n🔧 修復 {symbol}...")
        
        # 更新記錄
        update_data = {
            'is_etf': True,
            'asset_type': 'ETF'
        }
        
        result = make_supabase_request(f'us_stocks?symbol=eq.{symbol}', method='PATCH', data=update_data)
        
        if result:
            print(f"   ✅ 成功更新 {symbol}")
        else:
            print(f"   ❌ 更新 {symbol} 失敗")

def main():
    """主函數"""
    print("🔧 ETF標籤檢查和修復工具")
    print("=" * 60)
    
    # 1. 檢查特定ETF
    check_specific_etfs()
    
    # 2. 搜索測試
    search_etfs_with_qqq()
    
    # 3. 詢問是否修復
    choice = input("\n是否要修復ETF標籤？(y/N): ").strip().lower()
    if choice == 'y':
        fix_etf_flags()
        print("\n✅ ETF標籤修復完成！")
        
        # 重新檢查
        print("\n🔍 重新檢查修復結果:")
        check_specific_etfs()
    else:
        print("❌ 跳過修復")

if __name__ == "__main__":
    main()
