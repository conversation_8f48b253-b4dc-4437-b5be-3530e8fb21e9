/**
 * 測試實際問題的詳細腳本
 * 模擬用戶的實際操作流程
 */

console.log('🔍 開始測試實際問題...');

// 模擬負債添加流程
function simulateLiabilityAddition() {
  console.log('\n📝 模擬負債添加流程');
  console.log('='.repeat(50));
  
  // 模擬用戶輸入的負債數據
  const userInput = {
    name: '房貸',
    type: 'mortgage',
    balance: 1000000,
    monthly_payment: 10000,
    payment_account: '銀行',
    payment_day: 31,  // 用戶選擇31號
    payment_periods: 240
  };
  
  console.log('用戶輸入的負債數據:', userInput);
  
  // 模擬 AddLiabilityModal 的 handleSubmit 邏輯
  const liability = {
    id: Date.now().toString(),
    name: userInput.name,
    type: userInput.type,
    balance: userInput.balance,
    monthly_payment: userInput.monthly_payment,
    payment_account: userInput.payment_account,
    payment_day: userInput.payment_day,
    payment_periods: userInput.payment_periods,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
  
  console.log('創建的負債對象:', liability);
  
  return liability;
}

// 模擬負債同步到循環交易的流程
function simulateLiabilitySync(liability) {
  console.log('\n🔄 模擬負債同步到循環交易流程');
  console.log('='.repeat(50));
  
  // 檢查是否滿足循環交易條件
  const shouldCreateRecurring = liability.monthly_payment && 
                                liability.payment_account && 
                                liability.payment_day;
  
  console.log('是否滿足循環交易條件:', shouldCreateRecurring);
  
  if (!shouldCreateRecurring) {
    console.log('❌ 不滿足條件，不會創建循環交易');
    return null;
  }
  
  // 模擬 calculateStartDate 邏輯
  const today = new Date();
  const currentDay = today.getDate();
  const paymentDay = liability.payment_day;
  
  console.log('日期計算參數:', {
    today: today.toLocaleDateString('zh-TW'),
    currentDay,
    paymentDay
  });
  
  let startDate;
  
  if (currentDay < paymentDay) {
    // 從本月開始
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth();
    
    // 獲取當月的最後一天
    const lastDayOfCurrentMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
    
    // 根據月末調整邏輯確定實際還款日期
    const actualPaymentDay = paymentDay > lastDayOfCurrentMonth ? lastDayOfCurrentMonth : paymentDay;
    
    startDate = new Date(currentYear, currentMonth, actualPaymentDay);
    console.log('本月還款日未到，從本月開始:', {
      設定日期: paymentDay,
      實際日期: actualPaymentDay,
      結果: startDate.toLocaleDateString('zh-TW'),
      實際天數: startDate.getDate()
    });
  } else {
    // 從下個月開始
    const nextYear = today.getMonth() === 11 ? today.getFullYear() + 1 : today.getFullYear();
    const nextMonth = today.getMonth() === 11 ? 0 : today.getMonth() + 1;
    
    // 獲取下個月的最後一天
    const lastDayOfNextMonth = new Date(nextYear, nextMonth + 1, 0).getDate();
    
    // 根據月末調整邏輯確定實際還款日期
    const actualPaymentDay = paymentDay > lastDayOfNextMonth ? lastDayOfNextMonth : paymentDay;
    
    startDate = new Date(nextYear, nextMonth, actualPaymentDay);
    console.log('本月還款日已過，從下個月開始:', {
      設定日期: paymentDay,
      實際日期: actualPaymentDay,
      結果: startDate.toLocaleDateString('zh-TW'),
      實際天數: startDate.getDate()
    });
  }
  
  // 模擬創建循環交易
  const recurringTransaction = {
    id: `recurring_${Date.now()}`,
    amount: liability.monthly_payment,
    type: 'expense',
    description: liability.name,
    category: '還款',
    account: liability.payment_account,
    frequency: 'monthly',
    startDate: startDate,
    maxOccurrences: liability.payment_periods,
    original_target_day: liability.payment_day,
    is_active: true,
    created_at: new Date().toISOString()
  };
  
  console.log('創建的循環交易:', recurringTransaction);
  
  return recurringTransaction;
}

// 模擬當月交易記錄創建
function simulateCurrentMonthTransaction(liability) {
  console.log('\n💰 模擬當月交易記錄創建');
  console.log('='.repeat(50));
  
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();
  
  // 模擬 ensureCurrentMonthTransaction 邏輯
  const paymentDay = liability.payment_day || 1;
  
  // 獲取當月的最後一天
  const lastDayOfCurrentMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
  
  // 根據月末調整邏輯確定實際還款日期
  let actualPaymentDay;
  if (paymentDay > lastDayOfCurrentMonth) {
    actualPaymentDay = lastDayOfCurrentMonth;
    console.log(`📅 月末日期調整: 原定${paymentDay}號，${currentYear}年${currentMonth + 1}月只有${lastDayOfCurrentMonth}天，調整為${actualPaymentDay}號`);
  } else {
    actualPaymentDay = paymentDay;
    console.log(`📅 無需調整: ${paymentDay}號`);
  }
  
  const paymentDate = new Date(currentYear, currentMonth, actualPaymentDay);
  
  console.log(`📅 日期創建: 設定${paymentDay}號 -> 實際${actualPaymentDay}號 -> ${paymentDate.toLocaleDateString('zh-TW')} (${paymentDate.getDate()}號)`);
  
  const actualTransaction = {
    id: `ensure_debt_payment_${liability.id}_${Date.now()}`,
    amount: liability.monthly_payment,
    type: 'expense',
    description: liability.name,
    category: '還款',
    account: liability.payment_account,
    date: paymentDate.toISOString(),
    is_recurring: true,
    recurring_frequency: 'monthly',
    max_occurrences: liability.payment_periods,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
  
  console.log('創建的實際交易記錄:', actualTransaction);
  
  return actualTransaction;
}

// 檢查問題
function checkProblems(liability, recurringTransaction, actualTransaction) {
  console.log('\n🔍 檢查問題');
  console.log('='.repeat(50));
  
  const userSetDay = liability.payment_day;
  const actualDay = actualTransaction ? new Date(actualTransaction.date).getDate() : null;
  
  console.log('問題1檢查 - 日期邏輯:');
  console.log(`用戶設定: ${userSetDay}號`);
  console.log(`實際記錄: ${actualDay}號`);
  console.log(`是否有問題: ${userSetDay !== actualDay}`);
  
  if (userSetDay !== actualDay) {
    console.log('❌ 發現問題1：日期不匹配');
    console.log('可能原因：');
    console.log('- 月末調整邏輯有問題');
    console.log('- 日期創建邏輯有問題');
  } else {
    console.log('✅ 問題1：日期邏輯正確');
  }
  
  console.log('\n問題2檢查 - 交易記錄:');
  console.log(`是否創建了還款交易: ${!!actualTransaction}`);
  console.log(`交易類別: ${actualTransaction?.category}`);
  console.log(`交易金額: ${actualTransaction?.amount}`);
  
  if (!actualTransaction || actualTransaction.category !== '還款') {
    console.log('❌ 發現問題2：還款交易記錄有問題');
  } else {
    console.log('✅ 問題2：還款交易記錄正確');
  }
}

// 執行完整測試
function runCompleteTest() {
  console.log('🧪 執行完整測試流程');
  console.log('='.repeat(70));
  
  // 1. 模擬負債添加
  const liability = simulateLiabilityAddition();
  
  // 2. 模擬負債同步
  const recurringTransaction = simulateLiabilitySync(liability);
  
  // 3. 模擬當月交易記錄創建
  const actualTransaction = simulateCurrentMonthTransaction(liability);
  
  // 4. 檢查問題
  checkProblems(liability, recurringTransaction, actualTransaction);
  
  console.log('\n📊 測試總結');
  console.log('='.repeat(50));
  console.log('如果上述邏輯都正確，但用戶仍然遇到問題，可能的原因：');
  console.log('1. 代碼中的修復沒有正確部署');
  console.log('2. 事件監聽器沒有正確觸發');
  console.log('3. 數據同步有延遲');
  console.log('4. 頁面刷新機制有問題');
}

// 執行測試
runCompleteTest();

console.log('\n✅ 測試完成');
