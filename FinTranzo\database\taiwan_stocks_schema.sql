-- 台股資料庫架構 - 只保存最新一日收盤價
-- 每日自動更新，覆蓋前一日資料

-- 刪除舊表格（這會自動刪除相關的觸發器和依賴）
DROP TABLE IF EXISTS taiwan_stocks CASCADE;

-- 刪除所有舊的函數（避免衝突）
DROP FUNCTION IF EXISTS search_stocks(text) CASCADE;
DROP FUNCTION IF EXISTS get_stock_by_code(VARCHAR(10)) CASCADE;
DROP FUNCTION IF EXISTS update_daily_stock_prices(JSONB) CASCADE;
DROP FUNCTION IF EXISTS cleanup_old_stock_data() CASCADE;
DROP FUNCTION IF EXISTS update_taiwan_stocks_timestamp() CASCADE;

-- 建立台股資料表（簡化版本 - 只保存最新一日資料）
CREATE TABLE taiwan_stocks (
  code VARCHAR(10) PRIMARY KEY,                    -- 股票代號（主鍵）
  name VARCHAR(100) NOT NULL,                      -- 股票名稱
  closing_price DECIMAL(10,2) NOT NULL,            -- 收盤價
  price_date DATE NOT NULL,                        -- 價格日期
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 建立索引以提升查詢效能
CREATE INDEX idx_taiwan_stocks_name ON taiwan_stocks(name);
CREATE INDEX idx_taiwan_stocks_price_date ON taiwan_stocks(price_date);
CREATE INDEX idx_taiwan_stocks_code_name ON taiwan_stocks(code, name);

-- 建立更新時間觸發器
CREATE OR REPLACE FUNCTION update_taiwan_stocks_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_taiwan_stocks_timestamp
    BEFORE UPDATE ON taiwan_stocks
    FOR EACH ROW
    EXECUTE FUNCTION update_taiwan_stocks_timestamp();

-- 建立 RLS (Row Level Security) 政策
ALTER TABLE taiwan_stocks ENABLE ROW LEVEL SECURITY;

-- 允許所有人讀取台股資料
CREATE POLICY "公開讀取台股資料" ON taiwan_stocks
    FOR SELECT USING (true);

-- 只允許服務角色寫入資料（用於每日自動更新）
CREATE POLICY "服務角色寫入權限" ON taiwan_stocks
    FOR ALL USING (auth.role() = 'service_role');

-- 建立函數：批量更新股票資料（每日自動更新使用）
CREATE OR REPLACE FUNCTION update_daily_stock_prices(
    stock_data JSONB
)
RETURNS INTEGER AS $$
DECLARE
    stock_record JSONB;
    updated_count INTEGER := 0;
BEGIN
    -- 遍歷股票資料並更新
    FOR stock_record IN SELECT * FROM jsonb_array_elements(stock_data)
    LOOP
        -- 只處理有效的收盤價（避免空資料）
        IF (stock_record->>'closing_price') IS NOT NULL
           AND (stock_record->>'closing_price') != ''
           AND (stock_record->>'closing_price')::DECIMAL(10,2) > 0 THEN

            INSERT INTO taiwan_stocks (code, name, closing_price, price_date)
            VALUES (
                stock_record->>'code',
                stock_record->>'name',
                (stock_record->>'closing_price')::DECIMAL(10,2),
                (stock_record->>'date')::DATE
            )
            ON CONFLICT (code) DO UPDATE SET
                name = EXCLUDED.name,
                closing_price = EXCLUDED.closing_price,
                price_date = EXCLUDED.price_date,
                updated_at = NOW();

            updated_count := updated_count + 1;
        END IF;
    END LOOP;

    RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 建立函數：搜尋股票（支援代號或名稱模糊搜尋）
CREATE OR REPLACE FUNCTION search_stocks(search_term TEXT)
RETURNS TABLE(
    code VARCHAR(10),
    name VARCHAR(100),
    closing_price DECIMAL(10,2),
    price_date DATE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        ts.code,
        ts.name,
        ts.closing_price,
        ts.price_date
    FROM taiwan_stocks ts
    WHERE ts.code ILIKE search_term || '%'
       OR ts.name ILIKE '%' || search_term || '%'
    ORDER BY
        CASE
            WHEN ts.code ILIKE search_term || '%' THEN 1
            ELSE 2
        END,
        ts.code
    LIMIT 20;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 建立函數：獲取特定股票資料
CREATE OR REPLACE FUNCTION get_stock_by_code(stock_code VARCHAR(10))
RETURNS TABLE(
    code VARCHAR(10),
    name VARCHAR(100),
    closing_price DECIMAL(10,2),
    price_date DATE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        ts.code,
        ts.name,
        ts.closing_price,
        ts.price_date
    FROM taiwan_stocks ts
    WHERE ts.code = stock_code;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 刪除舊的清理函數（如果存在）
DROP FUNCTION IF EXISTS cleanup_old_stock_data();

-- 建立函數：清理舊資料（可選，用於維護）
CREATE OR REPLACE FUNCTION cleanup_old_stock_data()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 刪除超過 7 天的資料（保留最新資料）
    DELETE FROM taiwan_stocks
    WHERE price_date < CURRENT_DATE - INTERVAL '7 days';

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 注意：不插入測試資料，等待從台灣證交所 API 獲取實際資料
-- 執行以下命令來獲取實際股票資料：
-- 1. 手動執行更新腳本：node database/daily_stock_update.js
-- 2. 或等待 GitHub Actions 每日自動更新
-- 3. 或使用 Supabase Edge Functions 進行即時更新

-- 建立每日自動更新的排程函數（需要 pg_cron 擴展）
-- 注意：這需要在 Supabase 中手動設置或使用外部排程服務

-- 查詢範例：
-- 1. 搜尋股票：SELECT * FROM search_stocks('233');
-- 2. 獲取特定股票：SELECT * FROM get_stock_by_code('2330');
-- 3. 列出所有股票：SELECT * FROM taiwan_stocks ORDER BY code;
-- 4. 批量更新：SELECT update_daily_stock_prices('[{"code":"2330","name":"台積電","closing_price":"1050.00","date":"2024-12-31"}]'::jsonb);
