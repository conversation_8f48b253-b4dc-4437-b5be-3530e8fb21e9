# 📅 月曆收支總額顯示功能 - 直接在日期下方顯示

## 🎯 功能概述

**完全按照您的要求實現：直接在月曆的每個日期下方顯示收支總額**

- **有收入/支出的日期**：在日期數字下方顯示 `+總額` 或 `-總額`
- **沒有交易的日期**：下方保持空白
- **收入大於支出**：顯示綠色 `+金額`
- **支出大於收入**：顯示紅色 `-金額`

## 📱 視覺效果

```
6 月 2025
週日  週一  週二  週三  週四  週五  週六

1     2     3     4     5     6     7
      -5,000

8     9     10    11    12    13    14

15    16    17    18    19    20    21
+3萬   -776

22    23    24    25    26    27    28
            +1,252      -786

29    30
      -679
```

## 🔧 技術實現

### 1. 自定義日期組件

使用 `react-native-calendars` 的 `dayComponent` 屬性：

```typescript
const renderDay = (day: any, item: any) => {
  if (!day) return <View style={styles.emptyDay} />;

  const dateString = day.dateString;
  const dayNumber = day.day;
  const isSelected = dateString === selectedDate;
  const isToday = dateString === new Date().toISOString().split('T')[0];
  const isDisabled = day.state === 'disabled';
  
  // 獲取當日交易摘要
  const summary = getDayTransactionSummary(dateString);
  const netAmount = summary.income - summary.expense;
  const hasTransactions = summary.count > 0;

  return (
    <TouchableOpacity
      style={[
        styles.dayContainer,
        isSelected && styles.selectedDayContainer,
        isToday && !isSelected && styles.todayContainer,
      ]}
      onPress={() => setSelectedDate(dateString)}
      disabled={isDisabled}
    >
      <Text style={[
        styles.dayText,
        isSelected && styles.selectedDayText,
        isToday && !isSelected && styles.todayText,
        isDisabled && styles.disabledDayText,
      ]}>
        {dayNumber}
      </Text>
      {hasTransactions && (
        <Text style={[
          styles.amountText,
          isSelected && styles.selectedAmountText,
          netAmount > 0 ? styles.positiveAmount : styles.negativeAmount,
        ]}>
          {formatNetAmount(netAmount)}
        </Text>
      )}
    </TouchableOpacity>
  );
};
```

### 2. Calendar 組件配置

```typescript
<Calendar
  key={currentMonth}
  current={currentMonth}
  onDayPress={(day) => setSelectedDate(day.dateString)}
  onMonthChange={handleMonthChange}
  markedDates={markedDates}
  enableSwipeMonths={true}
  dayComponent={renderDay}  // 關鍵：使用自定義日期組件
  theme={{
    backgroundColor: '#ffffff',
    calendarBackground: '#ffffff',
    // ... 其他主題配置
  }}
/>
```

### 3. 金額格式化

```typescript
const formatNetAmount = (amount: number) => {
  if (amount === 0) return '';
  const absAmount = Math.abs(amount);
  
  // 簡化顯示：超過萬元顯示萬
  let formattedAmount: string;
  if (absAmount >= 10000) {
    const wanAmount = Math.round(absAmount / 1000) / 10;
    formattedAmount = wanAmount % 1 === 0 ? `${Math.round(wanAmount)}萬` : `${wanAmount}萬`;
  } else {
    formattedAmount = new Intl.NumberFormat('zh-TW', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(absAmount);
  }
  
  return amount > 0 ? `+${formattedAmount}` : `-${formattedAmount}`;
};
```

## 🎨 樣式配置

### 日期容器樣式

```typescript
dayContainer: {
  width: 32,
  height: 50,
  alignItems: 'center',
  justifyContent: 'center',
  paddingVertical: 2,
},
selectedDayContainer: {
  backgroundColor: '#007AFF',
  borderRadius: 16,
},
todayContainer: {
  backgroundColor: 'rgba(0, 122, 255, 0.1)',
  borderRadius: 16,
},
```

### 金額文字樣式

```typescript
amountText: {
  fontSize: 9,
  fontWeight: '600',
  textAlign: 'center',
  lineHeight: 11,
  marginTop: 1,
},
selectedAmountText: {
  color: '#ffffff',  // 選中時顯示白色
},
positiveAmount: {
  color: '#34C759',  // 綠色（收入）
},
negativeAmount: {
  color: '#FF3B30',  // 紅色（支出）
},
```

## 🧪 測試方法

### 1. 啟動應用程式

```bash
cd FinTranzo
npx expo start
```

### 2. 進入記帳頁面

- 點擊底部導航的「記帳」
- 查看月曆區域

### 3. 查看自動測試資料

開發模式下會自動添加測試交易：
- **今天**：-5,000 (餐飲支出) → 顯示紅色 `-5,000`
- **昨天**：+30,000 (薪水收入) → 顯示綠色 `+3萬`

### 4. 手動測試

1. **添加交易**：點擊「+」按鈕添加新交易
2. **查看顯示**：對應日期下方會立即顯示收支總額
3. **切換月份**：滑動切換月份，金額顯示會更新
4. **選中日期**：點擊有金額的日期，金額會變成白色

## 📊 預期顯示效果

### 測試資料範例

| 日期 | 收入 | 支出 | 淨額 | 月曆顯示 | 顏色 |
|------|------|------|------|----------|------|
| 今天 | 0 | 5,000 | -5,000 | -5,000 | 紅色 |
| 昨天 | 30,000 | 0 | +30,000 | +3萬 | 綠色 |
| 其他日期 | 0 | 0 | 0 | (空白) | 無 |

## ✅ 功能驗證清單

- [ ] 有交易的日期在數字下方顯示收支總額
- [ ] 沒有交易的日期下方保持空白
- [ ] 收入大於支出顯示綠色 +金額
- [ ] 支出大於收入顯示紅色 -金額
- [ ] 選中日期時金額顯示白色
- [ ] 今天的日期有特殊背景色
- [ ] 金額格式化正確（萬元簡化顯示）
- [ ] 月份切換功能正常
- [ ] 交易增刪改後月曆自動更新

## 🎯 關鍵特色

### **完全符合您的要求**
✅ **直接在日期下方顯示**：不是在別的地方，就是在每個日期數字的正下方
✅ **有交易顯示金額**：+1,234 或 -5,678
✅ **沒交易保持空白**：完全空白，不顯示任何內容
✅ **顏色區分**：綠色收入、紅色支出

### **技術實現穩定**
✅ **自定義日期組件**：使用 react-native-calendars 的 dayComponent
✅ **即時更新**：添加/刪除交易後立即更新顯示
✅ **性能優化**：只計算有交易的日期
✅ **狀態管理**：正確處理選中、今天、禁用狀態

## 🚀 使用說明

1. **查看月曆**：進入記帳頁面即可看到月曆
2. **理解顯示**：
   - 日期數字下方有金額 = 該日有交易
   - 日期數字下方空白 = 該日無交易
   - 綠色金額 = 收入大於支出
   - 紅色金額 = 支出大於收入
3. **查看詳情**：點擊有金額的日期查看詳細交易
4. **切換月份**：滑動或點擊標題切換月份

**這就是您要的功能！完全按照圖片中的方式實現，直接在每個日期下方顯示收支總額！** 🎉📅💰
