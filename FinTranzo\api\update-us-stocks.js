/**
 * Vercel Serverless Function - 美股每日更新
 * 每個工作日晚上10點執行（美股收盤後）
 */

import { createClient } from '@supabase/supabase-js';

// Supabase 配置
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Missing Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * 使用 Alpha Vantage API 獲取美股報價
 */
async function fetchUSStockPrice(symbol) {
  try {
    const apiKey = process.env.ALPHA_VANTAGE_API_KEY;
    if (!apiKey) {
      throw new Error('Missing Alpha Vantage API key');
    }
    
    const response = await fetch(
      `https://www.alphavantage.co/query?function=GLOBAL_QUOTE&symbol=${symbol}&apikey=${apiKey}`
    );
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    const quote = data['Global Quote'];
    
    if (quote && quote['05. price']) {
      const price = parseFloat(quote['05. price']);
      const change = parseFloat(quote['09. change']);
      const changePercent = parseFloat(quote['10. change percent'].replace('%', ''));
      
      return {
        symbol,
        price,
        change,
        change_percent: changePercent,
        updated_at: new Date().toISOString()
      };
    }
    
    return null;
  } catch (error) {
    console.error(`Error fetching ${symbol}:`, error);
    return null;
  }
}

/**
 * 使用免費 API 獲取美股報價（備用方案）
 */
async function fetchUSStockPriceFree(symbol) {
  try {
    // 使用 Yahoo Finance API（非官方）
    const response = await fetch(
      `https://query1.finance.yahoo.com/v8/finance/chart/${symbol}`
    );
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    const result = data.chart.result[0];
    
    if (result && result.meta) {
      const price = result.meta.regularMarketPrice;
      const previousClose = result.meta.previousClose;
      const change = price - previousClose;
      const changePercent = (change / previousClose) * 100;
      
      return {
        symbol,
        price,
        change,
        change_percent: changePercent,
        updated_at: new Date().toISOString()
      };
    }
    
    return null;
  } catch (error) {
    console.error(`Error fetching ${symbol} (free API):`, error);
    return null;
  }
}

/**
 * 批量更新美股價格
 */
async function updateUSStocks() {
  try {
    console.log('🚀 開始更新美股價格...');
    
    // 獲取需要更新的美股列表（限制數量以適應60秒限制）
    const { data: stocks, error: fetchError } = await supabase
      .from('us_stocks')
      .select('symbol')
      .limit(15); // 減少數量以適應免費版時間限制
    
    if (fetchError) {
      throw fetchError;
    }
    
    console.log(`📊 找到 ${stocks.length} 支美股需要更新`);
    
    let successCount = 0;
    let failedCount = 0;
    
    // 逐一處理，避免 API 限制
    for (const stock of stocks) {
      let result = null;
      
      // 首先嘗試 Alpha Vantage API
      if (process.env.ALPHA_VANTAGE_API_KEY) {
        result = await fetchUSStockPrice(stock.symbol);
      }
      
      // 如果失敗，嘗試免費 API
      if (!result) {
        result = await fetchUSStockPriceFree(stock.symbol);
      }
      
      if (result) {
        const { error: updateError } = await supabase
          .from('us_stocks')
          .update({
            price: result.price,
            change_amount: result.change,
            change_percent: result.change_percent,
            updated_at: result.updated_at
          })
          .eq('symbol', result.symbol);
        
        if (updateError) {
          console.error(`❌ 更新 ${result.symbol} 失敗:`, updateError);
          failedCount++;
        } else {
          console.log(`✅ 更新 ${result.symbol}: $${result.price}`);
          successCount++;
        }
      } else {
        console.log(`❌ 無法獲取 ${stock.symbol} 的價格`);
        failedCount++;
      }
      
      // 減少等待時間以適應時間限制
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log(`✅ 美股更新完成: 成功 ${successCount}, 失敗 ${failedCount}`);
    
    return {
      success: true,
      updated: successCount,
      failed: failedCount,
      total: stocks.length
    };
    
  } catch (error) {
    console.error('❌ 美股更新失敗:', error);
    throw error;
  }
}

/**
 * Vercel Serverless Function 入口
 */
export default async function handler(req, res) {
  // 只允許 POST 請求和 Cron Jobs
  if (req.method !== 'POST' && req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  // 驗證 Cron Job 請求（可選）
  const authHeader = req.headers.authorization;
  if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
    console.log('⚠️ 未授權的請求，但繼續執行更新');
  }
  
  try {
    const result = await updateUSStocks();
    
    res.status(200).json({
      message: '美股價格更新成功',
      timestamp: new Date().toISOString(),
      result
    });
    
  } catch (error) {
    console.error('❌ 處理請求失敗:', error);
    
    res.status(500).json({
      error: '美股價格更新失敗',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
}
