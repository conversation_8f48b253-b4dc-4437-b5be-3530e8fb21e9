# 🆓 Vercel 免費版優化策略

## 📋 免費版限制

Vercel 免費版有以下限制：
- ⏱️ **函數執行時間**：最多 60 秒
- 🔄 **每日執行次數**：100 次
- 💾 **記憶體**：1024 MB
- 🌐 **頻寬**：100 GB/月

## 🎯 優化策略

### 1. 時間限制優化

#### 原始設定 vs 優化後：
| 項目 | 原始 | 優化後 | 說明 |
|------|------|--------|------|
| **台股數量** | 100 支 | 20 支 | 減少 80% |
| **美股數量** | 50 支 | 15 支 | 減少 70% |
| **批次大小** | 10 支 | 5 支 | 減少處理時間 |
| **等待時間** | 1000ms | 500ms | 加快處理速度 |
| **執行時間** | 300s | 60s | 符合免費版限制 |

### 2. 分批更新策略

#### 方案A：輪流更新（推薦）
```javascript
// 每天更新不同的股票組
const today = new Date().getDay(); // 0-6 (週日到週六)
const offset = today * 20; // 每天偏移20支股票

const { data: stocks } = await supabase
  .from('taiwan_stocks')
  .select('code')
  .range(offset, offset + 19); // 每天更新20支
```

#### 方案B：優先級更新
```javascript
// 優先更新熱門股票
const { data: stocks } = await supabase
  .from('taiwan_stocks')
  .select('code')
  .order('volume', { ascending: false }) // 按成交量排序
  .limit(20);
```

### 3. 實際配置

#### vercel.json 配置：
```json
{
  "functions": {
    "api/update-taiwan-stocks.js": {
      "maxDuration": 60
    },
    "api/update-us-stocks.js": {
      "maxDuration": 60
    },
    "api/update-exchange-rates.js": {
      "maxDuration": 30
    }
  }
}
```

#### 每日執行次數：
- 🇹🇼 台股：1 次/天
- 🇺🇸 美股：1 次/天
- 💱 匯率：1 次/天
- **總計**：3 次/天（遠低於 100 次限制）

## 📊 覆蓋率計算

### 台股覆蓋率：
- **每天更新**：20 支股票
- **一週覆蓋**：140 支股票
- **一個月覆蓋**：600 支股票
- **足夠覆蓋**：主要上市股票

### 美股覆蓋率：
- **每天更新**：15 支股票
- **一週覆蓋**：105 支股票
- **一個月覆蓋**：450 支股票
- **足夠覆蓋**：S&P 500 主要股票

## 🔄 輪流更新實現

### 台股輪流更新：
```javascript
// 根據日期決定更新哪些股票
const today = new Date();
const dayOfYear = Math.floor((today - new Date(today.getFullYear(), 0, 0)) / 86400000);
const offset = (dayOfYear % 50) * 20; // 50天一個循環

const { data: stocks } = await supabase
  .from('taiwan_stocks')
  .select('code')
  .range(offset, offset + 19);
```

### 美股輪流更新：
```javascript
// 類似的輪流策略
const offset = (dayOfYear % 33) * 15; // 33天一個循環

const { data: stocks } = await supabase
  .from('us_stocks')
  .select('symbol')
  .range(offset, offset + 14);
```

## 🎯 升級考量

### 何時考慮升級到付費版？

#### 升級觸發條件：
- 📈 **用戶增長**：超過 1000 活躍用戶
- 🔄 **更新需求**：需要更頻繁的更新
- 📊 **數據量**：需要更多股票覆蓋
- ⚡ **性能要求**：需要更快的響應時間

#### Pro 版本優勢：
- ⏱️ **執行時間**：最多 5 分鐘
- 🔄 **執行次數**：1000 次/天
- 📊 **更多數據**：可更新 500+ 股票
- 🚀 **更好性能**：更快的冷啟動

## 💡 免費版最佳實踐

### 1. 智能更新
```javascript
// 只更新交易時間內的股票
const now = new Date();
const hour = now.getHours();

// 台股：只在交易時間後更新
if (hour >= 15 && hour <= 18) {
  await updateTaiwanStocks();
}

// 美股：只在交易時間後更新
if (hour >= 22 || hour <= 6) {
  await updateUSStocks();
}
```

### 2. 錯誤處理
```javascript
// 快速失敗，避免浪費時間
const timeout = setTimeout(() => {
  throw new Error('Function timeout approaching');
}, 55000); // 55秒後超時

try {
  await updateStocks();
} finally {
  clearTimeout(timeout);
}
```

### 3. 緩存策略
```javascript
// 使用 Supabase 作為緩存
const lastUpdate = await supabase
  .from('update_log')
  .select('updated_at')
  .eq('type', 'taiwan_stocks')
  .single();

// 如果今天已更新，跳過
if (isToday(lastUpdate.updated_at)) {
  return { message: 'Already updated today' };
}
```

## 📈 監控和優化

### 關鍵指標：
- ⏱️ **執行時間**：保持在 50 秒以下
- 📊 **成功率**：目標 > 90%
- 🔄 **覆蓋率**：一週內覆蓋主要股票
- 💰 **成本**：保持免費

### 優化建議：
1. **監控執行時間**：定期檢查函數日誌
2. **調整批次大小**：根據實際性能調整
3. **優化 API 調用**：減少不必要的請求
4. **錯誤處理**：快速跳過失敗的請求

## 🎉 總結

免費版策略能夠：
- ✅ **滿足基本需求**：每日更新核心股票
- ✅ **控制成本**：完全免費運行
- ✅ **保證穩定**：在限制範圍內可靠運行
- ✅ **可擴展**：需要時可升級到付費版

這個策略適合個人使用和小型應用，能夠提供穩定的股票價格更新服務！
