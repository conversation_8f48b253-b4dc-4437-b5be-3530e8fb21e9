-- 匯率資料表設定
-- 用於儲存美元兌台幣匯率資料

-- 建立匯率資料表
CREATE TABLE IF NOT EXISTS exchange_rates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  date DATE NOT NULL,
  currency VARCHAR(3) NOT NULL DEFAULT 'USD',
  cash_buy DECIMAL(8,3) NOT NULL,
  cash_sell DECIMAL(8,3) NOT NULL,
  spot_buy DECIMAL(8,3) NOT NULL,
  spot_sell DECIMAL(8,3) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 建立唯一索引 (每日每幣別只能有一筆記錄)
CREATE UNIQUE INDEX IF NOT EXISTS idx_exchange_rates_date_currency 
ON exchange_rates(date, currency);

-- 建立日期索引 (用於快速查詢)
CREATE INDEX IF NOT EXISTS idx_exchange_rates_date 
ON exchange_rates(date DESC);

-- 建立幣別索引
CREATE INDEX IF NOT EXISTS idx_exchange_rates_currency 
ON exchange_rates(currency);

-- 建立更新時間觸發器
CREATE OR REPLACE FUNCTION update_exchange_rates_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_exchange_rates_updated_at
  BEFORE UPDATE ON exchange_rates
  FOR EACH ROW
  EXECUTE FUNCTION update_exchange_rates_updated_at();

-- 獲取最新匯率函數
CREATE OR REPLACE FUNCTION get_latest_exchange_rate(
  target_currency VARCHAR(3) DEFAULT 'USD'
)
RETURNS TABLE (
  date DATE,
  currency VARCHAR(3),
  cash_buy DECIMAL(8,3),
  cash_sell DECIMAL(8,3),
  spot_buy DECIMAL(8,3),
  spot_sell DECIMAL(8,3)
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    er.date,
    er.currency,
    er.cash_buy,
    er.cash_sell,
    er.spot_buy,
    er.spot_sell
  FROM exchange_rates er
  WHERE er.currency = target_currency
  ORDER BY er.date DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- 獲取指定日期匯率函數
CREATE OR REPLACE FUNCTION get_exchange_rate_by_date(
  target_date DATE,
  target_currency VARCHAR(3) DEFAULT 'USD'
)
RETURNS TABLE (
  date DATE,
  currency VARCHAR(3),
  cash_buy DECIMAL(8,3),
  cash_sell DECIMAL(8,3),
  spot_buy DECIMAL(8,3),
  spot_sell DECIMAL(8,3)
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    er.date,
    er.currency,
    er.cash_buy,
    er.cash_sell,
    er.spot_buy,
    er.spot_sell
  FROM exchange_rates er
  WHERE er.currency = target_currency
    AND er.date <= target_date
  ORDER BY er.date DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- 匯率轉換函數 (美元轉台幣)
CREATE OR REPLACE FUNCTION convert_usd_to_twd(
  usd_amount DECIMAL(15,2),
  exchange_rate DECIMAL(8,3) DEFAULT NULL
)
RETURNS DECIMAL(15,2) AS $$
DECLARE
  rate DECIMAL(8,3);
BEGIN
  -- 如果沒有提供匯率，使用最新的即期賣出價
  IF exchange_rate IS NULL THEN
    SELECT spot_sell INTO rate
    FROM get_latest_exchange_rate('USD')
    LIMIT 1;
    
    -- 如果沒有匯率資料，使用預設值
    IF rate IS NULL THEN
      rate := 31.3;
    END IF;
  ELSE
    rate := exchange_rate;
  END IF;
  
  RETURN usd_amount * rate;
END;
$$ LANGUAGE plpgsql;

-- 匯率轉換函數 (台幣轉美元)
CREATE OR REPLACE FUNCTION convert_twd_to_usd(
  twd_amount DECIMAL(15,2),
  exchange_rate DECIMAL(8,3) DEFAULT NULL
)
RETURNS DECIMAL(15,2) AS $$
DECLARE
  rate DECIMAL(8,3);
BEGIN
  -- 如果沒有提供匯率，使用最新的即期買入價
  IF exchange_rate IS NULL THEN
    SELECT spot_buy INTO rate
    FROM get_latest_exchange_rate('USD')
    LIMIT 1;
    
    -- 如果沒有匯率資料，使用預設值
    IF rate IS NULL THEN
      rate := 31.2;
    END IF;
  ELSE
    rate := exchange_rate;
  END IF;
  
  RETURN twd_amount / rate;
END;
$$ LANGUAGE plpgsql;

-- 插入或更新匯率資料函數
CREATE OR REPLACE FUNCTION upsert_exchange_rate(
  target_date DATE,
  target_currency VARCHAR(3),
  cash_buy_rate DECIMAL(8,3),
  cash_sell_rate DECIMAL(8,3),
  spot_buy_rate DECIMAL(8,3),
  spot_sell_rate DECIMAL(8,3)
)
RETURNS UUID AS $$
DECLARE
  rate_id UUID;
BEGIN
  INSERT INTO exchange_rates (
    date, currency, cash_buy, cash_sell, spot_buy, spot_sell
  ) VALUES (
    target_date, target_currency, cash_buy_rate, cash_sell_rate, spot_buy_rate, spot_sell_rate
  )
  ON CONFLICT (date, currency) 
  DO UPDATE SET
    cash_buy = EXCLUDED.cash_buy,
    cash_sell = EXCLUDED.cash_sell,
    spot_buy = EXCLUDED.spot_buy,
    spot_sell = EXCLUDED.spot_sell,
    updated_at = NOW()
  RETURNING id INTO rate_id;
  
  RETURN rate_id;
END;
$$ LANGUAGE plpgsql;

-- 建立 RLS (Row Level Security) 政策
ALTER TABLE exchange_rates ENABLE ROW LEVEL SECURITY;

-- 允許所有人讀取匯率資料
CREATE POLICY "Allow public read access" ON exchange_rates
  FOR SELECT USING (true);

-- 只允許服務角色寫入資料
CREATE POLICY "Allow service role write access" ON exchange_rates
  FOR ALL USING (auth.role() = 'service_role');

-- 插入一些測試資料
INSERT INTO exchange_rates (date, currency, cash_buy, cash_sell, spot_buy, spot_sell) 
VALUES 
  (CURRENT_DATE, 'USD', 31.000, 31.500, 31.200, 31.300),
  (CURRENT_DATE - INTERVAL '1 day', 'USD', 30.950, 31.450, 31.150, 31.250),
  (CURRENT_DATE - INTERVAL '2 days', 'USD', 31.100, 31.600, 31.300, 31.400)
ON CONFLICT (date, currency) DO UPDATE SET
  cash_buy = EXCLUDED.cash_buy,
  cash_sell = EXCLUDED.cash_sell,
  spot_buy = EXCLUDED.spot_buy,
  spot_sell = EXCLUDED.spot_sell,
  updated_at = NOW();

-- 建立匯率統計視圖
CREATE OR REPLACE VIEW exchange_rate_stats AS
SELECT 
  currency,
  COUNT(*) as total_records,
  MIN(date) as earliest_date,
  MAX(date) as latest_date,
  AVG(spot_buy) as avg_spot_buy,
  AVG(spot_sell) as avg_spot_sell,
  MIN(spot_buy) as min_spot_buy,
  MAX(spot_buy) as max_spot_buy
FROM exchange_rates
GROUP BY currency;

-- 建立最新匯率視圖
CREATE OR REPLACE VIEW latest_exchange_rates AS
SELECT DISTINCT ON (currency)
  currency,
  date,
  cash_buy,
  cash_sell,
  spot_buy,
  spot_sell,
  updated_at
FROM exchange_rates
ORDER BY currency, date DESC;

COMMENT ON TABLE exchange_rates IS '匯率資料表，儲存美元兌台幣等匯率資訊';
COMMENT ON COLUMN exchange_rates.date IS '匯率日期';
COMMENT ON COLUMN exchange_rates.currency IS '幣別代碼 (如: USD)';
COMMENT ON COLUMN exchange_rates.cash_buy IS '現金買入價';
COMMENT ON COLUMN exchange_rates.cash_sell IS '現金賣出價';
COMMENT ON COLUMN exchange_rates.spot_buy IS '即期買入價';
COMMENT ON COLUMN exchange_rates.spot_sell IS '即期賣出價';

COMMENT ON FUNCTION get_latest_exchange_rate IS '獲取指定幣別的最新匯率';
COMMENT ON FUNCTION get_exchange_rate_by_date IS '獲取指定日期的匯率 (如無資料則取最近日期)';
COMMENT ON FUNCTION convert_usd_to_twd IS '美元轉台幣';
COMMENT ON FUNCTION convert_twd_to_usd IS '台幣轉美元';
COMMENT ON FUNCTION upsert_exchange_rate IS '插入或更新匯率資料';
