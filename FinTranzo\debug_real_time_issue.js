/**
 * 實時調試腳本 - 檢查當前應用狀態
 */

console.log('🔍 ===== 實時調試：檢查當前應用狀態 =====\n');

// 檢查當前日期
const now = new Date();
console.log('📅 當前系統時間:', now.toLocaleString('zh-TW'));
console.log('📅 當前年月日:', now.getFullYear(), '年', now.getMonth() + 1, '月', now.getDate(), '日');
console.log('📅 當前月份天數:', new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate(), '天\n');

// 模擬檢查修復是否生效
console.log('🔧 ===== 檢查修復是否生效 =====');

// 測試1：檢查日期邏輯修復
function checkDateLogicFix() {
  console.log('\n1️⃣ 檢查日期邏輯修復:');
  
  const paymentDay = 31;
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth();
  const lastDayOfCurrentMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
  
  console.log(`設定還款日: ${paymentDay}號`);
  console.log(`當前月份: ${currentYear}年${currentMonth + 1}月`);
  console.log(`當月最後一天: ${lastDayOfCurrentMonth}號`);
  
  // 檢查修復後的邏輯
  let actualPaymentDay;
  if (paymentDay > lastDayOfCurrentMonth) {
    actualPaymentDay = lastDayOfCurrentMonth;
    console.log(`❌ 需要調整: 原定${paymentDay}號 → ${actualPaymentDay}號`);
  } else {
    actualPaymentDay = paymentDay;
    console.log(`✅ 無需調整: ${paymentDay}號正常`);
  }
  
  return {
    isFixed: actualPaymentDay === paymentDay && lastDayOfCurrentMonth >= 31,
    actualDay: actualPaymentDay,
    expectedDay: paymentDay
  };
}

const dateFixResult = checkDateLogicFix();

// 檢查可能的問題
console.log('\n🚨 ===== 可能的問題檢查 =====');

console.log('\n2️⃣ 檢查可能的問題:');

// 問題1：檢查是否有其他地方覆蓋了修復
console.log('\n🔍 可能問題1: 其他地方的日期處理邏輯');
console.log('需要檢查的文件:');
console.log('- AddLiabilityModal.tsx (負債表單)');
console.log('- PaymentDayPicker.tsx (日期選擇器)');
console.log('- 其他可能處理日期的組件');

// 問題2：檢查是否有緩存問題
console.log('\n🔍 可能問題2: 緩存或狀態問題');
console.log('可能的原因:');
console.log('- 瀏覽器緩存了舊的代碼');
console.log('- React Native 的 Metro bundler 緩存');
console.log('- 應用狀態沒有正確更新');

// 問題3：檢查是否有異步問題
console.log('\n🔍 可能問題3: 異步執行順序問題');
console.log('可能的原因:');
console.log('- 負債添加和同步的時序問題');
console.log('- 事件發射和監聽的時序問題');
console.log('- 狀態更新的時序問題');

// 建議的調試步驟
console.log('\n🛠️ ===== 建議的調試步驟 =====');

console.log('\n3️⃣ 立即可以嘗試的解決方案:');

console.log('\n方案1: 清除緩存並重新啟動');
console.log('- 停止應用 (Ctrl+C)');
console.log('- 清除 Metro 緩存: npx expo start --clear');
console.log('- 清除瀏覽器緩存 (Ctrl+Shift+R)');

console.log('\n方案2: 檢查控制台日誌');
console.log('- 打開瀏覽器開發者工具');
console.log('- 查看 Console 標籤');
console.log('- 尋找以下關鍵字的日誌:');
console.log('  * "🔥 修復1" 或 "🔥 修復2"');
console.log('  * "月末日期調整"');
console.log('  * "無需調整"');
console.log('  * "負債添加事件發射"');

console.log('\n方案3: 手動觸發同步');
console.log('- 在瀏覽器控制台執行:');
console.log('  window.location.reload()');

// 生成測試用的負債數據
console.log('\n4️⃣ 測試用負債數據:');
const testLiability = {
  name: '測試信用卡',
  type: 'credit_card',
  balance: 50000,
  monthly_payment: 10000,
  payment_account: '銀行',
  payment_day: 31,
  payment_periods: 12
};

console.log('建議使用以下數據測試:');
console.log(JSON.stringify(testLiability, null, 2));

// 檢查當前時間是否影響邏輯
console.log('\n5️⃣ 時間相關檢查:');
const currentDay = now.getDate();
console.log(`當前日期: ${currentDay}號`);
console.log(`測試還款日: 31號`);

if (currentDay < 31) {
  console.log('✅ 當前日期小於31號，應該從本月開始計算');
} else {
  console.log('⚠️ 當前日期等於31號，會從下月開始計算');
}

// 檢查月份特殊情況
const monthsWith31Days = [1, 3, 5, 7, 8, 10, 12];
const currentMonthNumber = now.getMonth() + 1;
const hasCurrentMonth31Days = monthsWith31Days.includes(currentMonthNumber);

console.log(`當前月份(${currentMonthNumber}月)是否有31天: ${hasCurrentMonth31Days ? '是' : '否'}`);

if (hasCurrentMonth31Days) {
  console.log('✅ 當前月份有31天，31號應該正常顯示');
} else {
  console.log('⚠️ 當前月份沒有31天，31號會被調整');
}

console.log('\n📋 ===== 調試總結 =====');
console.log('如果問題仍然存在，請:');
console.log('1. 清除所有緩存並重新啟動應用');
console.log('2. 檢查瀏覽器控制台的錯誤和日誌');
console.log('3. 確認修復的代碼確實被執行');
console.log('4. 檢查是否有其他組件覆蓋了修復');

console.log('\n🎯 預期結果:');
console.log('- 設定31號還款日時，在5月應該顯示31號');
console.log('- 收支分析應該包含三筆記錄：午餐-500、薪水+80000、還款-10000');

console.log('\n✨ 調試腳本執行完成！');
