/**
 * 測試 Supabase 連接腳本
 * 在 React Native 之外測試資料庫連接
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Supabase 配置
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

console.log('🔗 Supabase URL:', supabaseUrl);
console.log('🔑 Supabase Key exists:', !!supabaseAnonKey);

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ 缺少 Supabase 環境變數');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testSupabaseConnection() {
  console.log('\n🚀 開始測試 Supabase 連接...\n');

  try {
    // 測試 1: 基本連接
    console.log('📡 測試 1: 基本連接');
    const { data: basicTest, error: basicError } = await supabase
      .from('taiwan_stocks')
      .select('code')
      .limit(1);

    if (basicError) {
      console.error('❌ 基本連接失敗:', basicError.message);
      return false;
    }
    console.log('✅ 基本連接成功');

    // 測試 2: 獲取股票資料
    console.log('\n📊 測試 2: 獲取股票資料');
    const { data: stocks, error: stocksError } = await supabase
      .from('taiwan_stocks')
      .select('code, name, market_type, closing_price')
      .limit(5);

    if (stocksError) {
      console.error('❌ 獲取股票資料失敗:', stocksError.message);
    } else {
      console.log(`✅ 成功獲取 ${stocks.length} 檔股票:`);
      stocks.forEach((stock, index) => {
        console.log(`   ${index + 1}. ${stock.code} ${stock.name} (${stock.market_type}) NT$${stock.closing_price}`);
      });
    }

    // 測試 3: 搜尋功能
    console.log('\n🔍 測試 3: 搜尋功能 (搜尋 "台積")');
    const { data: searchResults, error: searchError } = await supabase
      .rpc('search_stocks', { 
        search_term: '台積', 
        market_filter: null, 
        limit_count: 3 
      });

    if (searchError) {
      console.error('❌ 搜尋功能失敗:', searchError.message);
    } else {
      console.log(`✅ 搜尋成功，找到 ${searchResults.length} 筆結果:`);
      searchResults.forEach((stock, index) => {
        console.log(`   ${index + 1}. ${stock.code} ${stock.name} NT$${stock.closing_price}`);
      });
    }

    // 測試 4: 統計視圖
    console.log('\n📈 測試 4: 市場統計');
    const { data: stats, error: statsError } = await supabase
      .from('v_stock_summary')
      .select('*');

    if (statsError) {
      console.error('❌ 統計查詢失敗:', statsError.message);
    } else {
      console.log(`✅ 統計查詢成功，獲取 ${stats.length} 個市場統計:`);
      stats.forEach(stat => {
        console.log(`   ${stat.market_type}: ${stat.stock_count} 檔股票，平均價格 NT$${stat.avg_price.toFixed(2)}`);
      });
    }

    // 測試 5: 市場篩選
    console.log('\n🏢 測試 5: 市場篩選 (只顯示 ETF)');
    const { data: etfs, error: etfError } = await supabase
      .from('taiwan_stocks')
      .select('code, name, closing_price')
      .eq('market_type', 'ETF')
      .limit(5);

    if (etfError) {
      console.error('❌ ETF 篩選失敗:', etfError.message);
    } else {
      console.log(`✅ ETF 篩選成功，找到 ${etfs.length} 檔 ETF:`);
      etfs.forEach((etf, index) => {
        console.log(`   ${index + 1}. ${etf.code} ${etf.name} NT$${etf.closing_price}`);
      });
    }

    // 測試 6: 價格排序
    console.log('\n💰 測試 6: 價格排序 (最高價前5名)');
    const { data: highPriceStocks, error: priceError } = await supabase
      .from('taiwan_stocks')
      .select('code, name, closing_price, market_type')
      .order('closing_price', { ascending: false })
      .limit(5);

    if (priceError) {
      console.error('❌ 價格排序失敗:', priceError.message);
    } else {
      console.log(`✅ 價格排序成功，最高價前5名:`);
      highPriceStocks.forEach((stock, index) => {
        console.log(`   ${index + 1}. ${stock.code} ${stock.name} NT$${stock.closing_price} (${stock.market_type})`);
      });
    }

    console.log('\n🎉 所有測試完成！Supabase 連接正常');
    return true;

  } catch (error) {
    console.error('\n❌ 測試過程發生錯誤:', error.message);
    return false;
  }
}

// 執行測試
testSupabaseConnection()
  .then(success => {
    if (success) {
      console.log('\n✅ 測試結果: 成功');
      console.log('📱 您現在可以在 React Native 中使用股票功能了！');
    } else {
      console.log('\n❌ 測試結果: 失敗');
      console.log('🔧 請檢查 Supabase 配置和資料庫設置');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('\n💥 測試腳本執行失敗:', error);
    process.exit(1);
  });
