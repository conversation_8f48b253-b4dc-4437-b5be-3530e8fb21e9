#!/usr/bin/env python3
"""
執行ETF搜索函數修復
"""

import requests

# Supabase配置
SUPABASE_URL = "https://yrryyapzkgrsahranzvo.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlycnl5YXB6a2dyc2FocmFuenZvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODE3MzYzNSwiZXhwIjoyMDYzNzQ5NjM1fQ.xjiXVagzKgBBYXqtRjJiXwCgbRdEW0EpFzCIlJ26SSQ"

def execute_sql_file(file_path):
    """執行SQL文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 使用Supabase的SQL執行端點
        url = f"{SUPABASE_URL}/rest/v1/rpc/exec_sql"
        headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
        
        # 分割SQL語句並逐個執行
        statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        for i, statement in enumerate(statements, 1):
            if not statement:
                continue
                
            print(f"執行語句 {i}/{len(statements)}...")
            
            # 直接使用POST請求執行SQL
            response = requests.post(
                f"{SUPABASE_URL}/rest/v1/rpc/exec_sql",
                headers=headers,
                json={'sql': statement}
            )
            
            if response.status_code == 200:
                print(f"✅ 語句 {i} 執行成功")
            else:
                print(f"❌ 語句 {i} 執行失敗: {response.text}")
        
        print("✅ SQL文件執行完成")
        return True
        
    except Exception as e:
        print(f"❌ 執行SQL文件失敗: {str(e)}")
        return False

def test_etf_search():
    """測試ETF搜索函數"""
    print("\n🧪 測試修復後的ETF搜索函數...")
    
    url = f"{SUPABASE_URL}/rest/v1/rpc/search_us_etf"
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'application/json'
    }
    
    data = {
        'search_term': 'qqq',
        'limit_count': 5
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        
        if response.status_code == 200:
            results = response.json()
            print(f"✅ 搜索成功，找到 {len(results)} 個結果:")
            
            for result in results:
                is_etf = result.get('is_etf', False)
                asset_type = result.get('asset_type', 'N/A')
                print(f"   {result['symbol']}: {result['name']}")
                print(f"      is_etf: {is_etf} {'✅' if is_etf else '❌'}")
                print(f"      asset_type: {asset_type}")
                print()
            
            return len(results) > 0 and all(r.get('is_etf') for r in results)
        else:
            print(f"❌ 搜索失敗: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {str(e)}")
        return False

def main():
    """主函數"""
    print("🔧 ETF搜索函數修復工具")
    print("=" * 50)
    
    # 1. 執行修復腳本
    print("📝 執行ETF搜索函數修復...")
    success = execute_sql_file('fix_etf_search_function.sql')
    
    if not success:
        print("❌ 修復失敗，退出")
        return
    
    # 2. 測試修復結果
    test_success = test_etf_search()
    
    if test_success:
        print("🎉 ETF搜索函數修復成功！所有ETF都有正確的標籤")
    else:
        print("⚠️ 修復可能不完整，請檢查結果")

if __name__ == "__main__":
    main()
