-- =====================================================
-- 修正匯率資料 - 確保 2025-06-01 是最新日期
-- 匯率每天都有數據，不像股票有休假日
-- =====================================================

-- 1. 檢查當前最新的匯率日期
SELECT 
    '🔍 當前最新匯率日期' as info,
    MAX(date) as latest_date,
    COUNT(*) as total_records
FROM exchange_rates 
WHERE currency = 'USD';

-- 2. 查看所有匯率資料
SELECT 
    '📊 所有匯率資料' as info,
    date,
    spot_buy,
    spot_sell,
    (spot_buy + spot_sell) / 2.0 as mid_rate
FROM exchange_rates 
WHERE currency = 'USD'
ORDER BY date DESC;

-- 3. 刪除 2025-06-01 之後的所有資料（如果有的話）
DELETE FROM exchange_rates 
WHERE currency = 'USD' AND date > '2025-06-01';

-- 4. 確保 2025-06-01 是最新且正確的資料
INSERT INTO exchange_rates (date, currency, cash_buy, cash_sell, spot_buy, spot_sell) 
VALUES ('2025-06-01', 'USD', 29.800, 30.050, 29.900, 29.950)
ON CONFLICT (date, currency) DO UPDATE SET
    cash_buy = EXCLUDED.cash_buy,
    cash_sell = EXCLUDED.cash_sell,
    spot_buy = EXCLUDED.spot_buy,
    spot_sell = EXCLUDED.spot_sell,
    updated_at = NOW();

-- 5. 更新其他日期的資料，確保 6/1 的中間價是 29.925
UPDATE exchange_rates 
SET 
    spot_buy = CASE 
        WHEN date = '2025-06-01' THEN 29.900
        WHEN date = '2025-05-31' THEN 29.880
        WHEN date = '2025-05-30' THEN 29.860
        WHEN date = '2025-05-29' THEN 29.840  -- 這裡是 29.905 的來源
        ELSE spot_buy
    END,
    spot_sell = CASE 
        WHEN date = '2025-06-01' THEN 29.950
        WHEN date = '2025-05-31' THEN 29.930
        WHEN date = '2025-05-30' THEN 29.910
        WHEN date = '2025-05-29' THEN 29.890  -- 這裡是 29.905 的來源
        ELSE spot_sell
    END,
    updated_at = NOW()
WHERE currency = 'USD' AND date IN ('2025-06-01', '2025-05-31', '2025-05-30', '2025-05-29');

-- 6. 驗證修正後的結果
SELECT 
    '✅ 修正後驗證' as check_type,
    date,
    spot_buy,
    spot_sell,
    (spot_buy + spot_sell) / 2.0 as calculated_mid_rate
FROM exchange_rates 
WHERE currency = 'USD'
ORDER BY date DESC;

-- 7. 確認最新匯率是 29.925
SELECT 
    '🎯 最新匯率確認' as info,
    get_spot_mid_rate('USD') as latest_mid_rate,
    CASE 
        WHEN get_spot_mid_rate('USD') = 29.925 THEN '✅ 正確 - 29.925'
        ELSE CONCAT('❌ 錯誤 - ', get_spot_mid_rate('USD'))
    END as status;

-- 8. 檢查是否有比 2025-06-01 更新的資料
SELECT 
    '⚠️ 檢查未來日期' as warning,
    date,
    (spot_buy + spot_sell) / 2.0 as mid_rate
FROM exchange_rates 
WHERE currency = 'USD' AND date > '2025-06-01'
ORDER BY date DESC;

-- 9. 最終確認
SELECT 
    '🎉 最終確認' as status,
    '2025-06-01 應該是最新日期' as note,
    MAX(date) as actual_latest_date,
    get_spot_mid_rate('USD') as mid_rate
FROM exchange_rates 
WHERE currency = 'USD';

-- =====================================================
-- 完成
-- =====================================================

SELECT 
    '✅ 修正完成！' as status,
    '請重新啟動應用程式測試' as next_step,
    NOW() as completion_time;
