-- =====================================================
-- 簡化版權限修正
-- 解決 Supabase 寫入權限問題
-- =====================================================

-- =====================================================
-- 1. 修正美股表權限
-- =====================================================

-- 刪除現有的限制性政策
DROP POLICY IF EXISTS "Allow service role write access" ON us_stocks;
DROP POLICY IF EXISTS "Allow public read access" ON us_stocks;
DROP POLICY IF EXISTS "Allow all operations on us_stocks" ON us_stocks;

-- 創建新的寬鬆政策
CREATE POLICY "Allow all operations on us_stocks" ON us_stocks
    FOR ALL USING (true)
    WITH CHECK (true);

-- =====================================================
-- 2. 修正同步狀態表權限
-- =====================================================

-- 刪除現有的限制性政策
DROP POLICY IF EXISTS "Allow service role write access" ON sync_status;
DROP POLICY IF EXISTS "Allow public read access" ON sync_status;
DROP POLICY IF EXISTS "Allow all operations on sync_status" ON sync_status;

-- 創建新的寬鬆政策
CREATE POLICY "Allow all operations on sync_status" ON sync_status
    FOR ALL USING (true)
    WITH CHECK (true);

-- =====================================================
-- 3. 授予角色權限
-- =====================================================

-- 授予 anon 角色權限
GRANT ALL ON us_stocks TO anon;
GRANT ALL ON sync_status TO anon;

-- 授予 authenticated 角色權限
GRANT ALL ON us_stocks TO authenticated;
GRANT ALL ON sync_status TO authenticated;

-- 授予 service_role 角色權限
GRANT ALL ON us_stocks TO service_role;
GRANT ALL ON sync_status TO service_role;

-- =====================================================
-- 4. 測試權限
-- =====================================================

-- 清除舊的測試資料
DELETE FROM us_stocks WHERE symbol = 'TEST';

-- 插入測試資料
INSERT INTO us_stocks (
    symbol, 
    name, 
    sector, 
    price, 
    is_sp500, 
    created_at, 
    updated_at
) VALUES (
    'TEST', 
    'Test Company', 
    'Technology', 
    100.00, 
    true, 
    NOW(), 
    NOW()
);

-- 檢查插入結果
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM us_stocks WHERE symbol = 'TEST') 
        THEN '✅ 權限修正成功！可以寫入資料'
        ELSE '❌ 權限修正失敗！仍無法寫入'
    END as permission_status;

-- 測試更新
UPDATE us_stocks 
SET price = 150.00, updated_at = NOW() 
WHERE symbol = 'TEST';

-- 檢查更新結果
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM us_stocks WHERE symbol = 'TEST' AND price = 150.00) 
        THEN '✅ 更新權限正常！'
        ELSE '❌ 更新權限失敗！'
    END as update_status;

-- =====================================================
-- 5. 顯示結果
-- =====================================================

SELECT 
    '🎉 簡化版權限修正完成！' as status,
    '已允許所有角色讀寫美股資料' as fix_applied,
    '現在可以正常存儲股價資料' as result,
    NOW() as fix_time;

-- 顯示測試資料
SELECT 
    symbol,
    name,
    price,
    updated_at
FROM us_stocks 
WHERE symbol = 'TEST';

-- 清理測試資料
DELETE FROM us_stocks WHERE symbol = 'TEST';
