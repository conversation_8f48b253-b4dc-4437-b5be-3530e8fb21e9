/**
 * 測試日期修復邏輯
 */

// 模擬修復後的日期計算邏輯
function calculateCorrectPaymentDate(paymentDay, year, month) {
  // 獲取指定月份的最後一天
  const lastDayOfMonth = new Date(year, month + 1, 0).getDate();
  
  // 根據月末調整邏輯確定實際還款日期
  let actualPaymentDay;
  if (paymentDay > lastDayOfMonth) {
    // 如果設定的還款日超過當月最大天數，使用當月最後一天
    actualPaymentDay = lastDayOfMonth;
    console.log(`📅 月末日期調整: 原定${paymentDay}號，${year}年${month + 1}月只有${lastDayOfMonth}天，調整為${lastDayOfMonth}號`);
  } else {
    actualPaymentDay = paymentDay;
  }

  const paymentDate = new Date(year, month, actualPaymentDay);
  
  console.log(`📅 日期創建: 設定${paymentDay}號 -> 實際${actualPaymentDay}號 -> ${paymentDate.toLocaleDateString('zh-TW')} (${paymentDate.getDate()}號)`);
  
  return {
    originalDay: paymentDay,
    actualDay: actualPaymentDay,
    date: paymentDate,
    dateString: paymentDate.toLocaleDateString('zh-TW')
  };
}

// 測試案例
console.log('🧪 測試日期修復邏輯');
console.log('='.repeat(50));

// 測試案例1：5月31號（5月有31天）
console.log('\n📅 測試案例1：5月31號（5月有31天）');
const may31 = calculateCorrectPaymentDate(31, 2025, 4); // 2025年5月（月份從0開始）
console.log('結果:', may31);

// 測試案例2：4月31號（4月只有30天）
console.log('\n📅 測試案例2：4月31號（4月只有30天）');
const april31 = calculateCorrectPaymentDate(31, 2025, 3); // 2025年4月
console.log('結果:', april31);

// 測試案例3：2月31號（2月只有28天）
console.log('\n📅 測試案例3：2月31號（2月只有28天）');
const feb31 = calculateCorrectPaymentDate(31, 2025, 1); // 2025年2月
console.log('結果:', feb31);

// 測試案例4：2月29號（閏年）
console.log('\n📅 測試案例4：2月29號（2024閏年）');
const feb29_leap = calculateCorrectPaymentDate(29, 2024, 1); // 2024年2月（閏年）
console.log('結果:', feb29_leap);

// 測試案例5：2月29號（非閏年）
console.log('\n📅 測試案例5：2月29號（2025非閏年）');
const feb29_normal = calculateCorrectPaymentDate(29, 2025, 1); // 2025年2月（非閏年）
console.log('結果:', feb29_normal);

console.log('\n✅ 測試完成');
