{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "fetch-stocks": "tsx scripts/fetch-taiwan-stocks.ts", "import-csv": "tsx scripts/import-csv-stocks.ts", "test-supabase": "node test_supabase_connection.js", "update-stocks": "node -r ts-node/register scripts/fetch-taiwan-stocks.ts", "build-scripts": "tsc scripts/*.ts --outDir dist/scripts"}, "dependencies": {"@babel/plugin-transform-runtime": "^7.27.3", "@craftzdog/react-native-buffer": "^6.1.0", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "@supabase/supabase-js": "^2.49.8", "assert": "^2.1.0", "axios": "^1.6.0", "babel-plugin-module-resolver": "^5.0.2", "base-64": "^1.0.0", "buffer": "^6.0.3", "dotenv": "^16.5.0", "events": "^3.3.0", "expo": "~53.0.9", "expo-crypto": "^14.1.4", "expo-haptics": "~14.1.4", "expo-sensors": "~14.1.1", "expo-status-bar": "~2.2.3", "expo-web-browser": "^14.1.6", "path-browserify": "^1.0.1", "querystring-es3": "^0.2.1", "react": "19.0.0", "react-dom": "^18.3.1", "react-native": "0.79.2", "react-native-calendars": "^1.1312.0", "react-native-chart-kit": "^6.12.0", "react-native-dotenv": "^3.4.11", "react-native-draggable-flatlist": "^4.0.3", "react-native-gesture-handler": "^2.25.0", "react-native-get-random-values": "^1.11.0", "react-native-reanimated": "^3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "^15.12.0", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "readable-stream": "^4.7.0", "util": "^0.12.5", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/node": "^22.15.24", "@types/react": "~19.0.10", "ts-node": "^10.9.0", "tsx": "^4.0.0", "typescript": "~5.8.3"}, "private": true}