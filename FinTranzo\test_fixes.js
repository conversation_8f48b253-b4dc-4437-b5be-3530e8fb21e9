/**
 * 測試修復後的負債功能
 */

// 測試1：驗證31號還款日在5月的處理
console.log('🔍 ===== 測試1：驗證31號還款日處理 =====');

function testFixedDateLogic() {
  const currentDate = new Date(2025, 4, 29); // 5月29日
  const paymentDay = 31;
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();
  
  console.log(`當前日期: ${currentDate.toLocaleDateString('zh-TW')}`);
  console.log(`設定還款日: ${paymentDay}號`);
  console.log(`當前月份: ${currentYear}年${currentMonth + 1}月`);
  
  // 獲取當月的最後一天
  const lastDayOfCurrentMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
  console.log(`當月最後一天: ${lastDayOfCurrentMonth}號`);
  
  // 🔥 修復後的邏輯：只有當設定日期超過該月最大天數時才調整
  let actualPaymentDay;
  if (paymentDay > lastDayOfCurrentMonth) {
    actualPaymentDay = lastDayOfCurrentMonth;
    console.log(`❌ 需要調整: 原定${paymentDay}號，調整為${actualPaymentDay}號`);
  } else {
    actualPaymentDay = paymentDay;
    console.log(`✅ 無需調整: ${currentYear}年${currentMonth + 1}月有${lastDayOfCurrentMonth}天，${paymentDay}號正常`);
  }
  
  return actualPaymentDay;
}

const fixedPaymentDay = testFixedDateLogic();

// 測試2：驗證不同月份的日期調整
console.log('\n📅 ===== 測試2：驗證不同月份的日期調整 =====');

function testDifferentMonths() {
  const testCases = [
    { year: 2025, month: 4, paymentDay: 31, expected: 31, description: '5月31號' },
    { year: 2025, month: 3, paymentDay: 31, expected: 30, description: '4月31號→30號' },
    { year: 2025, month: 1, paymentDay: 31, expected: 28, description: '2月31號→28號' },
    { year: 2025, month: 1, paymentDay: 29, expected: 28, description: '2月29號→28號(平年)' },
    { year: 2024, month: 1, paymentDay: 29, expected: 29, description: '2024年2月29號(閏年)' },
  ];
  
  testCases.forEach(testCase => {
    const lastDay = new Date(testCase.year, testCase.month + 1, 0).getDate();
    const actualDay = Math.min(testCase.paymentDay, lastDay);
    const isCorrect = actualDay === testCase.expected;
    
    console.log(`${isCorrect ? '✅' : '❌'} ${testCase.description}: 實際${actualDay}號, 期望${testCase.expected}號`);
  });
}

testDifferentMonths();

// 測試3：模擬完整的負債創建流程
console.log('\n💳 ===== 測試3：模擬完整的負債創建流程 =====');

function simulateDebtCreation() {
  // 模擬負債數據
  const liability = {
    id: 'test_liability_1',
    name: '信用卡',
    type: 'credit_card',
    balance: 50000,
    monthly_payment: 10000,
    payment_account: '銀行',
    payment_day: 31,
    payment_periods: 12,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
  
  console.log('📝 負債信息:', {
    name: liability.name,
    balance: liability.balance,
    monthlyPayment: liability.monthly_payment,
    paymentDay: liability.payment_day,
    periods: liability.payment_periods
  });
  
  // 模擬日期計算邏輯
  const currentDate = new Date(2025, 4, 29); // 5月29日
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();
  const currentDay = currentDate.getDate();
  const paymentDay = liability.payment_day;
  
  console.log(`當前日期: ${currentDay}號，還款日: ${paymentDay}號`);
  
  // 判斷是否需要從本月開始
  if (currentDay < paymentDay) {
    console.log('✅ 還沒到本月還款日，從本月開始');
    
    const lastDayOfCurrentMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
    const actualPaymentDay = paymentDay > lastDayOfCurrentMonth ? lastDayOfCurrentMonth : paymentDay;
    
    const paymentDate = new Date(currentYear, currentMonth, actualPaymentDay);
    
    console.log(`📅 計算結果: ${paymentDate.toLocaleDateString('zh-TW')} (${actualPaymentDay}號)`);
    
    // 模擬創建交易記錄
    const transaction = {
      id: `debt_payment_${Date.now()}`,
      amount: liability.monthly_payment,
      type: 'expense',
      description: liability.name,
      category: '還款',
      account: liability.payment_account,
      date: paymentDate.toISOString(),
      is_recurring: true,
      recurring_frequency: 'monthly',
      max_occurrences: liability.payment_periods,
    };
    
    console.log('💰 創建的交易記錄:', {
      amount: transaction.amount,
      description: transaction.description,
      category: transaction.category,
      date: new Date(transaction.date).toLocaleDateString('zh-TW'),
      isRecurring: transaction.is_recurring
    });
    
    return transaction;
  } else {
    console.log('⏰ 已過本月還款日，從下月開始');
    return null;
  }
}

const createdTransaction = simulateDebtCreation();

// 測試4：模擬收支分析計算
console.log('\n📊 ===== 測試4：模擬收支分析計算 =====');

function simulateCashFlowAnalysis() {
  // 模擬交易數據
  const mockTransactions = [
    {
      id: '1',
      amount: 500,
      type: 'expense',
      description: '午餐',
      category: '餐飲',
      account: '現金',
      date: new Date(2025, 4, 20).toISOString()
    },
    {
      id: '2',
      amount: 80000,
      type: 'income',
      description: '薪水',
      category: '薪水',
      account: '銀行',
      date: new Date(2025, 4, 1).toISOString()
    }
  ];
  
  // 如果有創建的還款交易，加入到列表中
  if (createdTransaction) {
    mockTransactions.push(createdTransaction);
  }
  
  console.log('📋 所有交易:');
  mockTransactions.forEach(t => {
    console.log(`  ${t.type === 'income' ? '+' : '-'}${t.amount} - ${t.description} (${t.category})`);
  });
  
  // 計算收支分析
  let totalIncome = 0;
  let totalExpense = 0;
  let debtPayments = 0;
  const expenseByCategory = {};
  
  mockTransactions.forEach(transaction => {
    if (transaction.type === 'income') {
      totalIncome += transaction.amount;
    } else if (transaction.type === 'expense') {
      totalExpense += transaction.amount;
      
      const category = transaction.category || '其他';
      expenseByCategory[category] = (expenseByCategory[category] || 0) + transaction.amount;
      
      if (category === '還款') {
        debtPayments += transaction.amount;
      }
    }
  });
  
  console.log('\n📈 收支分析結果:');
  console.log(`總收入: +${totalIncome}`);
  console.log(`總支出: -${totalExpense}`);
  console.log(`淨現金流: ${totalIncome - totalExpense}`);
  
  console.log('\n📤 支出明細:');
  Object.entries(expenseByCategory).forEach(([category, amount]) => {
    console.log(`  ${category}: -${amount}`);
  });
  
  console.log(`\n🔍 還款交易檢查: ${debtPayments > 0 ? '✅ 包含' : '❌ 缺失'}`);
  if (debtPayments > 0) {
    console.log(`還款總額: ${debtPayments}`);
  }
  
  return {
    totalIncome,
    totalExpense,
    debtPayments,
    expenseByCategory,
    hasDebtPayment: debtPayments > 0
  };
}

const analysisResult = simulateCashFlowAnalysis();

// 總結測試結果
console.log('\n🎯 ===== 測試結果總結 =====');

console.log('1. 日期處理修復:');
if (fixedPaymentDay === 31) {
  console.log('   ✅ 5月31號正確顯示為31號');
} else {
  console.log('   ❌ 5月31號仍然被錯誤調整');
}

console.log('\n2. 收支分析修復:');
if (analysisResult.hasDebtPayment) {
  console.log('   ✅ 收支分析正確包含還款交易');
  console.log(`   💰 還款金額: ${analysisResult.debtPayments}`);
} else {
  console.log('   ❌ 收支分析仍然缺少還款交易');
}

console.log('\n3. 預期的收支分析數據:');
console.log('   午餐: -500');
console.log('   薪水: +80000');
if (analysisResult.hasDebtPayment) {
  console.log('   還款: -10000');
}

console.log('\n✅ 修復驗證完成！');
