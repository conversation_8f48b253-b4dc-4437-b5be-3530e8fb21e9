// 測試環境變數載入
require('dotenv').config();

console.log('🔍 測試環境變數...');
console.log('EXPO_PUBLIC_SUPABASE_URL:', process.env.EXPO_PUBLIC_SUPABASE_URL);
console.log('SUPABASE_SERVICE_ROLE_KEY exists:', !!process.env.SUPABASE_SERVICE_ROLE_KEY);

// 測試 Supabase 連接
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ 環境變數未正確載入');
  process.exit(1);
}

console.log('✅ 環境變數載入成功');

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// 測試簡單查詢
async function testConnection() {
  try {
    console.log('🔍 測試 Supabase 連接...');
    
    const { data, error } = await supabase
      .from('taiwan_stocks')
      .select('count(*)', { count: 'exact', head: true });
    
    if (error) {
      console.error('❌ Supabase 連接失敗:', error.message);
      return;
    }
    
    console.log('✅ Supabase 連接成功');
    console.log('📊 資料庫狀態正常');
    
  } catch (error) {
    console.error('❌ 連接測試失敗:', error.message);
  }
}

testConnection();
