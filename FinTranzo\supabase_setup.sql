-- 建立台股日收盤價資料表
CREATE TABLE IF NOT EXISTS taiwan_stocks (
  id SERIAL PRIMARY KEY,
  date DATE NOT NULL,
  code VARCHAR(10) NOT NULL,
  name VARCHAR(100) NOT NULL,
  closing_price DECIMAL(10,2),
  monthly_average_price DECIMAL(10,2),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 建立複合唯一索引，確保同一天同一股票只有一筆記錄
  UNIQUE(date, code)
);

-- 建立索引以提升查詢效能
CREATE INDEX IF NOT EXISTS idx_taiwan_stocks_code ON taiwan_stocks(code);
CREATE INDEX IF NOT EXISTS idx_taiwan_stocks_date ON taiwan_stocks(date);
CREATE INDEX IF NOT EXISTS idx_taiwan_stocks_code_date ON taiwan_stocks(code, date);

-- 建立更新時間觸發器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_taiwan_stocks_updated_at 
    BEFORE UPDATE ON taiwan_stocks 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 插入一些測試資料（可選）
INSERT INTO taiwan_stocks (date, code, name, closing_price, monthly_average_price) 
VALUES 
  ('2024-12-30', '2330', '台積電', 1000.00, 980.50),
  ('2024-12-30', '2317', '鴻海', 180.50, 175.20),
  ('2024-12-30', '2454', '聯發科', 1200.00, 1180.30)
ON CONFLICT (date, code) DO UPDATE SET
  name = EXCLUDED.name,
  closing_price = EXCLUDED.closing_price,
  monthly_average_price = EXCLUDED.monthly_average_price,
  updated_at = NOW();

-- 建立 RLS (Row Level Security) 政策
ALTER TABLE taiwan_stocks ENABLE ROW LEVEL SECURITY;

-- 允許所有人讀取台股資料
CREATE POLICY "Allow public read access" ON taiwan_stocks
  FOR SELECT USING (true);

-- 只允許服務角色寫入資料
CREATE POLICY "Allow service role write access" ON taiwan_stocks
  FOR ALL USING (auth.role() = 'service_role');

-- 建立視圖來獲取最新股價
CREATE OR REPLACE VIEW latest_taiwan_stocks AS
SELECT DISTINCT ON (code) 
  code,
  name,
  closing_price,
  monthly_average_price,
  date,
  updated_at
FROM taiwan_stocks 
ORDER BY code, date DESC;

-- 建立函數來獲取特定股票的最新價格
CREATE OR REPLACE FUNCTION get_latest_stock_price(stock_code VARCHAR(10))
RETURNS TABLE(
  code VARCHAR(10),
  name VARCHAR(100),
  closing_price DECIMAL(10,2),
  monthly_average_price DECIMAL(10,2),
  price_date DATE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ts.code,
    ts.name,
    ts.closing_price,
    ts.monthly_average_price,
    ts.date as price_date
  FROM taiwan_stocks ts
  WHERE ts.code = stock_code
  ORDER BY ts.date DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- 建立函數來搜尋股票（支援代號或名稱搜尋）
CREATE OR REPLACE FUNCTION search_stocks(search_term VARCHAR(100))
RETURNS TABLE(
  code VARCHAR(10),
  name VARCHAR(100),
  closing_price DECIMAL(10,2),
  monthly_average_price DECIMAL(10,2),
  price_date DATE
) AS $$
BEGIN
  RETURN QUERY
  SELECT DISTINCT ON (ts.code)
    ts.code,
    ts.name,
    ts.closing_price,
    ts.monthly_average_price,
    ts.date as price_date
  FROM taiwan_stocks ts
  WHERE ts.code ILIKE '%' || search_term || '%' 
     OR ts.name ILIKE '%' || search_term || '%'
  ORDER BY ts.code, ts.date DESC;
END;
$$ LANGUAGE plpgsql;
