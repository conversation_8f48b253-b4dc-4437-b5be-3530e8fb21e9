<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FinTranzo - 個人財務管理專家</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        
        .logo {
            font-size: 48px;
            font-weight: bold;
            color: #007AFF;
            margin-bottom: 16px;
        }
        
        .subtitle {
            font-size: 18px;
            color: #666;
            margin-bottom: 32px;
        }
        
        .feature {
            background: #F8F9FA;
            border-radius: 12px;
            padding: 20px;
            margin: 16px 0;
            text-align: left;
        }
        
        .feature-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .feature-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }
        
        .status {
            background: #34C759;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            margin-top: 24px;
            display: inline-block;
        }
        
        .tech-stack {
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #E5E5E5;
        }
        
        .tech-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
        }
        
        .tech-items {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
        }
        
        .tech-item {
            background: #007AFF;
            color: white;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">FinTranzo</div>
        <div class="subtitle">個人財務管理專家</div>
        
        <div class="feature">
            <div class="feature-title">🏠 儀表板</div>
            <div class="feature-desc">Bento Grid 風格的資產變化核心視圖，展示淨資產趨勢和 TOP 5 增減項目</div>
        </div>
        
        <div class="feature">
            <div class="feature-title">📅 智能記帳</div>
            <div class="feature-desc">月曆形式的交易記錄，支持手動記帳和週期性交易設定</div>
        </div>
        
        <div class="feature">
            <div class="feature-title">💰 資產負債表</div>
            <div class="feature-desc">完整的資產負債管理，支持台股美股 API 自動更新市價</div>
        </div>
        
        <div class="feature">
            <div class="feature-title">📊 收支分析</div>
            <div class="feature-desc">高度彈性的流水帳查詢、篩選與排序功能</div>
        </div>
        
        <div class="feature">
            <div class="feature-title">📈 圖表洞察</div>
            <div class="feature-desc">財務數據可視化和健康度評估指標</div>
        </div>
        
        <div class="status">✅ 開發環境已就緒</div>
        
        <div class="tech-stack">
            <div class="tech-title">技術架構</div>
            <div class="tech-items">
                <div class="tech-item">React Native</div>
                <div class="tech-item">Expo SDK</div>
                <div class="tech-item">TypeScript</div>
                <div class="tech-item">Supabase</div>
                <div class="tech-item">Zustand</div>
                <div class="tech-item">React Navigation</div>
                <div class="tech-item">Victory Charts</div>
            </div>
        </div>
    </div>
    
    <script>
        console.log('FinTranzo Web Test Page Loaded');
        console.log('Project Structure Created Successfully');
        console.log('Ready for Expo Development');
    </script>
</body>
</html>
