/**
 * 調試負債問題的腳本
 * 檢查兩個主要問題：
 * 1. 負債還款日31號顯示為30號
 * 2. 收支分析中缺少還款交易
 */

// 模擬當前日期為5月
const currentDate = new Date(2025, 4, 25); // 2025年5月25日
console.log('🔍 當前測試日期:', currentDate.toLocaleDateString('zh-TW'));

// 問題1：測試31號還款日的處理邏輯
console.log('\n📅 ===== 問題1：測試31號還款日處理 =====');

function testPaymentDayLogic() {
  const paymentDay = 31;
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();
  
  console.log(`設定還款日: ${paymentDay}號`);
  console.log(`當前月份: ${currentYear}年${currentMonth + 1}月`);
  
  // 獲取當月的最後一天
  const lastDayOfCurrentMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
  console.log(`當月最後一天: ${lastDayOfCurrentMonth}號`);
  
  // 根據月末調整邏輯確定實際還款日期
  let actualPaymentDay;
  if (paymentDay > lastDayOfCurrentMonth) {
    actualPaymentDay = lastDayOfCurrentMonth;
    console.log(`❌ 錯誤調整: 原定${paymentDay}號，調整為${actualPaymentDay}號`);
  } else {
    actualPaymentDay = paymentDay;
    console.log(`✅ 正確: ${currentYear}年${currentMonth + 1}月${paymentDay}號`);
  }
  
  return actualPaymentDay;
}

const actualDay = testPaymentDayLogic();

// 問題2：測試收支分析數據
console.log('\n💰 ===== 問題2：測試收支分析數據 =====');

// 模擬初始交易數據
const mockTransactions = [
  {
    id: '1',
    amount: 500,
    type: 'expense',
    description: '午餐',
    category: '餐飲',
    account: '現金',
    date: new Date(2025, 4, 20).toISOString() // 5月20日
  },
  {
    id: '2',
    amount: 80000,
    type: 'income',
    description: '薪水',
    category: '薪水',
    account: '銀行',
    date: new Date(2025, 4, 1).toISOString() // 5月1日
  }
];

console.log('📊 初始交易數據:');
mockTransactions.forEach(t => {
  console.log(`  ${t.type === 'income' ? '+' : '-'}${t.amount} - ${t.description} (${t.category})`);
});

// 模擬添加負債後的還款交易
function simulateDebtPaymentTransaction() {
  const debtPayment = {
    id: '3',
    amount: 10000,
    type: 'expense',
    description: '信用卡',
    category: '還款',
    account: '銀行',
    date: new Date(2025, 4, actualDay).toISOString() // 使用計算出的還款日
  };
  
  console.log('\n💳 模擬添加負債還款交易:');
  console.log(`  -${debtPayment.amount} - ${debtPayment.description} (${debtPayment.category})`);
  console.log(`  還款日期: ${new Date(debtPayment.date).toLocaleDateString('zh-TW')}`);
  
  return debtPayment;
}

const debtPayment = simulateDebtPaymentTransaction();

// 測試收支分析計算
function testCashFlowAnalysis() {
  const allTransactions = [...mockTransactions, debtPayment];
  
  console.log('\n📈 收支分析計算:');
  
  // 按類別統計支出
  const expensesByCategory = {};
  const incomeByCategory = {};
  
  allTransactions.forEach(transaction => {
    const category = transaction.category || '其他';
    
    if (transaction.type === 'expense') {
      expensesByCategory[category] = (expensesByCategory[category] || 0) + transaction.amount;
    } else if (transaction.type === 'income') {
      incomeByCategory[category] = (incomeByCategory[category] || 0) + transaction.amount;
    }
  });
  
  console.log('📤 支出分析:');
  Object.entries(expensesByCategory).forEach(([category, amount]) => {
    console.log(`  ${category}: -${amount}`);
  });
  
  console.log('📥 收入分析:');
  Object.entries(incomeByCategory).forEach(([category, amount]) => {
    console.log(`  ${category}: +${amount}`);
  });
  
  // 檢查是否包含還款
  const hasDebtPayment = expensesByCategory['還款'] > 0;
  console.log(`\n🔍 還款交易檢查: ${hasDebtPayment ? '✅ 包含' : '❌ 缺失'}`);
  
  return { expensesByCategory, incomeByCategory, hasDebtPayment };
}

const analysisResult = testCashFlowAnalysis();

// 總結問題
console.log('\n🎯 ===== 問題總結 =====');
console.log('1. 還款日期問題:');
if (actualDay === 31) {
  console.log('   ✅ 5月31號正確顯示為31號');
} else {
  console.log('   ❌ 5月31號錯誤顯示為30號');
  console.log('   🔧 需要修復月末日期調整邏輯');
}

console.log('\n2. 收支分析問題:');
if (analysisResult.hasDebtPayment) {
  console.log('   ✅ 收支分析包含還款交易');
} else {
  console.log('   ❌ 收支分析缺少還款交易');
  console.log('   🔧 需要確保負債同步正確創建交易記錄');
}

// 修復建議
console.log('\n🛠️ ===== 修復建議 =====');
console.log('1. 修復日期邏輯:');
console.log('   - 5月有31天，31號還款日應該顯示為31號，不需要調整');
console.log('   - 只有在目標月份沒有該日期時才需要調整');

console.log('\n2. 修復收支分析:');
console.log('   - 確保負債同步服務正確創建還款交易記錄');
console.log('   - 檢查交易數據服務是否正確返回所有交易');
console.log('   - 驗證收支分析計算邏輯包含所有類別');

// 測試正確的日期邏輯
console.log('\n✅ ===== 正確的日期邏輯測試 =====');
function testCorrectDateLogic() {
  const testCases = [
    { month: 4, paymentDay: 31, expected: 31, description: '5月31號' },
    { month: 3, paymentDay: 31, expected: 30, description: '4月31號→30號' },
    { month: 1, paymentDay: 31, expected: 28, description: '2月31號→28號' },
    { month: 1, paymentDay: 29, expected: 28, description: '2月29號→28號(平年)' },
  ];
  
  testCases.forEach(testCase => {
    const year = 2025;
    const lastDay = new Date(year, testCase.month + 1, 0).getDate();
    const actualDay = Math.min(testCase.paymentDay, lastDay);
    const isCorrect = actualDay === testCase.expected;
    
    console.log(`${isCorrect ? '✅' : '❌'} ${testCase.description}: 實際${actualDay}號, 期望${testCase.expected}號`);
  });
}

testCorrectDateLogic();
