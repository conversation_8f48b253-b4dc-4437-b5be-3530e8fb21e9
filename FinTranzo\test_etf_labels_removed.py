#!/usr/bin/env python3
"""
測試ETF標籤是否已經完全移除
"""

import os
import re

def check_file_for_etf_labels(file_path):
    """檢查文件中是否還有ETF標籤相關代碼"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查ETF標籤相關的模式
        etf_patterns = [
            r'etfBadge',
            r'ETF.*Badge',
            r'is_etf.*&&',
            r'item\.is_etf',
            r'etfBadgeText',
            r'ETF.*標籤',
            r'ETF.*徽章'
        ]
        
        found_patterns = []
        for pattern in etf_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                found_patterns.extend(matches)
        
        return found_patterns
        
    except Exception as e:
        print(f"❌ 讀取文件失敗 {file_path}: {str(e)}")
        return []

def scan_frontend_components():
    """掃描前端組件中的ETF標籤"""
    print("🔍 掃描前端組件中的ETF標籤...")
    print("=" * 60)
    
    # 要檢查的文件
    files_to_check = [
        'src/components/USStockSearchInput.tsx',
        'src/components/AllTickUSStockSearchInput.tsx',
        'src/components/StockSearchInput.tsx',
        'components/SimpleStockSearch.tsx',
        'components/StockListExample.tsx'
    ]
    
    total_issues = 0
    
    for file_path in files_to_check:
        full_path = os.path.join('FinTranzo', file_path)
        
        if os.path.exists(full_path):
            print(f"\n📁 檢查: {file_path}")
            
            patterns = check_file_for_etf_labels(full_path)
            
            if patterns:
                print(f"   ⚠️ 發現 {len(patterns)} 個ETF標籤相關代碼:")
                for pattern in patterns:
                    print(f"      - {pattern}")
                total_issues += len(patterns)
            else:
                print(f"   ✅ 沒有發現ETF標籤相關代碼")
        else:
            print(f"\n📁 檢查: {file_path}")
            print(f"   ⚠️ 文件不存在")
    
    print(f"\n📊 掃描結果:")
    print(f"   檢查文件數: {len(files_to_check)}")
    print(f"   發現問題數: {total_issues}")
    
    if total_issues == 0:
        print("🎉 所有ETF標籤已成功移除！")
        return True
    else:
        print("⚠️ 仍有ETF標籤相關代碼需要清理")
        return False

def check_specific_file_content():
    """檢查USStockSearchInput.tsx的具體內容"""
    print("\n🔍 檢查USStockSearchInput.tsx的具體內容:")
    print("=" * 60)
    
    file_path = 'FinTranzo/src/components/USStockSearchInput.tsx'
    
    if os.path.exists(file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 檢查關鍵行
            key_sections = []
            for i, line in enumerate(lines, 1):
                if 'resultHeader' in line or 'resultSymbol' in line or 'etf' in line.lower():
                    key_sections.append(f"第{i}行: {line.strip()}")
            
            if key_sections:
                print("📋 關鍵代碼段:")
                for section in key_sections[:10]:  # 只顯示前10行
                    print(f"   {section}")
            else:
                print("✅ 沒有發現ETF相關代碼")
                
        except Exception as e:
            print(f"❌ 讀取文件失敗: {str(e)}")
    else:
        print("❌ 文件不存在")

def main():
    """主函數"""
    print("🧪 ETF標籤移除驗證工具")
    print("=" * 60)
    
    # 1. 掃描所有前端組件
    success = scan_frontend_components()
    
    # 2. 檢查具體文件內容
    check_specific_file_content()
    
    # 3. 總結
    print(f"\n{'='*60}")
    if success:
        print("🎉 驗證通過！所有ETF標籤已成功移除")
        print("✅ 現在所有股票和ETF都會以統一的樣式顯示")
        print("✅ 不再有藍色ETF徽章")
        print("✅ 界面更加簡潔統一")
    else:
        print("⚠️ 驗證失敗！仍有ETF標籤需要清理")
    
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
