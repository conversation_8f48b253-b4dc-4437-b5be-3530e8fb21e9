import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { RecurringFrequency } from './src/types';
import { recurringTransactionService } from './src/services/recurringTransactionService';
import { getFrequencyDisplayName } from './src/utils/recurringTransactions';
import { testRecurringTransactions } from './src/utils/testRecurringTransactions';

export default function RecurringTransactionDemo() {
  const [recurringTransactions, setRecurringTransactions] = useState(
    recurringTransactionService.getAllRecurringTransactions()
  );
  const [generatedTransactions, setGeneratedTransactions] = useState(
    recurringTransactionService.getGeneratedTransactions()
  );

  const handleRunTest = () => {
    const result = testRecurringTransactions();
    setRecurringTransactions(result.recurringTransactions);
    setGeneratedTransactions(result.generatedTransactions);
    Alert.alert('測試完成', '請查看控制台輸出和下方結果');
  };

  const handleCreateSampleRecurring = () => {
    // 創建一個示例循環交易
    const newRecurring = recurringTransactionService.createRecurringTransaction({
      amount: 600,
      type: 'expense',
      description: '每月房租',
      category: '家居',
      account: '現金',
      frequency: RecurringFrequency.MONTHLY,
      startDate: new Date('2024-05-29'),
    });

    setRecurringTransactions(recurringTransactionService.getAllRecurringTransactions());
    Alert.alert('成功', `已創建循環交易: ${newRecurring.description}`);
  };

  const handleProcessRecurring = () => {
    const newTransactions = recurringTransactionService.processRecurringTransactions();
    setGeneratedTransactions(recurringTransactionService.getGeneratedTransactions());
    setRecurringTransactions(recurringTransactionService.getAllRecurringTransactions());

    if (newTransactions.length > 0) {
      Alert.alert('處理完成', `生成了 ${newTransactions.length} 筆新交易`);
    } else {
      Alert.alert('處理完成', '沒有需要執行的循環交易');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-TW', {
      style: 'currency',
      currency: 'TWD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>🔄 循環交易功能演示</Text>
        <Text style={styles.subtitle}>測試和演示循環交易的各種功能</Text>
      </View>

      {/* 控制按鈕 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>控制面板</Text>

        <TouchableOpacity style={styles.button} onPress={handleRunTest}>
          <Ionicons name="play-circle" size={20} color="#fff" />
          <Text style={styles.buttonText}>運行完整測試</Text>
        </TouchableOpacity>

        <TouchableOpacity style={[styles.button, styles.secondaryButton]} onPress={handleCreateSampleRecurring}>
          <Ionicons name="add-circle" size={20} color="#007AFF" />
          <Text style={[styles.buttonText, styles.secondaryButtonText]}>創建示例循環交易</Text>
        </TouchableOpacity>

        <TouchableOpacity style={[styles.button, styles.tertiaryButton]} onPress={handleProcessRecurring}>
          <Ionicons name="refresh-circle" size={20} color="#34C759" />
          <Text style={[styles.buttonText, styles.tertiaryButtonText]}>處理循環交易</Text>
        </TouchableOpacity>
      </View>

      {/* 循環交易列表 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>循環交易模板 ({recurringTransactions.length})</Text>

        {recurringTransactions.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="repeat-outline" size={48} color="#999" />
            <Text style={styles.emptyText}>暫無循環交易</Text>
          </View>
        ) : (
          recurringTransactions.map((rt) => (
            <View key={rt.id} style={styles.recurringItem}>
              <View style={styles.recurringLeft}>
                <View style={[styles.frequencyBadge, { backgroundColor: rt.is_active ? '#E8F5E8' : '#F5F5F5' }]}>
                  <Ionicons
                    name="repeat"
                    size={16}
                    color={rt.is_active ? '#34C759' : '#999'}
                  />
                </View>
                <View style={styles.recurringInfo}>
                  <Text style={styles.recurringDescription}>{rt.description}</Text>
                  <Text style={styles.recurringFrequency}>
                    {getFrequencyDisplayName(rt.frequency)} • {rt.account_id}
                  </Text>
                  <Text style={styles.recurringNext}>
                    下次執行: {new Date(rt.next_execution_date).toLocaleDateString('zh-TW')}
                  </Text>
                </View>
              </View>
              <View style={styles.recurringRight}>
                <Text style={[styles.recurringAmount, rt.type === 'income' ? styles.incomeAmount : styles.expenseAmount]}>
                  {rt.type === 'income' ? '+' : '-'}{formatCurrency(Math.abs(rt.amount))}
                </Text>
                <Text style={[styles.statusText, { color: rt.is_active ? '#34C759' : '#999' }]}>
                  {rt.is_active ? '啟用' : '停用'}
                </Text>
              </View>
            </View>
          ))
        )}
      </View>

      {/* 生成的交易記錄 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>生成的交易記錄 ({generatedTransactions.length})</Text>

        {generatedTransactions.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="document-text-outline" size={48} color="#999" />
            <Text style={styles.emptyText}>暫無生成的交易</Text>
          </View>
        ) : (
          generatedTransactions.slice(0, 10).map((transaction) => (
            <View key={transaction.id} style={styles.transactionItem}>
              <View style={styles.transactionLeft}>
                <View style={styles.transactionIcon}>
                  <Ionicons
                    name={transaction.type === 'income' ? 'arrow-down' : 'arrow-up'}
                    size={16}
                    color="#fff"
                  />
                </View>
                <View style={styles.transactionInfo}>
                  <Text style={styles.transactionDescription}>{transaction.description}</Text>
                  <Text style={styles.transactionDate}>
                    {new Date(transaction.date).toLocaleDateString('zh-TW')} •
                    {transaction.recurring_frequency && ` ${getFrequencyDisplayName(transaction.recurring_frequency)}`}
                  </Text>
                </View>
              </View>
              <Text style={[
                styles.transactionAmount,
                transaction.type === 'income' ? styles.incomeAmount : styles.expenseAmount
              ]}>
                {transaction.type === 'income' ? '+' : '-'}{formatCurrency(Math.abs(transaction.amount))}
              </Text>
            </View>
          ))
        )}

        {generatedTransactions.length > 10 && (
          <Text style={styles.moreText}>還有 {generatedTransactions.length - 10} 筆交易...</Text>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  section: {
    margin: 20,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginBottom: 12,
    gap: 8,
  },
  secondaryButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  tertiaryButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#34C759',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  secondaryButtonText: {
    color: '#007AFF',
  },
  tertiaryButtonText: {
    color: '#34C759',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    marginTop: 12,
  },
  recurringItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  recurringLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  frequencyBadge: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  recurringInfo: {
    flex: 1,
  },
  recurringDescription: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 2,
  },
  recurringFrequency: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  recurringNext: {
    fontSize: 12,
    color: '#007AFF',
  },
  recurringRight: {
    alignItems: 'flex-end',
  },
  recurringAmount: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  incomeAmount: {
    color: '#34C759',
  },
  expenseAmount: {
    color: '#FF3B30',
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  transactionIcon: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 12,
    color: '#666',
  },
  transactionAmount: {
    fontSize: 14,
    fontWeight: '600',
  },
  moreText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    paddingTop: 12,
    fontStyle: 'italic',
  },
});
