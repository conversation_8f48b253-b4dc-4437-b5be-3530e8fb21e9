/**
 * 清除本地存儲腳本
 * 用於重置應用程式數據，讓新的類別設定生效
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

// 存儲鍵名
const STORAGE_KEYS = {
  TRANSACTIONS: '@FinTranzo:transactions',
  CATEGORIES: '@FinTranzo:categories',
  ACCOUNTS: '@FinTranzo:accounts',
  INITIALIZED: '@FinTranzo:initialized'
};

/**
 * 清除所有本地存儲數據
 */
async function clearAllStorage() {
  console.log('🧹 開始清除本地存儲數據...');
  
  try {
    // 清除所有相關的存儲項目
    await AsyncStorage.multiRemove([
      STORAGE_KEYS.TRANSACTIONS,
      STORAGE_KEYS.CATEGORIES,
      STORAGE_KEYS.ACCOUNTS,
      STORAGE_KEYS.INITIALIZED
    ]);
    
    console.log('✅ 本地存儲數據已清除');
    console.log('📱 請重新啟動應用程式以使用新的類別設定');
    
    return true;
  } catch (error) {
    console.error('❌ 清除本地存儲失敗:', error);
    return false;
  }
}

/**
 * 檢查當前存儲狀態
 */
async function checkStorageStatus() {
  console.log('🔍 檢查當前存儲狀態...');
  
  try {
    const transactions = await AsyncStorage.getItem(STORAGE_KEYS.TRANSACTIONS);
    const categories = await AsyncStorage.getItem(STORAGE_KEYS.CATEGORIES);
    const accounts = await AsyncStorage.getItem(STORAGE_KEYS.ACCOUNTS);
    const initialized = await AsyncStorage.getItem(STORAGE_KEYS.INITIALIZED);
    
    console.log('📊 存儲狀態:');
    console.log('  - 交易記錄:', transactions ? '存在' : '不存在');
    console.log('  - 類別設定:', categories ? '存在' : '不存在');
    console.log('  - 帳戶設定:', accounts ? '存在' : '不存在');
    console.log('  - 初始化標記:', initialized ? '已初始化' : '未初始化');
    
    if (categories) {
      const parsedCategories = JSON.parse(categories);
      console.log('  - 類別數量:', parsedCategories.length);
      const expenseCategories = parsedCategories.filter(cat => cat.type === 'expense');
      console.log('  - 支出類別:', expenseCategories.map(cat => cat.name).join(', '));
    }
    
    return {
      hasTransactions: !!transactions,
      hasCategories: !!categories,
      hasAccounts: !!accounts,
      isInitialized: !!initialized
    };
  } catch (error) {
    console.error('❌ 檢查存儲狀態失敗:', error);
    return null;
  }
}

/**
 * 主函數
 */
async function main() {
  console.log('🚀 本地存儲管理工具');
  console.log('====================');
  
  // 檢查當前狀態
  await checkStorageStatus();
  
  console.log('');
  console.log('🧹 清除存儲數據...');
  
  // 清除數據
  const success = await clearAllStorage();
  
  if (success) {
    console.log('');
    console.log('✅ 操作完成！');
    console.log('📱 請重新啟動應用程式');
    console.log('🆕 新的類別設定將會生效：');
    console.log('   第一行：餐飲 交通 購物 娛樂 禮品');
    console.log('   第二行：學習 旅行 醫療 保險 還款');
    console.log('   第三行：家居 家庭 紅包 其他');
  } else {
    console.log('');
    console.log('❌ 操作失敗，請檢查錯誤信息');
  }
}

// 導出函數
export {
  clearAllStorage,
  checkStorageStatus,
  main
};

// 如果直接運行此文件，執行主函數
if (require.main === module) {
  main().catch(console.error);
}
