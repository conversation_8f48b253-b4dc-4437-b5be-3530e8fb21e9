/**
 * 清除本地存儲腳本
 * 用於重置應用程式數據，讓新的類別設定生效
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

// 存儲鍵名 - 完整列表
const STORAGE_KEYS = {
  // 交易相關
  TRANSACTIONS: '@FinTranzo:transactions',
  CATEGORIES: '@FinTranzo:categories',
  ACCOUNTS: '@FinTranzo:accounts',
  INITIALIZED: '@FinTranzo:initialized',

  // 資產負債相關
  ASSETS: '@FinTranzo:assets',
  LIABILITIES: '@FinTranzo:liabilities',

  // 用戶資料
  USER_PROFILE: '@FinTranzo:userProfile',

  // 循環交易
  RECURRING_TRANSACTIONS: '@FinTranzo:recurringTransactions',

  // 其他可能的存儲項目
  SETTINGS: '@FinTranzo:settings',
  CACHE: '@FinTranzo:cache'
};

/**
 * 清除所有本地存儲數據
 */
async function clearAllStorage() {
  console.log('🧹 開始清除本地存儲數據...');

  try {
    // 清除所有相關的存儲項目
    const keysToRemove = Object.values(STORAGE_KEYS);
    await AsyncStorage.multiRemove(keysToRemove);

    console.log('✅ 本地存儲數據已清除');
    console.log(`📊 清除了 ${keysToRemove.length} 個存儲項目`);
    console.log('📱 請重新啟動應用程式以重置所有數據');

    return true;
  } catch (error) {
    console.error('❌ 清除本地存儲失敗:', error);
    return false;
  }
}

/**
 * 清除特定類型的數據
 */
async function clearSpecificData(dataType) {
  console.log(`🧹 開始清除 ${dataType} 數據...`);

  try {
    let keysToRemove = [];

    switch (dataType) {
      case 'transactions':
        keysToRemove = [
          STORAGE_KEYS.TRANSACTIONS,
          STORAGE_KEYS.CATEGORIES,
          STORAGE_KEYS.ACCOUNTS,
          STORAGE_KEYS.INITIALIZED
        ];
        break;
      case 'assets':
        keysToRemove = [STORAGE_KEYS.ASSETS, STORAGE_KEYS.LIABILITIES];
        break;
      case 'user':
        keysToRemove = [STORAGE_KEYS.USER_PROFILE];
        break;
      case 'recurring':
        keysToRemove = [STORAGE_KEYS.RECURRING_TRANSACTIONS];
        break;
      default:
        console.log('❌ 未知的數據類型:', dataType);
        return false;
    }

    await AsyncStorage.multiRemove(keysToRemove);
    console.log(`✅ ${dataType} 數據已清除`);
    return true;
  } catch (error) {
    console.error(`❌ 清除 ${dataType} 數據失敗:`, error);
    return false;
  }
}

/**
 * 檢查當前存儲狀態
 */
async function checkStorageStatus() {
  console.log('🔍 檢查當前存儲狀態...');

  try {
    const results = {};

    // 檢查所有存儲項目
    for (const [key, storageKey] of Object.entries(STORAGE_KEYS)) {
      const data = await AsyncStorage.getItem(storageKey);
      results[key] = data !== null;
    }

    console.log('📊 存儲狀態:');
    console.log('  🔄 交易相關:');
    console.log('    - 交易記錄:', results.TRANSACTIONS ? '存在' : '不存在');
    console.log('    - 類別設定:', results.CATEGORIES ? '存在' : '不存在');
    console.log('    - 帳戶設定:', results.ACCOUNTS ? '存在' : '不存在');
    console.log('    - 初始化標記:', results.INITIALIZED ? '已初始化' : '未初始化');

    console.log('  💰 資產負債:');
    console.log('    - 資產數據:', results.ASSETS ? '存在' : '不存在');
    console.log('    - 負債數據:', results.LIABILITIES ? '存在' : '不存在');

    console.log('  👤 用戶資料:');
    console.log('    - 用戶檔案:', results.USER_PROFILE ? '存在' : '不存在');

    console.log('  🔄 其他:');
    console.log('    - 循環交易:', results.RECURRING_TRANSACTIONS ? '存在' : '不存在');
    console.log('    - 設定:', results.SETTINGS ? '存在' : '不存在');
    console.log('    - 快取:', results.CACHE ? '存在' : '不存在');

    return results;
  } catch (error) {
    console.error('❌ 檢查存儲狀態失敗:', error);
    return null;
  }
}

/**
 * 獲取所有存儲的鍵名
 */
async function getAllStorageKeys() {
  try {
    const allKeys = await AsyncStorage.getAllKeys();
    const finTranzoKeys = allKeys.filter(key => key.startsWith('@FinTranzo:'));

    console.log('🔍 所有 FinTranzo 存儲鍵名:');
    finTranzoKeys.forEach(key => {
      console.log(`  - ${key}`);
    });

    return finTranzoKeys;
  } catch (error) {
    console.error('❌ 獲取存儲鍵名失敗:', error);
    return [];
  }
}

/**
 * 主函數
 */
async function main() {
  console.log('🚀 本地存儲管理工具');
  console.log('====================');
  
  // 檢查當前狀態
  await checkStorageStatus();
  
  console.log('');
  console.log('🧹 清除存儲數據...');
  
  // 清除數據
  const success = await clearAllStorage();
  
  if (success) {
    console.log('');
    console.log('✅ 操作完成！');
    console.log('📱 請重新啟動應用程式');
    console.log('🆕 新的類別設定將會生效：');
    console.log('   第一行：餐飲 交通 購物 娛樂 禮品');
    console.log('   第二行：學習 旅行 醫療 保險 還款');
    console.log('   第三行：家居 家庭 紅包 其他');
  } else {
    console.log('');
    console.log('❌ 操作失敗，請檢查錯誤信息');
  }
}

// 導出函數
export {
  clearAllStorage,
  clearSpecificData,
  checkStorageStatus,
  getAllStorageKeys,
  main
};

// 如果直接運行此文件，執行主函數
if (require.main === module) {
  main().catch(console.error);
}
