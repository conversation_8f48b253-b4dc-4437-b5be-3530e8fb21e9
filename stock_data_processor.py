import os
import requests
import pandas as pd
import time
from datetime import datetime
from supabase import create_client, Client

# 配置Alpha Vantage API
ALPHA_VANTAGE_API_KEY = "QJTK95T7SA1661WM"
BASE_URL = "https://www.alphavantage.co/query"

# 配置Supabase
SUPABASE_URL = "YOUR_SUPABASE_URL"
SUPABASE_KEY = "YOUR_SUPABASE_KEY"
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

def get_stock_data(symbol):
    """從Alpha Vantage獲取股票數據"""
    params = {
        "function": "TIME_SERIES_DAILY",
        "symbol": symbol,
        "apikey": ALPHA_VANTAGE_API_KEY,
        "outputsize": "compact",
        "datatype": "json"
    }
    response = requests.get(BASE_URL, params=params)
    response.raise_for_status()
    return response.json()

def process_us_etf_data(csv_path):
    """處理美國ETF列表並插入到Supabase數據庫"""
    print(f"🔄 開始處理美國ETF數據: {csv_path}")

    try:
        # 讀取CSV文件
        df = pd.read_csv(csv_path, header=None, names=['symbol', 'name'])

        # 過濾掉空行
        df = df.dropna(subset=['symbol', 'name'])
        df = df[df['symbol'].str.strip() != '']
        df = df[df['name'].str.strip() != '']

        print(f"📊 找到 {len(df)} 個ETF")

        success_count = 0
        error_count = 0

        for index, row in df.iterrows():
            symbol = row['symbol'].strip()
            name = row['name'].strip()

            try:
                # 準備ETF數據
                etf_data = {
                    'symbol': symbol,
                    'name': name,
                    'is_etf': True,
                    'asset_type': 'ETF',
                    'sector': 'ETF',
                    'is_sp500': False,
                    'created_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat()
                }

                # 插入到Supabase (使用upsert避免重複)
                result = supabase.table('us_stocks').upsert(etf_data).execute()

                if result.data:
                    print(f"✅ 成功插入 {symbol}: {name}")
                    success_count += 1
                else:
                    print(f"⚠️ 插入 {symbol} 時沒有返回數據")
                    error_count += 1

            except Exception as e:
                print(f"❌ 處理 {symbol} 時出錯: {str(e)}")
                error_count += 1

            # 每10個ETF暫停一下，避免過載
            if (index + 1) % 10 == 0:
                print(f"📈 已處理 {index + 1}/{len(df)} 個ETF")
                time.sleep(1)

        print(f"\n🎉 ETF數據處理完成!")
        print(f"✅ 成功: {success_count} 個")
        print(f"❌ 失敗: {error_count} 個")

        return success_count, error_count

    except Exception as e:
        print(f"❌ 處理ETF數據時發生錯誤: {str(e)}")
        return 0, 1

def get_etf_quote(symbol):
    """從Alpha Vantage獲取ETF即時報價"""
    try:
        params = {
            "function": "GLOBAL_QUOTE",
            "symbol": symbol,
            "apikey": ALPHA_VANTAGE_API_KEY
        }

        response = requests.get(BASE_URL, params=params)
        response.raise_for_status()
        data = response.json()

        if 'Global Quote' in data and data['Global Quote']:
            quote = data['Global Quote']
            return {
                'symbol': quote.get('01. symbol', symbol),
                'price': float(quote.get('05. price', 0)),
                'change': float(quote.get('09. change', 0)),
                'change_percent': quote.get('10. change percent', '0%').replace('%', ''),
                'volume': int(quote.get('06. volume', 0)),
                'latest_trading_day': quote.get('07. latest trading day', ''),
                'previous_close': float(quote.get('08. previous close', 0)),
                'open': float(quote.get('02. open', 0)),
                'high': float(quote.get('03. high', 0)),
                'low': float(quote.get('04. low', 0))
            }
        else:
            print(f"⚠️ 沒有找到 {symbol} 的報價數據")
            return None

    except Exception as e:
        print(f"❌ 獲取 {symbol} 報價時出錯: {str(e)}")
        return None

def update_etf_prices(etf_symbols, max_requests=100):
    """更新ETF價格數據"""
    print(f"🔄 開始更新 {len(etf_symbols)} 個ETF的價格...")

    success_count = 0
    error_count = 0
    request_count = 0

    for i, symbol in enumerate(etf_symbols):
        if request_count >= max_requests:
            print(f"⚠️ 已達到最大請求數限制 ({max_requests})，停止更新")
            break

        try:
            print(f"📊 獲取 {symbol} 的報價... ({i+1}/{len(etf_symbols)})")

            quote_data = get_etf_quote(symbol)
            request_count += 1

            if quote_data:
                # 更新數據庫中的價格信息
                update_data = {
                    'price': quote_data['price'],
                    'open_price': quote_data['open'],
                    'high_price': quote_data['high'],
                    'low_price': quote_data['low'],
                    'volume': quote_data['volume'],
                    'change_amount': quote_data['change'],
                    'change_percent': float(quote_data['change_percent']),
                    'previous_close': quote_data['previous_close'],
                    'price_date': quote_data['latest_trading_day'],
                    'updated_at': datetime.now().isoformat()
                }

                result = supabase.table('us_stocks').update(update_data).eq('symbol', symbol).execute()

                if result.data:
                    print(f"✅ 更新 {symbol} 價格: ${quote_data['price']}")
                    success_count += 1
                else:
                    print(f"⚠️ 更新 {symbol} 時沒有返回數據")
                    error_count += 1
            else:
                error_count += 1

        except Exception as e:
            print(f"❌ 更新 {symbol} 價格時出錯: {str(e)}")
            error_count += 1

        # API速率限制：每分鐘5個請求
        if request_count % 5 == 0:
            print("⏱️ 等待60秒以遵守API速率限制...")
            time.sleep(60)
        else:
            time.sleep(12)  # 每個請求間隔12秒

    print(f"\n🎉 ETF價格更新完成!")
    print(f"✅ 成功: {success_count} 個")
    print(f"❌ 失敗: {error_count} 個")
    print(f"📡 API請求: {request_count} 次")

    return success_count, error_count

if __name__ == "__main__":
    # 處理ETF數據
    csv_path = "FinTranzo/database/美國ETF.csv"

    print("=" * 50)
    print("🇺🇸 美國ETF數據處理器")
    print("=" * 50)

    # 1. 插入ETF基本信息
    print("\n1️⃣ 插入ETF基本信息到數據庫...")
    success, errors = process_us_etf_data(csv_path)

    if success > 0:
        print(f"\n2️⃣ 更新前10個ETF的即時價格...")
        # 獲取前10個ETF進行價格更新測試
        test_etfs = ['SPY', 'QQQ', 'IWM', 'TQQQ', 'TLT', 'IVV', 'VOO', 'LQD', 'SOXL', 'SQQQ']
        update_etf_prices(test_etfs, max_requests=10)

    print("\n✅ 處理完成！")