import os
import requests
import pandas as pd
from supabase import create_client, Client

# 配置Alpha Vantage API
ALPHA_VANTAGE_API_KEY = "QJTK95T7SA1661WM"
BASE_URL = "https://www.alphavantage.co/query"

# 配置Supabase
SUPABASE_URL = "YOUR_SUPABASE_URL"
SUPABASE_KEY = "YOUR_SUPABASE_KEY"
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

def get_stock_data(symbol):
    """從Alpha Vantage獲取股票數據"""
    params = {
        "function": "TIME_SERIES_DAILY",
        "symbol": symbol,
        "apikey": ALPHA_VANTAGE_API_KEY,
        "outputsize": "compact",
        "datatype": "json"
    }
    response = requests.get(BASE_URL, params=params)
    response.raise_for_status()
    return response.json()

def process_sp500_data(csv_path):
    """處理SP500股票列表並獲取每支股票的數據"""
    # 讀取CSV文件
    df = pd.read_csv(csv_path)
    
    # 限制請求速率以避免超過API限制
    for index, row in df.iterrows():
        symbol = row['Symbol']
        try:
            data = get_stock_data(symbol)
            # 在這裡添加數據處理和存儲到Supabase的邏輯
            print(f"成功處理 {symbol} 的數據")
        except Exception as e:
            print(f"處理 {symbol} 時出錯: {str(e)}")
        
        # 遵守免費API的速率限制 (5請求/分鐘)
        if (index + 1) % 5 == 0:
            time.sleep(60)

if __name__ == "__main__":
    csv_path = "20250601135735.csv"
    process_sp500_data(csv_path)