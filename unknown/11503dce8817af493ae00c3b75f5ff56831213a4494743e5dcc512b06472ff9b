-- =====================================================
-- 自動更新系統資料庫設定
-- 支援美股、台股、匯率的每日自動更新
-- =====================================================

-- =====================================================
-- 1. 台股資料表
-- =====================================================

CREATE TABLE IF NOT EXISTS taiwan_stocks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    price DECIMAL(10,2),
    change_amount DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    volume BIGINT,
    market_type VARCHAR(10) DEFAULT 'TSE', -- TSE (上市) 或 OTC (上櫃)
    price_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 台股索引
CREATE INDEX IF NOT EXISTS idx_taiwan_stocks_symbol ON taiwan_stocks(symbol);
CREATE INDEX IF NOT EXISTS idx_taiwan_stocks_name ON taiwan_stocks(name);
CREATE INDEX IF NOT EXISTS idx_taiwan_stocks_price_date ON taiwan_stocks(price_date);

-- =====================================================
-- 2. 匯率資料表
-- =====================================================

CREATE TABLE IF NOT EXISTS exchange_rates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    base_currency VARCHAR(3) NOT NULL,
    target_currency VARCHAR(3) NOT NULL,
    rate DECIMAL(10,6) NOT NULL,
    buy_rate DECIMAL(10,6),
    sell_rate DECIMAL(10,6),
    source VARCHAR(50),
    rate_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(base_currency, target_currency, rate_date)
);

-- 匯率索引
CREATE INDEX IF NOT EXISTS idx_exchange_rates_pair ON exchange_rates(base_currency, target_currency);
CREATE INDEX IF NOT EXISTS idx_exchange_rates_date ON exchange_rates(rate_date);

-- =====================================================
-- 3. 每日更新日誌表
-- =====================================================

CREATE TABLE IF NOT EXISTS daily_update_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    update_date DATE NOT NULL,
    total_updates INTEGER DEFAULT 0,
    successful_updates INTEGER DEFAULT 0,
    failed_updates INTEGER DEFAULT 0,
    update_results JSONB,
    total_duration INTEGER, -- 秒
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 更新日誌索引
CREATE INDEX IF NOT EXISTS idx_daily_update_logs_date ON daily_update_logs(update_date);

-- =====================================================
-- 4. 台股 UPSERT 函數
-- =====================================================

CREATE OR REPLACE FUNCTION upsert_taiwan_stock(
    stock_symbol VARCHAR(10),
    stock_name VARCHAR(100),
    stock_price DECIMAL(10,2),
    stock_change DECIMAL(10,2) DEFAULT NULL,
    stock_change_percent DECIMAL(5,2) DEFAULT NULL,
    stock_volume BIGINT DEFAULT NULL,
    market_type VARCHAR(10) DEFAULT 'TSE'
)
RETURNS UUID AS $$
DECLARE
    stock_id UUID;
BEGIN
    INSERT INTO taiwan_stocks (
        symbol, name, price, change_amount, change_percent, volume, market_type
    ) VALUES (
        stock_symbol, stock_name, stock_price, stock_change, stock_change_percent, stock_volume, market_type
    )
    ON CONFLICT (symbol) 
    DO UPDATE SET
        name = EXCLUDED.name,
        price = EXCLUDED.price,
        change_amount = EXCLUDED.change_amount,
        change_percent = EXCLUDED.change_percent,
        volume = EXCLUDED.volume,
        market_type = EXCLUDED.market_type,
        price_date = CURRENT_DATE,
        updated_at = NOW()
    RETURNING id INTO stock_id;
    
    RETURN stock_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. 匯率 UPSERT 函數
-- =====================================================

CREATE OR REPLACE FUNCTION upsert_exchange_rate(
    base_currency VARCHAR(3),
    target_currency VARCHAR(3),
    exchange_rate DECIMAL(10,6),
    buy_rate DECIMAL(10,6) DEFAULT NULL,
    sell_rate DECIMAL(10,6) DEFAULT NULL,
    rate_source VARCHAR(50) DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    rate_id UUID;
BEGIN
    INSERT INTO exchange_rates (
        base_currency, target_currency, rate, buy_rate, sell_rate, source
    ) VALUES (
        base_currency, target_currency, exchange_rate, buy_rate, sell_rate, rate_source
    )
    ON CONFLICT (base_currency, target_currency, rate_date) 
    DO UPDATE SET
        rate = EXCLUDED.rate,
        buy_rate = EXCLUDED.buy_rate,
        sell_rate = EXCLUDED.sell_rate,
        source = EXCLUDED.source,
        updated_at = NOW()
    RETURNING id INTO rate_id;
    
    RETURN rate_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 6. 搜尋台股函數
-- =====================================================

CREATE OR REPLACE FUNCTION search_taiwan_stocks(
    search_term TEXT,
    limit_count INTEGER DEFAULT 50
)
RETURNS TABLE (
    symbol VARCHAR(10),
    name VARCHAR(100),
    price DECIMAL(10,2),
    change_amount DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    volume BIGINT,
    market_type VARCHAR(10)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.symbol, s.name, s.price, s.change_amount, s.change_percent, s.volume, s.market_type
    FROM taiwan_stocks s
    WHERE 
        (s.symbol ILIKE '%' || search_term || '%' OR s.name ILIKE '%' || search_term || '%')
        AND s.price IS NOT NULL
    ORDER BY 
        CASE 
            WHEN s.symbol = search_term THEN 1
            WHEN s.symbol ILIKE search_term || '%' THEN 2
            WHEN s.name ILIKE search_term || '%' THEN 3
            ELSE 4
        END,
        s.volume DESC NULLS LAST
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 7. 獲取匯率函數
-- =====================================================

CREATE OR REPLACE FUNCTION get_exchange_rate(
    base_currency VARCHAR(3),
    target_currency VARCHAR(3),
    rate_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE (
    rate DECIMAL(10,6),
    buy_rate DECIMAL(10,6),
    sell_rate DECIMAL(10,6),
    source VARCHAR(50),
    last_updated TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        e.rate, e.buy_rate, e.sell_rate, e.source, e.updated_at
    FROM exchange_rates e
    WHERE 
        e.base_currency = get_exchange_rate.base_currency
        AND e.target_currency = get_exchange_rate.target_currency
        AND e.rate_date <= rate_date
    ORDER BY e.rate_date DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 8. 獲取更新統計函數
-- =====================================================

CREATE OR REPLACE FUNCTION get_update_statistics()
RETURNS TABLE (
    us_stocks_count BIGINT,
    taiwan_stocks_count BIGINT,
    exchange_rates_count BIGINT,
    last_us_update DATE,
    last_taiwan_update DATE,
    last_rate_update DATE,
    total_daily_updates BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*) FROM us_stocks WHERE price IS NOT NULL) as us_stocks_count,
        (SELECT COUNT(*) FROM taiwan_stocks WHERE price IS NOT NULL) as taiwan_stocks_count,
        (SELECT COUNT(*) FROM exchange_rates WHERE rate_date = CURRENT_DATE) as exchange_rates_count,
        (SELECT MAX(price_date) FROM us_stocks) as last_us_update,
        (SELECT MAX(price_date) FROM taiwan_stocks) as last_taiwan_update,
        (SELECT MAX(rate_date) FROM exchange_rates) as last_rate_update,
        (SELECT COUNT(*) FROM daily_update_logs) as total_daily_updates;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 9. RLS 政策
-- =====================================================

-- 台股表 RLS
ALTER TABLE taiwan_stocks ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Allow all operations on taiwan_stocks" ON taiwan_stocks;
CREATE POLICY "Allow all operations on taiwan_stocks" ON taiwan_stocks
    FOR ALL USING (true)
    WITH CHECK (true);

-- 匯率表 RLS
ALTER TABLE exchange_rates ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Allow all operations on exchange_rates" ON exchange_rates;
CREATE POLICY "Allow all operations on exchange_rates" ON exchange_rates
    FOR ALL OPERATIONS USING (true)
    WITH CHECK (true);

-- 更新日誌表 RLS
ALTER TABLE daily_update_logs ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Allow all operations on daily_update_logs" ON daily_update_logs;
CREATE POLICY "Allow all operations on daily_update_logs" ON daily_update_logs
    FOR ALL USING (true)
    WITH CHECK (true);

-- =====================================================
-- 10. 授予權限
-- =====================================================

-- 授予 anon 角色權限
GRANT ALL ON taiwan_stocks TO anon;
GRANT ALL ON exchange_rates TO anon;
GRANT ALL ON daily_update_logs TO anon;

-- 授予 authenticated 角色權限
GRANT ALL ON taiwan_stocks TO authenticated;
GRANT ALL ON exchange_rates TO authenticated;
GRANT ALL ON daily_update_logs TO authenticated;

-- 授予 service_role 角色權限
GRANT ALL ON taiwan_stocks TO service_role;
GRANT ALL ON exchange_rates TO service_role;
GRANT ALL ON daily_update_logs TO service_role;

-- =====================================================
-- 11. 插入測試資料
-- =====================================================

-- 插入一些台股測試資料
INSERT INTO taiwan_stocks (symbol, name, price, market_type) VALUES
('2330', '台積電', 580.00, 'TSE'),
('2317', '鴻海', 105.50, 'TSE'),
('2454', '聯發科', 1200.00, 'TSE'),
('2881', '富邦金', 75.80, 'TSE'),
('2882', '國泰金', 62.40, 'TSE')
ON CONFLICT (symbol) DO NOTHING;

-- 插入一些匯率測試資料
INSERT INTO exchange_rates (base_currency, target_currency, rate, source) VALUES
('USD', 'TWD', 31.25, 'Test Data'),
('EUR', 'TWD', 33.80, 'Test Data'),
('JPY', 'TWD', 0.21, 'Test Data'),
('GBP', 'TWD', 39.50, 'Test Data')
ON CONFLICT (base_currency, target_currency, rate_date) DO NOTHING;

-- =====================================================
-- 設定完成
-- =====================================================

SELECT 
    '🎉 自動更新系統資料庫設定完成！' as status,
    '✅ 台股資料表已建立' as taiwan_stocks,
    '✅ 匯率資料表已建立' as exchange_rates,
    '✅ 更新日誌表已建立' as update_logs,
    '✅ UPSERT 函數已建立' as functions,
    '✅ 搜尋函數已建立' as search_functions,
    '✅ RLS 政策已設定' as security,
    '✅ 測試資料已插入' as test_data,
    NOW() as setup_time;
