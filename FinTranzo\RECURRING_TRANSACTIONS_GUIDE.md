# 🔄 循環交易功能使用指南

## 功能概述

循環交易功能允許用戶設定定期重複的收入或支出記錄，系統會自動在指定的時間間隔生成相應的交易記錄。

## 主要特性

### ✨ 支援的循環頻率
- **每日** - 每天執行一次
- **每週** - 每週執行一次  
- **每月** - 每月執行一次
- **每年** - 每年執行一次

### 🎯 使用場景
- **每月房租** - 每月29號自動扣款600元
- **每週健身房費用** - 每週固定時間扣款
- **每月薪水** - 每月固定日期收入
- **每年保險費** - 年度固定支出

## 如何使用

### 1. 創建循環交易

1. 在交易記錄頁面點擊 **"+"** 按鈕
2. 填寫交易基本資訊：
   - 金額
   - 描述（可選）
   - 類型（收入/支出）
   - 類別
   - 帳戶
3. 開啟 **"循環"** 開關
4. 選擇循環頻率：
   - 每日
   - 每週  
   - 每月
   - 每年
5. 點擊 **"保存"** 完成設定

### 2. 循環交易的執行

- 系統會在每天午夜自動檢查是否有需要執行的循環交易
- 到期的循環交易會自動生成新的交易記錄
- 生成的交易會顯示在交易列表中，並標示為循環交易

### 3. 識別循環交易

在交易列表中，循環交易會有以下標識：
- 🔄 循環圖標徽章
- 顯示循環頻率（如：每月、每週）
- 特殊的視覺標識

## 技術實現

### 數據結構

```typescript
// 循環交易模板
interface RecurringTransaction {
  id: string;
  amount: number;
  type: 'income' | 'expense';
  description?: string;
  frequency: RecurringFrequency;
  start_date: string;
  next_execution_date: string;
  is_active: boolean;
}

// 生成的交易記錄
interface Transaction {
  id: string;
  amount: number;
  type: 'income' | 'expense';
  description?: string;
  date: string;
  is_recurring?: boolean;
  recurring_frequency?: RecurringFrequency;
  parent_recurring_id?: string;
}
```

### 核心功能

1. **日期計算** - 自動計算下次執行日期
2. **執行檢查** - 判斷是否需要執行循環交易
3. **交易生成** - 從模板生成實際交易記錄
4. **狀態管理** - 管理循環交易的啟用/停用狀態

## 示例場景

### 場景1：每月房租
```
設定日期：2024年5月29日
金額：600元
類型：支出
頻率：每月
結果：每月29號自動記錄600元支出
```

### 場景2：每週健身房
```
設定日期：2024年5月20日（週一）
金額：300元  
類型：支出
頻率：每週
結果：每週一自動記錄300元支出
```

### 場景3：每月薪水
```
設定日期：2024年5月25日
金額：50000元
類型：收入
頻率：每月
結果：每月25號自動記錄50000元收入
```

## 管理循環交易

### 查看循環交易
- 所有設定的循環交易都會保存在系統中
- 可以查看下次執行日期
- 顯示啟用/停用狀態

### 修改循環交易
- 可以停用不需要的循環交易
- 可以重新啟用已停用的循環交易
- 可以刪除不再需要的循環交易

### 生成記錄追蹤
- 所有由循環交易生成的記錄都會標記來源
- 可以追蹤哪些交易是自動生成的
- 保持完整的審計軌跡

## 注意事項

1. **時間精確性** - 系統在每天午夜檢查循環交易
2. **日期處理** - 月末日期會自動調整（如31號在2月會調整為28/29號）
3. **狀態管理** - 停用的循環交易不會執行
4. **數據持久性** - 循環交易設定會永久保存

## 測試功能

項目包含完整的測試功能，可以通過以下方式測試：

1. 運行 `RecurringTransactionDemo.tsx` 查看演示
2. 使用 `testRecurringTransactions()` 函數進行功能測試
3. 檢查控制台輸出查看詳細測試結果

## 未來擴展

- 支援結束日期設定
- 支援跳過特定日期
- 支援更複雜的循環規則
- 支援循環交易的批量管理
- 支援循環交易的統計分析
