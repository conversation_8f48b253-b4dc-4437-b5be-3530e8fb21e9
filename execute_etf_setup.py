#!/usr/bin/env python3
"""
執行美國ETF數據庫設置和數據插入
"""

import os
import sys
from supabase import create_client, Client

# Supabase配置
SUPABASE_URL = "https://yrryyapzkgrsahranzvo.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlycnl5YXB6a2dyc2FocmFuenZvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODE3MzYzNSwiZXhwIjoyMDYzNzQ5NjM1fQ.xjiXVagzKgBBYXqtRjJiXwCgbRdEW0EpFzCIlJ26SSQ"

def execute_etf_setup_manually(supabase: Client):
    """手動執行ETF設置步驟"""
    try:
        print("🔄 檢查並添加ETF字段...")

        # 1. 檢查並添加 is_etf 字段
        try:
            # 嘗試查詢現有字段
            result = supabase.table('us_stocks').select('is_etf').limit(1).execute()
            print("✅ is_etf 字段已存在")
        except:
            print("⚠️ is_etf 字段不存在，需要手動添加")
            return False

        # 2. 檢查並添加 asset_type 字段
        try:
            result = supabase.table('us_stocks').select('asset_type').limit(1).execute()
            print("✅ asset_type 字段已存在")
        except:
            print("⚠️ asset_type 字段不存在，需要手動添加")
            return False

        return True

    except Exception as e:
        print(f"❌ 字段檢查失敗: {str(e)}")
        return False

def insert_etf_data_manually(supabase: Client):
    """手動插入ETF數據"""
    try:
        print("🔄 開始插入ETF數據...")

        # ETF數據列表（前20個作為測試）
        etf_data = [
            {'symbol': 'SPY', 'name': '標普500指數ETF-SPDR', 'is_etf': True, 'asset_type': 'ETF', 'sector': 'ETF', 'is_sp500': False},
            {'symbol': 'QQQ', 'name': '納指100ETF-Invesco QQQ Trust', 'is_etf': True, 'asset_type': 'ETF', 'sector': 'ETF', 'is_sp500': False},
            {'symbol': 'IWM', 'name': '羅素2000ETF-iShares', 'is_etf': True, 'asset_type': 'ETF', 'sector': 'ETF', 'is_sp500': False},
            {'symbol': 'TQQQ', 'name': '三倍做多納指ETF-ProShares', 'is_etf': True, 'asset_type': 'ETF', 'sector': 'ETF', 'is_sp500': False},
            {'symbol': 'TLT', 'name': '20+年以上美國國債ETF-iShares', 'is_etf': True, 'asset_type': 'ETF', 'sector': 'ETF', 'is_sp500': False},
            {'symbol': 'IVV', 'name': '標普500ETF-iShares', 'is_etf': True, 'asset_type': 'ETF', 'sector': 'ETF', 'is_sp500': False},
            {'symbol': 'VOO', 'name': '標普500ETF-Vanguard', 'is_etf': True, 'asset_type': 'ETF', 'sector': 'ETF', 'is_sp500': False},
            {'symbol': 'LQD', 'name': '債券指數ETF-iShares iBoxx投資級公司債', 'is_etf': True, 'asset_type': 'ETF', 'sector': 'ETF', 'is_sp500': False},
            {'symbol': 'SOXL', 'name': '三倍做多半導體ETF-Direxion', 'is_etf': True, 'asset_type': 'ETF', 'sector': 'ETF', 'is_sp500': False},
            {'symbol': 'SQQQ', 'name': '三倍做空納指ETF-ProShares', 'is_etf': True, 'asset_type': 'ETF', 'sector': 'ETF', 'is_sp500': False},
            {'symbol': 'GLD', 'name': '黃金ETF-SPDR', 'is_etf': True, 'asset_type': 'ETF', 'sector': 'ETF', 'is_sp500': False},
            {'symbol': 'TSLL', 'name': '2倍做多TSLA ETF-Direxion', 'is_etf': True, 'asset_type': 'ETF', 'sector': 'ETF', 'is_sp500': False},
            {'symbol': 'IBIT', 'name': '比特幣ETF-iShares', 'is_etf': True, 'asset_type': 'ETF', 'sector': 'ETF', 'is_sp500': False},
            {'symbol': 'SMH', 'name': '半導體指數ETF-VanEck', 'is_etf': True, 'asset_type': 'ETF', 'sector': 'ETF', 'is_sp500': False},
            {'symbol': 'HYG', 'name': '債券指數ETF-iShares iBoxx高收益公司債', 'is_etf': True, 'asset_type': 'ETF', 'sector': 'ETF', 'is_sp500': False},
            {'symbol': 'SOXX', 'name': '半導體ETF-iShares', 'is_etf': True, 'asset_type': 'ETF', 'sector': 'ETF', 'is_sp500': False},
            {'symbol': 'FXI', 'name': '中國大盤股ETF-iShares', 'is_etf': True, 'asset_type': 'ETF', 'sector': 'ETF', 'is_sp500': False},
            {'symbol': 'XLI', 'name': '工業指數ETF-SPDR', 'is_etf': True, 'asset_type': 'ETF', 'sector': 'ETF', 'is_sp500': False},
            {'symbol': 'XLF', 'name': '金融行業ETF-SPDR', 'is_etf': True, 'asset_type': 'ETF', 'sector': 'ETF', 'is_sp500': False},
            {'symbol': 'EEM', 'name': '新興市場ETF-iShares', 'is_etf': True, 'asset_type': 'ETF', 'sector': 'ETF', 'is_sp500': False}
        ]

        success_count = 0
        error_count = 0

        for etf in etf_data:
            try:
                # 先檢查記錄是否存在
                existing = supabase.table('us_stocks').select('symbol, is_etf').eq('symbol', etf['symbol']).execute()

                if existing.data:
                    # 記錄存在，更新ETF標識
                    result = supabase.table('us_stocks').update({
                        'is_etf': True,
                        'asset_type': 'ETF',
                        'sector': 'ETF'
                    }).eq('symbol', etf['symbol']).execute()

                    if result.data:
                        print(f"✅ 更新 {etf['symbol']} 為ETF: {etf['name']}")
                        success_count += 1
                    else:
                        print(f"⚠️ 更新 {etf['symbol']} 沒有返回數據")
                        error_count += 1
                else:
                    # 記錄不存在，插入新記錄
                    result = supabase.table('us_stocks').insert(etf).execute()
                    if result.data:
                        print(f"✅ 插入 {etf['symbol']}: {etf['name']}")
                        success_count += 1
                    else:
                        print(f"⚠️ 插入 {etf['symbol']} 沒有返回數據")
                        error_count += 1

            except Exception as e:
                print(f"❌ 處理 {etf['symbol']} 失敗: {str(e)}")
                error_count += 1

        print(f"\n📊 插入結果:")
        print(f"   成功: {success_count} 個ETF")
        print(f"   失敗: {error_count} 個ETF")

        return success_count > 0

    except Exception as e:
        print(f"❌ ETF數據插入失敗: {str(e)}")
        return False

def execute_etf_setup():
    """執行ETF設置流程"""
    print("🚀 開始執行美國ETF數據庫設置...")
    print("=" * 60)

    try:
        # 創建Supabase客戶端
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        print("✅ Supabase連接成功")

        # 1. 檢查ETF字段
        success = execute_etf_setup_manually(supabase)
        if not success:
            print("❌ ETF字段檢查失敗，停止執行")
            return False

        # 2. 插入ETF數據
        success = insert_etf_data_manually(supabase)
        if not success:
            print("❌ ETF數據插入失敗")
            return False
        
        # 3. 驗證數據
        print("\n🔍 驗證ETF數據...")
        try:
            # 查詢ETF數量
            result = supabase.table('us_stocks').select('*', count='exact').eq('is_etf', True).execute()
            etf_count = result.count
            
            # 查詢總股票數量
            total_result = supabase.table('us_stocks').select('*', count='exact').execute()
            total_count = total_result.count
            
            print(f"📊 數據驗證結果:")
            print(f"   總股票數: {total_count}")
            print(f"   ETF數量: {etf_count}")
            
            if etf_count > 0:
                print("✅ ETF數據插入成功！")
                
                # 顯示前5個ETF
                sample_result = supabase.table('us_stocks').select('symbol, name').eq('is_etf', True).limit(5).execute()
                if sample_result.data:
                    print("\n📈 ETF樣本數據:")
                    for etf in sample_result.data:
                        print(f"   {etf['symbol']}: {etf['name']}")
                
                return True
            else:
                print("❌ 沒有找到ETF數據")
                return False
                
        except Exception as e:
            print(f"❌ 數據驗證失敗: {str(e)}")
            return False
        
    except Exception as e:
        print(f"❌ 執行過程中發生錯誤: {str(e)}")
        return False

def main():
    """主函數"""
    print("🇺🇸 美國ETF數據庫設置工具")
    print("=" * 60)
    
    # 檢查文件是否存在
    required_files = [
        "FinTranzo/database/us_etf_setup_fixed.sql",
        "FinTranzo/database/us_etf_data.sql"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file_path in missing_files:
            print(f"   {file_path}")
        return False
    
    # 執行設置
    success = execute_etf_setup()
    
    if success:
        print("\n🎉 ETF數據庫設置完成！")
        print("📈 現在可以在美股搜索中查詢ETF了")
        print("\n🧪 建議執行測試:")
        print("   cd FinTranzo")
        print("   npm run test:etf")
        return True
    else:
        print("\n❌ ETF數據庫設置失敗")
        print("請檢查錯誤信息並重試")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 用戶中斷執行")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 未預期的錯誤: {str(e)}")
        sys.exit(1)
